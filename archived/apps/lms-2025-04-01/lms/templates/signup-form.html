{% set custom_signup_content = frappe.db.get_single_value("LMS Settings", "custom_signup_content") %}
<form class="signup-form" role="form">
    <div class="page-card-body">
        <div class="form-group">
            <label class="form-label sr-only" for="signup_fullname"> {{ _("Full Name") }} </label>
            <input type="text" id="signup_fullname" class="form-control" placeholder="{{ _('<PERSON>') }}"
            required autofocus>
        </div>
        <div class="form-group">
            <label class="form-label sr-only" for="signup_email"> {{ _("Email") }} </label>
            <input type="email" id="signup_email" class="form-control"
            placeholder="{{ _('<EMAIL>') }}" required>
        </div>

        {% if frappe.db.get_single_value("LMS Settings", "user_category") %}
        <div class="form-group">
            <label class="form-label sr-only"> {{ _("User Category") }} </label>
            <div class="control-input-wrapper">
                <div class="control-input flex align-center">
                    <select type="text" id="user_category" data-fieldname="user_category" style="color: var(--text-light)"
                    class="input-with-feedback form-control ellipsis" data-fieldtype="Select" required>
                        <option value=""> {{ _("Category") }} </option>
                        <option value="Business Owner"> {{ _("Business Owner") }} </option>
                        <option value="Manager (Sales/Marketing/Customer)"> {{ _("Manager (Sales/Marketing/Customer)") }} </option>
                        <option value="Employee"> {{ _("Employee") }} </option>
                        <option value="Student"> {{ _("Student") }} </option>
                        <option value="Freelancer/Just looking"> {{ _("Freelancer/Just looking") }} </option>
                        <option value="Others"> {{ _("Others") }} </option>
                    </select>
                </div>
            </div>
        </div>
        {% endif %}

        {% if custom_signup_content %}
        <div class="form-group">
            <div class="checkbox">
                <label>
                    <span class="input-area">
                    <input type="checkbox" autocomplete="off" class="input-with-feedback"
                        data-fieldtype="Check" data-fieldname="terms" id="signup-terms" required>
                    </span>
                    <span class="label-area">
                    {{ custom_signup_content }}
                    </span>
                </label>
            </div>
        </div>
        {% endif %}
    </div>
    <div class="page-card-actions">
        <button class="btn btn-sm btn-primary btn-block btn-signup"
            type="submit">{{ _("Sign up") }}</button>

        <p class="text-center sign-up-message">
            <a href="#login" class="blue">{{ _("Have an account? Login") }}</a>
        </p>
    </div>
</form>

<script>
    frappe.ready(function () {
        $("#user_category").on("change", (e) => {
            style_category(e);
        });

        $(".signup-form").on("submit", (e) => {
            signup(e);
        });
    });

    const style_category = (e) => {
        let category_color = $(e.currentTarget).val() ? "var(--text-color)" : "var(--text-muted)";
        $("#user_category").css("color", category_color);
    }

    const signup = (e) => {
        e.preventDefault();
        const email = ($("#signup_email").val() || "").trim();
        const full_name = frappe.utils.xss_sanitise(($("#signup_fullname").val() || "").trim());

        if (!email || !validate_email(email) || !full_name) {
            login.set_status('{{ _("Valid email and name required") }}', 'red');
            return false;
        }

        frappe.call({
            method: "lms.lms.user.sign_up",
            args: {
                "email": email,
                "full_name": full_name,
                "verify_terms": $("#signup-terms").prop("checked") ? 1 : 0,
                "user_category": $("#user_category").length ?  $("#user_category").val() : ""
            },
            statusCode: login.login_handlers
        })
        return false;
    }
</script>
