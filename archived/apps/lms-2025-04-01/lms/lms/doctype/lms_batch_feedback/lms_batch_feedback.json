{"actions": [], "allow_rename": 1, "creation": "2025-01-07 18:53:22.279844", "doctype": "DocType", "engine": "InnoDB", "field_order": ["member", "member_name", "member_image", "batch", "column_break_swst", "content", "instructors", "value", "feedback"], "fields": [{"fieldname": "member", "fieldtype": "Link", "in_list_view": 1, "label": "Member", "options": "User", "reqd": 1}, {"fieldname": "batch", "fieldtype": "Link", "in_list_view": 1, "label": "<PERSON><PERSON>", "options": "LMS Batch", "reqd": 1}, {"fieldname": "feedback", "fieldtype": "Small Text", "in_list_view": 1, "label": "<PERSON><PERSON><PERSON>", "reqd": 1}, {"fieldname": "column_break_swst", "fieldtype": "Column Break"}, {"fieldname": "content", "fieldtype": "Rating", "label": "Content"}, {"fieldname": "instructors", "fieldtype": "Rating", "label": "Instructors"}, {"fieldname": "value", "fieldtype": "Rating", "label": "Value"}, {"fetch_from": "member.full_name", "fieldname": "member_name", "fieldtype": "Data", "label": "Member Name", "read_only": 1}, {"fetch_from": "member.user_image", "fieldname": "member_image", "fieldtype": "Attach Image", "label": "Member Image", "read_only": 1}], "grid_page_length": 50, "index_web_pages_for_search": 1, "links": [], "modified": "2025-05-21 15:58:51.667270", "modified_by": "<EMAIL>", "module": "LMS", "name": "LMS Batch <PERSON>back", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"create": 1, "email": 1, "export": 1, "if_owner": 1, "print": 1, "read": 1, "report": 1, "role": "LMS Student", "share": 1, "write": 1}], "row_format": "Dynamic", "sort_field": "creation", "sort_order": "DESC", "states": [], "title_field": "member"}