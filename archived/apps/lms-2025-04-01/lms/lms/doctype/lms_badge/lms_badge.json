{"actions": [], "allow_rename": 1, "autoname": "field:title", "creation": "2024-04-30 11:29:53.548647", "doctype": "DocType", "engine": "InnoDB", "field_order": ["enabled", "title", "description", "reference_doctype", "event", "image", "column_break_wgum", "grant_only_once", "user_field", "field_to_check", "condition"], "fields": [{"fieldname": "title", "fieldtype": "Data", "in_list_view": 1, "label": "Title", "reqd": 1, "unique": 1}, {"fieldname": "image", "fieldtype": "Attach Image", "label": "Image", "reqd": 1}, {"fieldname": "column_break_wgum", "fieldtype": "Column Break"}, {"fieldname": "reference_doctype", "fieldtype": "Link", "in_list_view": 1, "in_standard_filter": 1, "label": "Reference Document Type", "options": "DocType", "reqd": 1}, {"fieldname": "event", "fieldtype": "Select", "in_list_view": 1, "label": "Event", "options": "New\nValue Change\nAuto Assign", "reqd": 1}, {"fieldname": "condition", "fieldtype": "Code", "label": "Condition", "mandatory_depends_on": "eval:doc.event == \"Auto Assign\""}, {"depends_on": "eval:doc.event == 'Value Change'", "fieldname": "field_to_check", "fieldtype": "Select", "label": "Field To Check"}, {"default": "0", "fieldname": "grant_only_once", "fieldtype": "Check", "label": "<PERSON> only once"}, {"default": "1", "fieldname": "enabled", "fieldtype": "Check", "label": "Enabled"}, {"fieldname": "description", "fieldtype": "Small Text", "label": "Description", "reqd": 1}, {"fieldname": "user_field", "fieldtype": "Select", "label": "User Field", "reqd": 1}], "grid_page_length": 50, "index_web_pages_for_search": 1, "links": [{"link_doctype": "LMS Badge Assignment", "link_fieldname": "badge"}], "modified": "2025-07-04 13:02:19.048994", "modified_by": "Administrator", "module": "LMS", "name": "LMS Badge", "naming_rule": "By fieldname", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "All", "share": 1}], "row_format": "Dynamic", "sort_field": "creation", "sort_order": "DESC", "states": [], "title_field": "title", "track_changes": 1}