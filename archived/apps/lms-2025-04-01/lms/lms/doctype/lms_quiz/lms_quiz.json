{"actions": [], "allow_import": 1, "allow_rename": 1, "creation": "2021-06-07 10:50:17.893625", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["title", "max_attempts", "show_answers", "show_submission_history", "column_break_gaac", "total_marks", "passing_percentage", "duration", "section_break_tzbu", "shuffle_questions", "limit_questions_to", "column_break_clsh", "enable_negative_marking", "marks_to_cut", "section_break_sbjx", "questions", "section_break_3", "lesson", "column_break_5", "course"], "fields": [{"fieldname": "title", "fieldtype": "Data", "in_list_view": 1, "label": "Title", "reqd": 1, "unique": 1}, {"fieldname": "questions", "fieldtype": "Table", "label": "Questions", "options": "LMS Quiz Question"}, {"fieldname": "lesson", "fieldtype": "Link", "label": "Lesson", "options": "Course Lesson", "read_only": 1}, {"default": "0", "fieldname": "max_attempts", "fieldtype": "Int", "label": "Max Attempts"}, {"fetch_from": "lesson.course", "fieldname": "course", "fieldtype": "Link", "label": "Course", "options": "LMS Course", "read_only": 1}, {"fieldname": "column_break_5", "fieldtype": "Column Break"}, {"fieldname": "section_break_3", "fieldtype": "Section Break"}, {"default": "1", "fieldname": "show_answers", "fieldtype": "Check", "in_standard_filter": 1, "label": "Show Answers"}, {"fieldname": "column_break_gaac", "fieldtype": "Column Break"}, {"fieldname": "section_break_sbjx", "fieldtype": "Section Break"}, {"default": "0", "fieldname": "show_submission_history", "fieldtype": "Check", "label": "Show Submission History"}, {"fieldname": "passing_percentage", "fieldtype": "Int", "in_list_view": 1, "in_standard_filter": 1, "label": "Passing Percentage", "non_negative": 1, "reqd": 1}, {"default": "0", "fieldname": "total_marks", "fieldtype": "Int", "in_list_view": 1, "label": "Total Marks", "non_negative": 1, "read_only": 1, "reqd": 1}, {"default": "0", "fieldname": "shuffle_questions", "fieldtype": "Check", "label": "Shuffle Questions"}, {"depends_on": "shuffle_questions", "fieldname": "limit_questions_to", "fieldtype": "Int", "label": "Limit Questions To"}, {"fieldname": "section_break_tzbu", "fieldtype": "Section Break"}, {"fieldname": "column_break_clsh", "fieldtype": "Column Break"}, {"fieldname": "duration", "fieldtype": "Data", "label": "Duration (in minutes)"}, {"default": "0", "fieldname": "enable_negative_marking", "fieldtype": "Check", "label": "Enable Negative Marking"}, {"default": "1", "depends_on": "enable_negative_marking", "fieldname": "marks_to_cut", "fieldtype": "Int", "label": "<PERSON> To Cut"}], "grid_page_length": 50, "index_web_pages_for_search": 1, "links": [{"link_doctype": "LMS Quiz Submission", "link_fieldname": "quiz"}], "modified": "2025-06-27 20:00:15.660323", "modified_by": "<EMAIL>", "module": "LMS", "name": "LMS Quiz", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Moderator", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Course Creator", "share": 1, "write": 1}, {"email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "LMS Student", "share": 1}], "row_format": "Dynamic", "show_title_field_in_link": 1, "sort_field": "modified", "sort_order": "DESC", "states": [], "title_field": "title", "track_changes": 1}