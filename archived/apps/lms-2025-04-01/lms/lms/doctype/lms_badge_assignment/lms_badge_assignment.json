{"actions": [], "allow_rename": 1, "creation": "2024-04-30 11:58:44.096879", "doctype": "DocType", "engine": "InnoDB", "field_order": ["member", "member_name", "member_username", "member_image", "column_break_ugix", "issued_on", "badge", "badge_image", "badge_description"], "fields": [{"fieldname": "member", "fieldtype": "Link", "in_list_view": 1, "label": "Member", "options": "User", "reqd": 1}, {"fieldname": "badge", "fieldtype": "Link", "in_list_view": 1, "in_standard_filter": 1, "label": "Badge", "options": "LMS Badge", "reqd": 1}, {"fieldname": "issued_on", "fieldtype": "Date", "in_list_view": 1, "label": "Issued On", "options": "Today", "reqd": 1}, {"fetch_from": "badge.image", "fieldname": "badge_image", "fieldtype": "Attach", "label": "Badge Image", "read_only": 1, "reqd": 1}, {"fieldname": "column_break_ugix", "fieldtype": "Column Break"}, {"fetch_from": "badge.description", "fieldname": "badge_description", "fieldtype": "Small Text", "label": "Badge Description", "read_only": 1, "reqd": 1}, {"fetch_from": "member.full_name", "fieldname": "member_name", "fieldtype": "Data", "label": "Member Name", "read_only": 1}, {"fetch_from": "member.username", "fieldname": "member_username", "fieldtype": "Data", "label": "Member <PERSON><PERSON><PERSON>"}, {"fetch_from": "member.user_image", "fieldname": "member_image", "fieldtype": "Attach Image", "label": "Member Image"}], "grid_page_length": 50, "index_web_pages_for_search": 1, "links": [], "modified": "2025-07-07 20:37:22.449149", "modified_by": "<EMAIL>", "module": "LMS", "name": "LMS Badge Assignment", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Moderator", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "if_owner": 1, "print": 1, "read": 1, "report": 1, "role": "LMS Student", "share": 1, "write": 1}, {"email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "LMS Student", "share": 1}], "row_format": "Dynamic", "show_title_field_in_link": 1, "sort_field": "creation", "sort_order": "DESC", "states": [], "title_field": "member"}