{"actions": [], "allow_rename": 1, "creation": "2023-03-02 10:59:01.741349", "default_view": "List", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["title", "host", "zoom_account", "batch_name", "column_break_astv", "date", "time", "duration", "timezone", "section_break_glxh", "description", "column_break_spvt", "event", "auto_recording", "section_break_fhet", "meeting_id", "uuid", "column_break_aony", "attendees", "password", "section_break_yrpq", "start_url", "column_break_yokr", "join_url"], "fields": [{"fieldname": "title", "fieldtype": "Data", "in_list_view": 1, "label": "Title", "reqd": 1}, {"fieldname": "description", "fieldtype": "Text", "label": "Description"}, {"fieldname": "date", "fieldtype": "Date", "in_list_view": 1, "label": "Date", "reqd": 1}, {"fieldname": "duration", "fieldtype": "Int", "label": "Duration", "reqd": 1}, {"fieldname": "timezone", "fieldtype": "Data", "in_list_view": 1, "label": "Timezone", "reqd": 1}, {"fieldname": "host", "fieldtype": "Link", "in_list_view": 1, "label": "Host", "options": "User", "reqd": 1}, {"fieldname": "column_break_astv", "fieldtype": "Column Break"}, {"fieldname": "section_break_glxh", "fieldtype": "Section Break"}, {"fieldname": "column_break_spvt", "fieldtype": "Column Break"}, {"fieldname": "section_break_yrpq", "fieldtype": "Section Break"}, {"fieldname": "start_url", "fieldtype": "Small Text", "label": "Start URL", "read_only": 1}, {"fieldname": "column_break_yokr", "fieldtype": "Column Break"}, {"fieldname": "join_url", "fieldtype": "Small Text", "label": "Join <PERSON>", "read_only": 1}, {"fieldname": "password", "fieldtype": "Password", "label": "Password"}, {"fieldname": "time", "fieldtype": "Time", "label": "Time", "reqd": 1}, {"fieldname": "batch_name", "fieldtype": "Link", "label": "<PERSON><PERSON>", "options": "LMS Batch"}, {"default": "No Recording", "fieldname": "auto_recording", "fieldtype": "Select", "label": "Auto Recording", "options": "No Recording\nLocal\nCloud"}, {"fieldname": "event", "fieldtype": "Link", "label": "Event", "options": "Event", "read_only": 1}, {"fieldname": "zoom_account", "fieldtype": "Link", "label": "Zoom Account", "options": "LMS Zoom Settings", "reqd": 1}, {"fieldname": "meeting_id", "fieldtype": "Data", "label": "Meeting ID"}, {"fieldname": "attendees", "fieldtype": "Int", "label": "Attendees", "read_only": 1}, {"fieldname": "section_break_fhet", "fieldtype": "Section Break"}, {"fieldname": "uuid", "fieldtype": "Data", "label": "UUID"}, {"fieldname": "column_break_aony", "fieldtype": "Column Break"}], "grid_page_length": 50, "in_create": 1, "index_web_pages_for_search": 1, "links": [{"link_doctype": "LMS Live Class Participant", "link_fieldname": "live_class"}], "modified": "2025-05-27 14:44:35.679712", "modified_by": "<EMAIL>", "module": "LMS", "name": "LMS Live Class", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Moderator", "share": 1, "write": 1}, {"email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "LMS Student", "share": 1}], "row_format": "Dynamic", "show_title_field_in_link": 1, "sort_field": "modified", "sort_order": "DESC", "states": [], "title_field": "title", "track_changes": 1}