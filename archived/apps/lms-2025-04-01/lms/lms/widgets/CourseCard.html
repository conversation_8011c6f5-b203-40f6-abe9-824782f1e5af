{% if frappe.session.user != "Guest" %}
{% set membership = frappe.db.get_value("LMS Enrollment",
	{"member": frappe.session.user, "course": course.name},
	["name", "course", "batch_old", "current_lesson", "member_type", "progress"], as_dict=1) %}
{% set progress = frappe.utils.cint(membership.progress) %}
{% else %}
{% set membership, progress = None, None %}
{% endif %}


<div class="common-card-style course-card" data-course="{{ course.name }}" data-rating="{{ course.avg_rating }}"
    data-enrollment="{{ course.enrollment_count }}" data-creation="{{ course.creation }}">

    <div class="course-image {% if not course.image %} default-image {% endif %}"
    {% if course.image %} style="background-image: url( {{ course.image | urlencode }} );" {% endif %}>
        <div class="course-tags">
        {% for tag in get_tags(course.name) %}
            <div class="course-card-pills">{{ tag }}</div>
        {% endfor %}
        </div>
        {% if not course.image %}
            <div class="default-image-text">{{ course.title[0] }}</div>
        {% endif %}
    </div>


    <div class="course-card-content">
        <div class="course-card-meta">
            {% set lesson_count = get_lesson_count(course.name) %}
            {% if lesson_count %}
            <div class="vertically-center">
                <svg class="icon icon-md">
                    <use href="#icon-education"></use>
                </svg>
                {{ lesson_count }}
            </div>
            {% endif %}

            {% if course.status and course.status != "Approved" %}
            {% set pill_color = "gray" if course.status == "In Progress" else "orange" %}
            <div class="pull-right indicator-pill {{ pill_color }} "> {{ course.status }} </div>
            {% endif %}

            {% if course.enrollment_count %}
                <div class="vertically-center">
                    <svg class="icon  icon-md">
                        <use class="" href="#icon-users">
                    </svg>
                    {{ course.enrollment_count }}
                </div>
            {% endif %}

            {% if course.avg_rating %}
            <div class="vertically-center">
                <svg class="icon icon-md">
                    <use href="#icon-star"></use>
                </svg>
                {{ frappe.utils.flt(course.avg_rating, frappe.get_system_settings("float_precision") or 3) }}
            </div>
            {% endif %}
        </div>

        <div class="course-card-title">
            {{ course.title }}
        </div>

        <div class="short-introduction">
            {{ course.short_introduction }}
        </div>

        {% if membership and not is_instructor(course.name) and not read_only %}
        <div class="progress">
            <div class="progress-bar" role="progressbar" aria-valuenow="{{ progress }}"
            aria-valuemin="0" aria-valuemax="100" style="width:{{ progress }}%">
            <span class="sr-only"> {{ progress }} {{ _("Complete") }} </span>
            </div>
        </div>
        <div class="progress-percent">{{ progress }}% {{ _("Completed") }} </div>
        {% endif %}

        <div class="course-card-footer">

            <div class="course-card-instructors">
                {% set instructors = get_instructors("LMS Course", course.name) %}
                {% set ins_len = instructors | length %}
                {% for instructor in instructors %}
                {% if ins_len > 1 and loop.index == 1 %}
                <div class="avatar-group overlap">
                {% endif %}
                {{ widgets.Avatar(member=instructor, avatar_class="avatar-small") }}

                {% if ins_len > 1 and loop.index == ins_len %}
                </div>
                {% endif %}
                {% endfor %}
                <a class="button-links" href="{{ get_profile_url(instructors[0].username) }}">
                    <span class="course-instructor">
                    {% if ins_len == 1 %}
                        {{ instructors[0].full_name }}
                    {% elif ins_len == 2 %}
                        {{ instructors[0].full_name.split(" ")[0] }} and {{ instructors[1].full_name.split(" ")[0] }}
                    {% else %}
                        {% set suffix = "other" if ins_len - 1 == 1 else "others"  %}
                        {{ instructors[0].full_name.split(" ")[0] }} and {{ ins_len - 1 }} {{ suffix }}
                    {% endif %}
                    </span>
                </a>
            </div>

            <div class="course-price">
                {% if course.paid_course %}
                    {{ frappe.utils.fmt_money(course.course_price, 0, course.currency) }}
                {% else %}
                    {{ _("Free") }}
                {% endif %}
            </div>
        </div>

        {% if read_only %}
            <a class="stretched-link" href="/courses/{{ course.name }}"></a>
		{% else %}
            {% if progress != 100 and membership and not course.upcoming %}

				{% set lesson_index = get_lesson_index(membership.current_lesson) or "1.1" %}

				{% set query_parameter = "?batch=" + membership.batch_old if membership.batch_old else "" %}

				<a class="stretched-link" href="{{ get_lesson_url(course.name, lesson_index) }}{{ query_parameter }}"></a>

			{% else %}
				<a class="stretched-link" href="/lms/courses/{{ course.name }}"></a>
            {% endif %}
        {% endif %}
    </div>
</div>
