{"actions": [], "allow_import": 1, "allow_rename": 1, "creation": "2022-02-07 12:01:41.074418", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["job_title", "location", "country", "column_break_5", "type", "status", "disabled", "section_break_6", "company_name", "company_website", "column_break_phkm", "company_logo", "company_email_address", "company_details_section", "description"], "fields": [{"fieldname": "job_title", "fieldtype": "Data", "in_list_view": 1, "in_standard_filter": 1, "label": "Job Title", "reqd": 1}, {"fieldname": "location", "fieldtype": "Data", "in_list_view": 1, "in_standard_filter": 1, "label": "City", "reqd": 1}, {"fieldname": "column_break_5", "fieldtype": "Column Break"}, {"default": "Full Time", "fieldname": "type", "fieldtype": "Select", "label": "Type", "options": "Full Time\nPart Time\nFreelance\nContract", "reqd": 1}, {"default": "Open", "fieldname": "status", "fieldtype": "Select", "in_list_view": 1, "in_standard_filter": 1, "label": "Status", "options": "Open\nClosed"}, {"fieldname": "section_break_6", "fieldtype": "Section Break", "label": "Company Details"}, {"fieldname": "description", "fieldtype": "Text Editor", "label": "Description", "reqd": 1}, {"fieldname": "company_details_section", "fieldtype": "Section Break"}, {"fieldname": "company_name", "fieldtype": "Data", "in_list_view": 1, "in_standard_filter": 1, "label": "Company Name", "reqd": 1}, {"fieldname": "company_website", "fieldtype": "Data", "label": "Company Website", "reqd": 1}, {"fieldname": "company_logo", "fieldtype": "Attach Image", "label": "Company Logo", "reqd": 1}, {"default": "0", "fieldname": "disabled", "fieldtype": "Check", "label": "Disabled"}, {"fieldname": "company_email_address", "fieldtype": "Data", "label": "Company Email Address", "options": "Email", "reqd": 1}, {"fieldname": "column_break_phkm", "fieldtype": "Column Break"}, {"fieldname": "country", "fieldtype": "Link", "label": "Country", "options": "Country", "reqd": 1}], "grid_page_length": 50, "index_web_pages_for_search": 1, "links": [{"link_doctype": "LMS Job Application", "link_fieldname": "job"}], "make_attachments_public": 1, "modified": "2025-04-24 14:34:35.920242", "modified_by": "<EMAIL>", "module": "Job", "name": "Job Opportunity", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "select": 1, "share": 1, "write": 1}, {"email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "LMS Student", "select": 1, "share": 1}, {"create": 1, "email": 1, "export": 1, "if_owner": 1, "print": 1, "report": 1, "role": "LMS Student", "share": 1, "write": 1}], "row_format": "Dynamic", "sort_field": "modified", "sort_order": "DESC", "states": [], "title_field": "job_title"}