msgid ""
msgstr ""
"Project-Id-Version: frappe\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2025-07-04 16:04+0000\n"
"PO-Revision-Date: 2025-07-07 19:40\n"
"Last-Translator: <EMAIL>\n"
"Language-Team: Swedish\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.16.0\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Crowdin-Project: frappe\n"
"X-Crowdin-Project-ID: 639578\n"
"X-Crowdin-Language: sv-SE\n"
"X-Crowdin-File: /[frappe.lms] develop/lms/locale/main.pot\n"
"X-Crowdin-File-ID: 90\n"
"Language: sv_SE\n"

#: lms/templates/emails/assignment_submission.html:5
msgid " Please evaluate and grade it."
msgstr " Utvärdera och betygsätt."

#: frontend/src/pages/Programs.vue:39
#, python-format
msgid "% completed"
msgstr "% klar"

#. Paragraph text in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "<a href=\"/app/lms-settings/LMS%20Settings\">LMS Settings</a>"
msgstr "<a href=\"/app/lms-settings/LMS%20Settings\">LMS Inställningar</a>"

#. Paragraph text in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "<a href=\"/app/web-page/new-web-page-1\">Setup a Home Page</a>"
msgstr "<a href=\"/app/web-page/new-web-page-1\">Skapa Startsida</a>"

#. Paragraph text in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "<a href=\"/lms/courses\">Visit LMS Portal</a>"
msgstr "<a href=\"/lms/courses\">Besök Portal</a>"

#. Paragraph text in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "<a href=\"/lms/courses/new/edit\">Create a Course</a>"
msgstr "<a href=\"/lms/courses/new/edit\">Skapa Kurs</a>"

#. Paragraph text in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "<a href=\"https://docs.frappe.io/learning\">Documentation</a>"
msgstr "<a href=\"https://docs.frappe.io/learning\">Dokumentation</a>"

#: frontend/src/components/Modals/EmailTemplateModal.vue:50
msgid "<p>Dear {{ member_name }},</p>\\n\\n<p>You have been enrolled in our upcoming batch {{ batch_name }}.</p>\\n\\n<p>Thanks,</p>\\n<p>Frappe Learning</p>"
msgstr "<p>Kära {{ member_name }},</p>\\n\\n<p>Du har blivit inskriven i vår kommande grupp {{ batch_name }}.</p>\\n\\n<p>Tack så mycket,</p>\\n<p>Frappe Lärande</p>"

#. Header text in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "<span class=\"h4\"><b>Get Started</b></span>"
msgstr "<span class=\"h4\"><b>Kom Igång</b></span>"

#. Header text in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "<span class=\"h4\"><b>Master</b></span>"
msgstr "<span class=\"h4\"><b>Inställningar</b></span>"

#. Header text in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "<span style=\"font-size: 18px;\"><b>Statistics</b></span>"
msgstr "<span style=\"font-size: 18px;\"><b>Statistik</b></span>"

#: lms/lms/doctype/lms_course/lms_course.py:63
msgid "A course cannot have both paid certificate and certificate of completion."
msgstr "Kurs kan inte ha både betalt certifikat och certifikat för genomförande."

#: frontend/src/pages/CourseForm.vue:82
msgid "A one line introduction to the course that appears on the course card"
msgstr "En rad introduktion till kurs som finns på kurskortet"

#: frontend/src/pages/ProfileAbout.vue:4
msgid "About"
msgstr "Om"

#: frontend/src/pages/Batch.vue:101
msgid "About this batch"
msgstr "Om denna grupp"

#. Label of the verify_terms (Check) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Acceptance for Terms and/or Policies"
msgstr "Godkännande av Villkor och/eller Principer"

#. Option for the 'Status' (Select) field in DocType 'Cohort Join Request'
#: lms/lms/doctype/cohort_join_request/cohort_join_request.json
msgid "Accepted"
msgstr "Accepterad"

#: frontend/src/components/Settings/ZoomSettings.vue:174
msgid "Account"
msgstr "Konto"

#. Label of the account_id (Data) field in DocType 'LMS Zoom Settings'
#. Label of the account_id (Data) field in DocType 'Zoom Settings'
#: frontend/src/components/Modals/ZoomAccountModal.vue:55
#: lms/lms/doctype/lms_zoom_settings/lms_zoom_settings.json
#: lms/lms/doctype/zoom_settings/zoom_settings.json
msgid "Account ID"
msgstr "Konto ID"

#. Label of the account_name (Data) field in DocType 'LMS Zoom Settings'
#: frontend/src/components/Modals/ZoomAccountModal.vue:30
#: lms/lms/doctype/lms_zoom_settings/lms_zoom_settings.json
msgid "Account Name"
msgstr "Konto Namn"

#: frontend/src/pages/ProfileAbout.vue:17
msgid "Achievements"
msgstr "Prestationer"

#. Option for the 'Status' (Select) field in DocType 'LMS Batch Old'
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
msgid "Active"
msgstr "Aktiv"

#: frontend/src/pages/Statistics.vue:16
msgid "Active Members"
msgstr "Aktiva Medlemmar"

#: frontend/src/components/Assessments.vue:11
#: frontend/src/components/BatchCourses.vue:11
#: frontend/src/components/BatchStudents.vue:73
#: frontend/src/components/LiveClass.vue:21
#: frontend/src/components/Modals/QuizInVideo.vue:29
#: frontend/src/components/Settings/Categories.vue:43
#: frontend/src/components/Settings/Evaluators.vue:93
#: frontend/src/components/Settings/Members.vue:91
#: frontend/src/pages/ProgramForm.vue:30 frontend/src/pages/ProgramForm.vue:92
#: frontend/src/pages/ProgramForm.vue:137
msgid "Add"
msgstr "Lägg till"

#: frontend/src/components/CourseOutline.vue:18
#: frontend/src/components/CreateOutline.vue:18
#: frontend/src/components/Modals/ChapterModal.vue:5
msgid "Add Chapter"
msgstr "Lägg till Kapitel"

#: frontend/src/components/Settings/Evaluators.vue:91
msgid "Add Evaluator"
msgstr "Lägg till Utvärderare"

#: frontend/src/components/CourseOutline.vue:133
msgid "Add Lesson"
msgstr "Lägg till Lektion"

#: frontend/src/components/VideoBlock.vue:121
msgid "Add Quiz to Video"
msgstr "Lägg till Frågesport till Video"

#: frontend/src/components/Controls/ChildTable.vue:69
msgid "Add Row"
msgstr "Lägg till Rad "

#: frontend/src/pages/ProfileEvaluator.vue:89
msgid "Add Slot"
msgstr "Lägg till Tid"

#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseForm.vue:35
msgid "Add Test Case"
msgstr "Lägg till Testfall"

#: lms/templates/onboarding_header.html:26
msgid "Add a Chapter"
msgstr "Lägg till Kapitel"

#: lms/templates/onboarding_header.html:33
msgid "Add a Lesson"
msgstr "Lägg till Lektion"

#: frontend/src/components/Modals/StudentModal.vue:5
msgid "Add a Student"
msgstr "Lägga till Student"

#: frontend/src/components/AppSidebar.vue:568
msgid "Add a chapter"
msgstr "Lägg till Kapitel"

#: frontend/src/components/Modals/BatchCourseModal.vue:5
msgid "Add a course"
msgstr "Lägg till kurs"

#: frontend/src/pages/CourseForm.vue:67
msgid "Add a keyword and then press enter"
msgstr "Lägg till nyckelord och tryck sedan på Enter"

#: frontend/src/components/AppSidebar.vue:569
msgid "Add a lesson"
msgstr "Lägg till Lektion"

#: frontend/src/components/Settings/Members.vue:88
msgid "Add a new member"
msgstr "Lägg till ny medlem"

#: frontend/src/components/Modals/Question.vue:166
#: frontend/src/pages/QuizForm.vue:200
msgid "Add a new question"
msgstr "Lägg till ny fråga"

#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseModal.vue:5
msgid "Add a programming exercise to your lesson"
msgstr "Lägg till programmeringsövning i lektion"

#: frontend/src/components/AssessmentPlugin.vue:7
msgid "Add a quiz to your lesson"
msgstr "Lägg till frågesport till din lektion"

#: frontend/src/components/Modals/AssessmentModal.vue:5
msgid "Add an assessment"
msgstr "Lägg till bedömning"

#: frontend/src/components/AssessmentPlugin.vue:8
msgid "Add an assignment to your lesson"
msgstr "Lägg till uppgift till din lektion"

#: lms/lms/doctype/lms_question/lms_question.py:66
msgid "Add at least one possible answer for this question: {0}"
msgstr "Lägg till minst ett möjligt svar för denna fråga: {0}"

#: frontend/src/components/AppSidebar.vue:532
msgid "Add courses to your batch"
msgstr "Lägg till kurser i din grupp"

#: frontend/src/components/Modals/QuizInVideo.vue:5
msgid "Add quiz to this video"
msgstr "Lägg till frågesport till denna video"

#: frontend/src/components/AppSidebar.vue:511
msgid "Add students to your batch"
msgstr "Lägg till studenter i din grupp"

#: frontend/src/components/Modals/PageModal.vue:6
msgid "Add web page to sidebar"
msgstr "Lägg till webbsida i sidofältet"

#: frontend/src/components/Assignment.vue:68
msgid "Add your assignment as {0}"
msgstr "Lägg till din uppgift som {0}"

#: frontend/src/components/AppSidebar.vue:444
msgid "Add your first chapter"
msgstr "Lägg till ditt första kapitel"

#: frontend/src/components/AppSidebar.vue:460
msgid "Add your first lesson"
msgstr "Lägg till din första lektion"

#. Label of the address (Link) field in DocType 'LMS Payment'
#: frontend/src/pages/Billing.vue:64
#: lms/lms/doctype/lms_payment/lms_payment.json
msgid "Address"
msgstr "Adress"

#: frontend/src/pages/Billing.vue:74
msgid "Address Line 1"
msgstr "Adress Linje 1"

#: frontend/src/pages/Billing.vue:78
msgid "Address Line 2"
msgstr "Adress Linje 2"

#. Option for the 'Role' (Select) field in DocType 'Cohort Staff'
#. Option for the 'Required Role' (Select) field in DocType 'Cohort Web Page'
#. Option for the 'Role' (Select) field in DocType 'LMS Enrollment'
#: lms/lms/doctype/cohort_staff/cohort_staff.json
#: lms/lms/doctype/cohort_web_page/cohort_web_page.json
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
msgid "Admin"
msgstr "Administratör"

#. Name of a role
#: frontend/src/pages/Batches.vue:273 lms/lms/doctype/lms_badge/lms_badge.json
msgid "All"
msgstr "Alla"

#: frontend/src/pages/Batches.vue:26
msgid "All Batches"
msgstr "Alla Grupper"

#: frontend/src/pages/Courses.vue:26 lms/lms/widgets/BreadCrumb.html:3
msgid "All Courses"
msgstr "Alla Kurser"

#: lms/templates/quiz/quiz.html:141
msgid "All Submissions"
msgstr "Alla Godkännande"

#: lms/lms/doctype/lms_quiz/lms_quiz.py:44
msgid "All questions should have the same marks if the limit is set."
msgstr "Alla frågor ska ha samma märken om gräns är angiven."

#. Label of the allow_guest_access (Check) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Allow Guest Access"
msgstr "Tillåt Gäst Åtkomst"

#. Label of the allow_posting (Check) field in DocType 'Job Settings'
#: lms/job/doctype/job_settings/job_settings.json
msgid "Allow Job Posting From Website"
msgstr "Tillåt jobbannonsering från webbplats"

#. Label of the allow_self_enrollment (Check) field in DocType 'LMS Batch'
#: lms/lms/doctype/lms_batch/lms_batch.json
msgid "Allow Self Enrollment"
msgstr "Tillåt självregistrering"

#. Label of the allow_future (Check) field in DocType 'LMS Batch'
#: lms/lms/doctype/lms_batch/lms_batch.json
msgid "Allow accessing future dates"
msgstr "Tillåt åtkomst till framtida datum"

#: frontend/src/pages/BatchForm.vue:57
msgid "Allow self enrollment"
msgstr "Tillåt självregistrering"

#: lms/lms/user.py:34
msgid "Already Registered"
msgstr "Redan Registrerad"

#. Label of the amount (Currency) field in DocType 'LMS Batch'
#. Label of the course_price (Currency) field in DocType 'LMS Course'
#. Label of the amount (Currency) field in DocType 'LMS Payment'
#: frontend/src/pages/BatchForm.vue:265 frontend/src/pages/CourseForm.vue:245
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_course/lms_course.json
#: lms/lms/doctype/lms_payment/lms_payment.json
msgid "Amount"
msgstr "Belopp"

#. Label of the amount_usd (Currency) field in DocType 'LMS Batch'
#. Label of the amount_usd (Currency) field in DocType 'LMS Course'
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_course/lms_course.json
msgid "Amount (USD)"
msgstr "Belopp (USD)"

#: lms/lms/doctype/lms_batch/lms_batch.py:70
msgid "Amount and currency are required for paid batches."
msgstr "Belopp och valuta erfordras för betalda grupper."

#: lms/lms/doctype/lms_course/lms_course.py:74
msgid "Amount and currency are required for paid certificates."
msgstr "Belopp och valuta är obligatoriska för betalda certifikat."

#: lms/lms/doctype/lms_course/lms_course.py:71
msgid "Amount and currency are required for paid courses."
msgstr "Belopp och valuta erfordras för betalda kurser."

#. Label of the amount_with_gst (Currency) field in DocType 'LMS Payment'
#: lms/lms/doctype/lms_payment/lms_payment.json
msgid "Amount with GST"
msgstr "Belopp med Moms"

#: frontend/src/components/Modals/AnnouncementModal.vue:33
msgid "Announcement"
msgstr "Meddelande"

#: frontend/src/components/Modals/AnnouncementModal.vue:101
msgid "Announcement has been sent successfully"
msgstr "Tillkännagivande är skickad"

#: frontend/src/components/Modals/AnnouncementModal.vue:96
msgid "Announcement is required"
msgstr "Tillkännagivande erfordras"

#. Label of the answer (Text Editor) field in DocType 'LMS Assignment'
#. Label of the answer (Text Editor) field in DocType 'LMS Assignment
#. Submission'
#. Label of the answer (Code) field in DocType 'LMS Exercise'
#: frontend/src/pages/QuizSubmission.vue:60
#: lms/lms/doctype/lms_assignment/lms_assignment.json
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
#: lms/lms/doctype/lms_exercise/lms_exercise.json
msgid "Answer"
msgstr "Svara"

#: frontend/src/pages/CourseForm.vue:112 frontend/src/pages/CourseForm.vue:131
msgid "Appears on the course card in the course list"
msgstr "Visas på kurskort i kurslista"

#: frontend/src/pages/BatchForm.vue:240
msgid "Appears when the batch URL is shared on any online platform"
msgstr "Visas när grupp URL delas på valfri online plattform"

#: frontend/src/pages/BatchForm.vue:221
msgid "Appears when the batch URL is shared on socials"
msgstr "Visas när grupp URL delas på sociala medier"

#: frontend/src/pages/JobDetail.vue:51
msgid "Apply"
msgstr "Tillämpa"

#. Label of the apply_gst (Check) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Apply GST for India"
msgstr "Tillämpa GST för Indien"

#. Label of the apply_rounding (Check) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Apply Rounding on Equivalent"
msgstr "Tillämpa avrundning på ekvivalent"

#: frontend/src/components/Modals/JobApplicationModal.vue:6
msgid "Apply for this job"
msgstr "Ansök till detta jobb"

#. Option for the 'Status' (Select) field in DocType 'Invite Request'
#. Option for the 'Status' (Select) field in DocType 'LMS Course'
#. Option for the 'Status' (Select) field in DocType 'LMS Mentor Request'
#: lms/lms/doctype/invite_request/invite_request.json
#: lms/lms/doctype/lms_course/lms_course.json
#: lms/lms/doctype/lms_mentor_request/lms_mentor_request.json
msgid "Approved"
msgstr "Godkänd"

#: frontend/src/components/Apps.vue:13
msgid "Apps"
msgstr "Appar"

#: frontend/src/pages/Batches.vue:283
msgid "Archived"
msgstr "Arkiverad"

#: frontend/src/components/UpcomingEvaluations.vue:172
msgid "Are you sure you want to cancel this evaluation? This action cannot be undone."
msgstr "Är du säker på att du vill avbryta denna utvärdering? Denna åtgärd kan inte ångras."

#: frontend/src/components/UserDropdown.vue:175
msgid "Are you sure you want to login to your Frappe Cloud dashboard?"
msgstr "Är du säker på att du vill logga in på din Översikt Panel i Frappe Cloud?"

#. Label of the assessment_tab (Tab Break) field in DocType 'LMS Batch'
#. Label of the assessment (Table) field in DocType 'LMS Batch'
#: frontend/src/components/Modals/AssessmentModal.vue:27
#: frontend/src/components/Modals/BatchStudentProgress.vue:41
#: lms/lms/doctype/lms_batch/lms_batch.json lms/templates/assessments.html:11
msgid "Assessment"
msgstr "Bedömning"

#. Label of the assessment_name (Dynamic Link) field in DocType 'LMS
#. Assessment'
#: lms/lms/doctype/lms_assessment/lms_assessment.json
msgid "Assessment Name"
msgstr "Bedömning Namn"

#. Label of the assessment_type (Link) field in DocType 'LMS Assessment'
#: lms/lms/doctype/lms_assessment/lms_assessment.json
msgid "Assessment Type"
msgstr "Bedömning Typ"

#: frontend/src/components/Modals/AssessmentModal.vue:91
msgid "Assessment added successfully"
msgstr "Bedömning tillagd"

#: lms/lms/doctype/lms_batch/lms_batch.py:80
msgid "Assessment {0} has already been added to this batch."
msgstr "Bedömning {0} har redan lagts till i denna grupp."

#. Label of the show_assessments (Check) field in DocType 'LMS Settings'
#: frontend/src/components/AppSidebar.vue:581
#: frontend/src/components/Assessments.vue:5
#: frontend/src/components/BatchStudents.vue:32
#: lms/lms/doctype/lms_settings/lms_settings.json
#: lms/templates/assessments.html:3
msgid "Assessments"
msgstr "Bedömningar"

#: lms/lms/doctype/lms_badge/lms_badge.js:50
msgid "Assign"
msgstr "Tilldela"

#. Label of the section_break_16 (Section Break) field in DocType 'Course
#. Lesson'
#. Label of the assignment (Link) field in DocType 'LMS Assignment Submission'
#: frontend/src/components/Assessments.vue:245
#: frontend/src/pages/AssignmentSubmissionList.vue:12
#: frontend/src/utils/assignment.js:24
#: lms/lms/doctype/course_lesson/course_lesson.json
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
#: lms/templates/assignment.html:3
msgid "Assignment"
msgstr "Tilldelning"

#. Label of the assignment_attachment (Attach) field in DocType 'LMS Assignment
#. Submission'
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
msgid "Assignment Attachment"
msgstr "Uppgift Bilaga"

#: frontend/src/pages/AssignmentSubmissionList.vue:222
msgid "Assignment Submissions"
msgstr "Uppgift Inlämningar"

#. Label of the assignment_title (Data) field in DocType 'LMS Assignment
#. Submission'
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
msgid "Assignment Title"
msgstr "Uppgift Benämning"

#: frontend/src/components/Modals/AssignmentForm.vue:125
msgid "Assignment created successfully"
msgstr "Uppgift skapad"

#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.py:24
msgid "Assignment for Lesson {0} by {1} already exists."
msgstr "Uppgift för Lektion {0} av {1} finns redan."

#: frontend/src/components/Assignment.vue:356
msgid "Assignment submitted successfully"
msgstr "Uppgift inlämnad"

#: frontend/src/components/Modals/AssignmentForm.vue:138
msgid "Assignment updated successfully"
msgstr "Uppgift uppdaterad"

#. Description of the 'Question' (Small Text) field in DocType 'Course Lesson'
#: lms/lms/doctype/course_lesson/course_lesson.json
msgid "Assignment will appear at the bottom of the lesson."
msgstr "Uppgift kommer att visas längst ner i lektion."

#: frontend/src/components/AppSidebar.vue:585
#: frontend/src/pages/Assignments.vue:208 lms/www/lms.py:273
msgid "Assignments"
msgstr "Uppgifter"

#: lms/lms/doctype/lms_question/lms_question.py:43
msgid "At least one option must be correct for this question."
msgstr "Minst ett alternativ måste vara korrekt för denna fråga."

#: lms/lms/doctype/lms_programming_exercise/lms_programming_exercise.py:15
msgid "At least one test case is required for the programming exercise."
msgstr "Minst ett testfall erfordras för programmeringsövning."

#: frontend/src/components/Modals/LiveClassAttendance.vue:5
msgid "Attendance for Class - {0}"
msgstr "Närvaro för Lektion - {0}"

#: frontend/src/components/Modals/LiveClassAttendance.vue:24
msgid "Attended for"
msgstr "Närvarar för"

#. Label of the attendees (Int) field in DocType 'LMS Live Class'
#: lms/lms/doctype/lms_live_class/lms_live_class.json
msgid "Attendees"
msgstr "Deltagare"

#. Label of the attire (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Attire Preference"
msgstr "Klädsel Preferens"

#: frontend/src/pages/ProfileEvaluator.vue:137
msgid "Authorize Google Calendar Access"
msgstr "Auktorisera Google Kalender Åtkomst"

#. Option for the 'Event' (Select) field in DocType 'LMS Badge'
#: lms/lms/doctype/lms_badge/lms_badge.json
msgid "Auto Assign"
msgstr "Automatiskt Tilldela"

#. Label of the auto_recording (Select) field in DocType 'LMS Live Class'
#: frontend/src/components/Modals/LiveClassModal.vue:73
#: lms/lms/doctype/lms_live_class/lms_live_class.json
msgid "Auto Recording"
msgstr "Automatisk Inspelning"

#: frontend/src/pages/ProfileEvaluator.vue:224
msgid "Availability updated successfully"
msgstr "Tillgänglighet uppdaterad"

#: frontend/src/components/BatchFeedback.vue:43
msgid "Average Feedback Received"
msgstr "Genomsnittlig Återkoppling Mottagen"

#: frontend/src/components/Modals/CourseProgressSummary.vue:96
msgid "Average Progress %"
msgstr "Genomsnittlig Framsteg %"

#: frontend/src/components/CourseCard.vue:55
#: frontend/src/pages/CourseDetail.vue:20
msgid "Average Rating"
msgstr "Genomsnittlig Betyg"

#: frontend/src/components/Modals/VideoStatistics.vue:65
msgid "Average Watch Time (seconds)"
msgstr "Genomsnittlig Visningstid (sekunder)"

#: frontend/src/pages/Lesson.vue:151
msgid "Back to Course"
msgstr "Tillbaka till Kurs"

#. Label of the badge (Link) field in DocType 'LMS Badge Assignment'
#: lms/lms/doctype/lms_badge_assignment/lms_badge_assignment.json
msgid "Badge"
msgstr "Emblem"

#. Label of the badge_description (Small Text) field in DocType 'LMS Badge
#. Assignment'
#: lms/lms/doctype/lms_badge_assignment/lms_badge_assignment.json
msgid "Badge Description"
msgstr "Emblem Beskrivning"

#. Label of the badge_image (Attach) field in DocType 'LMS Badge Assignment'
#: lms/lms/doctype/lms_badge_assignment/lms_badge_assignment.json
msgid "Badge Image"
msgstr "Emblem Bild"

#. Label of the batch (Link) field in DocType 'LMS Batch Enrollment'
#. Label of the batch (Link) field in DocType 'LMS Batch Feedback'
#. Label of the batch_name (Link) field in DocType 'LMS Certificate'
#. Label of the batch_name (Link) field in DocType 'LMS Certificate Request'
#. Label of the batch_name (Link) field in DocType 'LMS Live Class'
#: frontend/src/components/Modals/Event.vue:32
#: lms/lms/doctype/lms_batch_enrollment/lms_batch_enrollment.json
#: lms/lms/doctype/lms_batch_feedback/lms_batch_feedback.json
#: lms/lms/doctype/lms_certificate/lms_certificate.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
#: lms/lms/doctype/lms_live_class/lms_live_class.json
msgid "Batch"
msgstr "Parti"

#. Label of the batch_confirmation_template (Link) field in DocType 'LMS
#. Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Batch Confirmation Template"
msgstr "Grupp Bekräftelse Mall"

#. Name of a DocType
#: lms/lms/doctype/batch_course/batch_course.json
msgid "Batch Course"
msgstr "Grupp Kurs"

#. Label of the section_break_5 (Section Break) field in DocType 'LMS Batch
#. Old'
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
msgid "Batch Description"
msgstr "Parti Beskrivning"

#. Label of the batch_details (Text Editor) field in DocType 'LMS Batch'
#: frontend/src/pages/BatchForm.vue:126
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/templates/emails/batch_confirmation.html:26
msgid "Batch Details"
msgstr "Parti Detaljer"

#. Label of the batch_details_raw (HTML Editor) field in DocType 'LMS Batch'
#: lms/lms/doctype/lms_batch/lms_batch.json
msgid "Batch Details Raw"
msgstr "Grupp Detaljer"

#: frontend/src/components/Modals/EmailTemplateModal.vue:28
msgid "Batch Enrollment Confirmation"
msgstr "Grupp Bekräftelse Inskrivning"

#. Name of a role
#: lms/lms/doctype/course_evaluator/course_evaluator.json
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_category/lms_category.json
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
#: lms/lms/doctype/lms_programming_exercise/lms_programming_exercise.json
#: lms/lms/doctype/lms_programming_exercise_submission/lms_programming_exercise_submission.json
#: lms/lms/doctype/lms_zoom_settings/lms_zoom_settings.json
msgid "Batch Evaluator"
msgstr "Grupp Utvärderare"

#. Label of the batch_name (Link) field in DocType 'LMS Certificate Evaluation'
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
msgid "Batch Name"
msgstr "Grupp Namn"

#. Label of the batch_old (Link) field in DocType 'Exercise Latest Submission'
#. Label of the batch_old (Link) field in DocType 'Exercise Submission'
#. Label of the batch_old (Link) field in DocType 'LMS Enrollment'
#: lms/lms/doctype/exercise_latest_submission/exercise_latest_submission.json
#: lms/lms/doctype/exercise_submission/exercise_submission.json
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
msgid "Batch Old"
msgstr "Grupp Gammal"

#. Label of the section_break_7 (Section Break) field in DocType 'LMS Batch
#. Old'
#. Label of the section_break_szgq (Section Break) field in DocType 'LMS
#. Settings'
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Batch Settings"
msgstr "Grupp Inställningar"

#: lms/templates/emails/batch_confirmation.html:11
msgid "Batch Start Date:"
msgstr "Grupp Start Datum:"

#: frontend/src/components/BatchStudents.vue:40
msgid "Batch Summary"
msgstr "Gruppöversikt"

#. Label of the batch_title (Data) field in DocType 'LMS Certificate'
#. Label of the batch_title (Data) field in DocType 'LMS Certificate Request'
#: lms/lms/doctype/lms_certificate/lms_certificate.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
msgid "Batch Title"
msgstr "Grupp Benämning"

#: lms/lms/doctype/lms_batch/lms_batch.py:41
msgid "Batch end date cannot be before the batch start date"
msgstr "Grupp slutdatum får inte vara före grupp startdatum"

#: lms/lms/api.py:245
msgid "Batch has already started."
msgstr "Grupp redan startad."

#: lms/lms/api.py:240
msgid "Batch is sold out."
msgstr "Gruppen är slutsåld."

#: lms/lms/doctype/lms_batch/lms_batch.py:46
msgid "Batch start time cannot be greater than or equal to end time."
msgstr "Gruppens starttid kan inte vara senare än eller lika med sluttid."

#: lms/templates/emails/batch_start_reminder.html:10
msgid "Batch:"
msgstr "Parti:"

#. Label of the batches (Check) field in DocType 'LMS Settings'
#: frontend/src/pages/Batches.vue:299 frontend/src/pages/Batches.vue:306
#: lms/lms/doctype/lms_settings/lms_settings.json lms/www/lms.py:122
msgid "Batches"
msgstr "Grupper"

#. Label of the begin_date (Date) field in DocType 'Cohort'
#: lms/lms/doctype/cohort/cohort.json
msgid "Begin Date"
msgstr "Start Datum"

#: lms/templates/emails/batch_confirmation.html:33
#: lms/templates/emails/batch_start_reminder.html:31
#: lms/templates/emails/certification.html:20
#: lms/templates/emails/live_class_reminder.html:28
msgid "Best Regards"
msgstr "Vänliga hälsningar,"

#. Label of the billing_details_section (Section Break) field in DocType 'LMS
#. Payment'
#: frontend/src/pages/Billing.vue:8 frontend/src/pages/Billing.vue:357
#: lms/lms/doctype/lms_payment/lms_payment.json
msgid "Billing Details"
msgstr "Faktura Detaljer"

#. Label of the billing_name (Data) field in DocType 'LMS Payment'
#: frontend/src/pages/Billing.vue:70
#: lms/lms/doctype/lms_payment/lms_payment.json
msgid "Billing Name"
msgstr "Faktura Namn"

#: frontend/src/components/Modals/EditProfile.vue:75
msgid "Bio"
msgstr "Resume"

#. Label of the body (Markdown Editor) field in DocType 'Course Lesson'
#: lms/lms/doctype/course_lesson/course_lesson.json
msgid "Body"
msgstr "Huvudtext"

#. Option for the 'Collaboration Preference' (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Both Individual and Team Work"
msgstr "Både Individuellt och Teamarbete"

#. Label of the branch (Data) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Branch"
msgstr "Bransch"

#. Option for the 'User Category' (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json lms/templates/signup-form.html:23
msgid "Business Owner"
msgstr "Affärsägare"

#: frontend/src/components/CourseCardOverlay.vue:54
msgid "Buy this course"
msgstr "Köp denna kurs"

#: lms/templates/emails/lms_message.html:11
msgid "By"
msgstr "Av"

#. Option for the 'Grade Type' (Select) field in DocType 'Education Detail'
#: lms/lms/doctype/education_detail/education_detail.json
msgid "CGPA/4"
msgstr "CGPA/4"

#: frontend/src/components/UpcomingEvaluations.vue:57
#: frontend/src/components/UpcomingEvaluations.vue:177
msgid "Cancel"
msgstr "Annullera"

#: frontend/src/components/UpcomingEvaluations.vue:171
msgid "Cancel this evaluation?"
msgstr "Avbryt denna utvärdering?"

#. Option for the 'Status' (Select) field in DocType 'Cohort'
#. Option for the 'Stage' (Select) field in DocType 'LMS Batch Old'
#. Option for the 'Status' (Select) field in DocType 'LMS Certificate Request'
#: lms/lms/doctype/cohort/cohort.json
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
msgid "Cancelled"
msgstr "Annullerad"

#. Label of the carrer_preference_details (Section Break) field in DocType
#. 'User'
#: lms/fixtures/custom_field.json
msgid "Career Preference Details"
msgstr "Karriär Preferens Detaljer"

#. Option for the 'Attire Preference' (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Casual Wear"
msgstr "Fritidskläder"

#. Label of the category (Link) field in DocType 'LMS Batch'
#. Label of the category (Data) field in DocType 'LMS Category'
#. Label of the category (Link) field in DocType 'LMS Course'
#: frontend/src/pages/BatchForm.vue:192 frontend/src/pages/Batches.vue:55
#: frontend/src/pages/CertifiedParticipants.vue:35
#: frontend/src/pages/CourseForm.vue:36 frontend/src/pages/Courses.vue:51
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_category/lms_category.json
#: lms/lms/doctype/lms_course/lms_course.json lms/templates/signup-form.html:22
msgid "Category"
msgstr "Kategori"

#: frontend/src/components/Settings/Categories.vue:39
msgid "Category Name"
msgstr "Kategori Namn"

#: frontend/src/components/Settings/Categories.vue:133
msgid "Category added successfully"
msgstr "Kategori tillagd"

#: frontend/src/components/Settings/Categories.vue:193
msgid "Category deleted successfully"
msgstr "Kategori borttagen"

#: frontend/src/components/Settings/Categories.vue:173
msgid "Category updated successfully"
msgstr "Kategori uppdaterad"

#. Label of the certificate (Link) field in DocType 'LMS Enrollment'
#. Label of a shortcut in the LMS Workspace
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
#: lms/lms/workspace/lms/lms.json
msgid "Certificate"
msgstr "Certifikat"

#. Label of the certification_template (Link) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Certificate Email Template"
msgstr "E-post Mall för Certifikat"

#: lms/templates/emails/certification.html:13
msgid "Certificate Link"
msgstr "Cerifikat Länk"

#: frontend/src/components/CourseCardOverlay.vue:156
msgid "Certificate of Completion"
msgstr "Certifikat för Genomförande"

#: frontend/src/components/Modals/Event.vue:317
msgid "Certificate saved successfully"
msgstr "Certifikat sparad"

#: frontend/src/pages/ProfileCertificates.vue:4
msgid "Certificates"
msgstr "Certifikat"

#: frontend/src/components/Modals/BulkCertificates.vue:120
msgid "Certificates generated successfully"
msgstr "Certifikat genererade"

#. Label of the certification (Table) field in DocType 'User'
#. Name of a DocType
#. Label of the certification (Check) field in DocType 'LMS Batch'
#. Label of the certification_section (Section Break) field in DocType 'LMS
#. Enrollment'
#. Label of a Card Break in the LMS Workspace
#. Label of a Link in the LMS Workspace
#: frontend/src/components/AppSidebar.vue:589
#: frontend/src/components/CourseCard.vue:115
#: frontend/src/components/Modals/Event.vue:381
#: frontend/src/pages/BatchForm.vue:62 frontend/src/pages/Batches.vue:38
#: frontend/src/pages/CourseCertification.vue:10
#: frontend/src/pages/CourseCertification.vue:135
#: frontend/src/pages/Courses.vue:34 lms/fixtures/custom_field.json
#: lms/lms/doctype/certification/certification.json
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
#: lms/lms/workspace/lms/lms.json
msgid "Certification"
msgstr "Certifiering"

#. Label of the certification_details (Section Break) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Certification Details"
msgstr "Certifiering Detaljer"

#. Label of the certification_name (Data) field in DocType 'Certification'
#: lms/lms/doctype/certification/certification.json
msgid "Certification Name"
msgstr "Certifiering Namn"

#: frontend/src/components/BatchStudents.vue:17
msgid "Certified"
msgstr "Certifierad"

#. Label of the certified_members (Check) field in DocType 'LMS Settings'
#: frontend/src/pages/CertifiedParticipants.vue:182
#: frontend/src/pages/CertifiedParticipants.vue:189
#: frontend/src/pages/Statistics.vue:40
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Certified Members"
msgstr "Certifierade Medlemmar"

#. Label of the certified_participants (Check) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json lms/www/lms.py:302
msgid "Certified Participants"
msgstr "Certifierade Deltagare"

#: lms/templates/assignment.html:13
msgid "Change"
msgstr "Ändra"

#: frontend/src/components/Assignment.vue:342
msgid "Changes saved successfully"
msgstr "Ändringar sparade"

#. Label of the chapter (Link) field in DocType 'Chapter Reference'
#. Label of the chapter (Link) field in DocType 'LMS Course Progress'
#. Label of the chapter (Link) field in DocType 'LMS Video Watch Duration'
#. Label of a Link in the LMS Workspace
#: lms/lms/doctype/chapter_reference/chapter_reference.json
#: lms/lms/doctype/lms_course_progress/lms_course_progress.json
#: lms/lms/doctype/lms_video_watch_duration/lms_video_watch_duration.json
#: lms/lms/workspace/lms/lms.json
msgid "Chapter"
msgstr "Kapitel"

#. Name of a DocType
#: lms/lms/doctype/chapter_reference/chapter_reference.json
msgid "Chapter Reference"
msgstr "Kapitel Referens"

#: frontend/src/components/Modals/ChapterModal.vue:154
msgid "Chapter added successfully"
msgstr "Kapitel tillagt"

#: frontend/src/components/CourseOutline.vue:299
msgid "Chapter deleted successfully"
msgstr "Kapitel raderad"

#: frontend/src/components/Modals/ChapterModal.vue:196
msgid "Chapter updated successfully"
msgstr "Kapitel uppdaterad"

#. Label of the chapters (Table) field in DocType 'LMS Course'
#: lms/lms/doctype/lms_course/lms_course.json
msgid "Chapters"
msgstr "Kapitel"

#: frontend/src/components/Quiz.vue:229 lms/templates/quiz/quiz.html:120
msgid "Check"
msgstr "Check"

#: frontend/src/pages/ProgrammingExercises/ProgrammingExercises.vue:16
msgid "Check All Submissions"
msgstr "Kontrollera Alla Inlämningar"

#: lms/templates/emails/mention_template.html:10
msgid "Check Discussion"
msgstr "Kolla Diskussion"

#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseForm.vue:97
msgid "Check Submission"
msgstr "Kontrollera Inlämning"

#: frontend/src/components/Modals/AssignmentForm.vue:55
#: frontend/src/pages/QuizForm.vue:39
msgid "Check Submissions"
msgstr "Kontrollera Inlämningar"

#: lms/templates/certificates_section.html:24
msgid "Check out the {0} to know more about certification."
msgstr "Kolla in {0} för att få veta mer om certifiering."

#: frontend/src/components/NoPermission.vue:19
msgid "Checkout Courses"
msgstr "Kolla Kurser"

#. Option for the 'Type' (Select) field in DocType 'LMS Question'
#. Option for the 'Type' (Select) field in DocType 'LMS Quiz Question'
#: lms/lms/doctype/lms_question/lms_question.json
#: lms/lms/doctype/lms_quiz_question/lms_quiz_question.json
msgid "Choices"
msgstr "Alternativ"

#: frontend/src/components/Quiz.vue:644 lms/templates/quiz/quiz.html:53
msgid "Choose all answers that apply"
msgstr "Välj alla svar som gäller"

#: frontend/src/components/Modals/Question.vue:19
msgid "Choose an existing question"
msgstr "Välj befintlig fråga"

#: frontend/src/components/Controls/IconPicker.vue:27
msgid "Choose an icon"
msgstr "Välj Ikon"

#: frontend/src/components/Quiz.vue:645 lms/templates/quiz/quiz.html:53
msgid "Choose one answer"
msgstr "Välj ett svar"

#. Label of the city (Data) field in DocType 'User'
#. Label of the location (Data) field in DocType 'Job Opportunity'
#: frontend/src/pages/Billing.vue:81 frontend/src/pages/JobForm.vue:34
#: lms/fixtures/custom_field.json
#: lms/job/doctype/job_opportunity/job_opportunity.json
msgid "City"
msgstr "Ort"

#: lms/templates/emails/live_class_reminder.html:10
msgid "Class:"
msgstr "Klass:"

#: frontend/src/components/Controls/Link.vue:50
msgid "Clear"
msgstr "Rensa"

#. Option for the 'Role Preference' (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Clearly Defined Role"
msgstr "Tydligt Definierad Roll"

#: frontend/src/components/BatchFeedback.vue:10
msgid "Click here"
msgstr "Klicka här"

#. Label of the client_id (Data) field in DocType 'LMS Zoom Settings'
#. Label of the client_id (Data) field in DocType 'Zoom Settings'
#: frontend/src/components/Modals/ZoomAccountModal.vue:36
#: lms/lms/doctype/lms_zoom_settings/lms_zoom_settings.json
#: lms/lms/doctype/zoom_settings/zoom_settings.json
msgid "Client ID"
msgstr "Klient ID"

#. Label of the client_secret (Password) field in DocType 'LMS Zoom Settings'
#. Label of the client_secret (Password) field in DocType 'Zoom Settings'
#: frontend/src/components/Modals/ZoomAccountModal.vue:49
#: lms/lms/doctype/lms_zoom_settings/lms_zoom_settings.json
#: lms/lms/doctype/zoom_settings/zoom_settings.json
msgid "Client Secret"
msgstr "Klient Hemlighet"

#: frontend/src/components/Settings/Categories.vue:27
msgid "Close"
msgstr "Stäng"

#. Option for the 'Status' (Select) field in DocType 'Job Opportunity'
#. Option for the 'Membership' (Select) field in DocType 'LMS Batch Old'
#: lms/job/doctype/job_opportunity/job_opportunity.json
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
msgid "Closed"
msgstr "Stängd"

#. Option for the 'Auto Recording' (Select) field in DocType 'LMS Live Class'
#: lms/lms/doctype/lms_live_class/lms_live_class.json
msgid "Cloud"
msgstr "Moln"

#. Label of the code (Code) field in DocType 'LMS Exercise'
#. Label of the code (Code) field in DocType 'LMS Programming Exercise
#. Submission'
#: lms/lms/doctype/lms_exercise/lms_exercise.json
#: lms/lms/doctype/lms_programming_exercise_submission/lms_programming_exercise_submission.json
msgid "Code"
msgstr "Kod"

#. Name of a DocType
#. Label of the cohort (Link) field in DocType 'Cohort Join Request'
#. Label of the cohort (Link) field in DocType 'Cohort Mentor'
#. Label of the cohort (Link) field in DocType 'Cohort Staff'
#. Label of the cohort (Link) field in DocType 'Cohort Subgroup'
#. Option for the 'Scope' (Select) field in DocType 'Cohort Web Page'
#. Label of the cohort (Link) field in DocType 'LMS Enrollment'
#: lms/lms/doctype/cohort/cohort.json
#: lms/lms/doctype/cohort_join_request/cohort_join_request.json
#: lms/lms/doctype/cohort_mentor/cohort_mentor.json
#: lms/lms/doctype/cohort_staff/cohort_staff.json
#: lms/lms/doctype/cohort_subgroup/cohort_subgroup.json
#: lms/lms/doctype/cohort_web_page/cohort_web_page.json
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
msgid "Cohort"
msgstr "Grupp"

#. Name of a DocType
#: lms/lms/doctype/cohort_join_request/cohort_join_request.json
msgid "Cohort Join Request"
msgstr "Grupp Anslutning Begäran"

#. Name of a DocType
#: lms/lms/doctype/cohort_mentor/cohort_mentor.json
msgid "Cohort Mentor"
msgstr "Grupp Mentor"

#. Name of a DocType
#: lms/lms/doctype/cohort_staff/cohort_staff.json
msgid "Cohort Staff"
msgstr "Grupp Personal"

#. Name of a DocType
#: lms/lms/doctype/cohort_subgroup/cohort_subgroup.json
msgid "Cohort Subgroup"
msgstr "Undergrupp"

#. Name of a DocType
#: lms/lms/doctype/cohort_web_page/cohort_web_page.json
msgid "Cohort Web Page"
msgstr "Grupp Webbsida"

#. Label of the collaboration (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Collaboration Preference"
msgstr "Samarbetspreferens"

#: frontend/src/components/AppSidebar.vue:142
msgid "Collapse"
msgstr "Fäll In"

#. Label of the college (Data) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "College Name"
msgstr "Skola Namn"

#. Label of the color (Color) field in DocType 'LMS Timetable Legend'
#: lms/lms/doctype/lms_timetable_legend/lms_timetable_legend.json
msgid "Color"
msgstr "Färg"

#: frontend/src/pages/BatchForm.vue:293 frontend/src/pages/CourseForm.vue:283
msgid "Comma separated keywords for SEO"
msgstr "Kommaseparerade nyckelord för SEO"

#. Label of the comments (Small Text) field in DocType 'Exercise Latest
#. Submission'
#. Label of the comments (Small Text) field in DocType 'Exercise Submission'
#. Label of the comments (Text Editor) field in DocType 'LMS Assignment
#. Submission'
#. Label of the comments (Small Text) field in DocType 'LMS Mentor Request'
#: frontend/src/components/Assignment.vue:164
#: lms/lms/doctype/exercise_latest_submission/exercise_latest_submission.json
#: lms/lms/doctype/exercise_submission/exercise_submission.json
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
#: lms/lms/doctype/lms_mentor_request/lms_mentor_request.json
msgid "Comments"
msgstr "Kommentarer"

#: frontend/src/components/Assignment.vue:142
msgid "Comments by Evaluator"
msgstr "Kommentarer av Utvärderare"

#. Description of the 'Meta Keywords' (Small Text) field in DocType 'LMS
#. Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Common keywords that will be used for all pages"
msgstr "Gemensamma sökord som ska användas för alla sidor"

#. Label of the company (Data) field in DocType 'LMS Job Application'
#. Label of the company (Data) field in DocType 'Work Experience'
#: lms/job/doctype/lms_job_application/lms_job_application.json
#: lms/lms/doctype/work_experience/work_experience.json
msgid "Company"
msgstr "Bolag"

#. Label of the section_break_6 (Section Break) field in DocType 'Job
#. Opportunity'
#: frontend/src/pages/JobForm.vue:56
#: lms/job/doctype/job_opportunity/job_opportunity.json
msgid "Company Details"
msgstr "Bolag Detaljer"

#. Label of the company_email_address (Data) field in DocType 'Job Opportunity'
#: frontend/src/pages/JobForm.vue:75
#: lms/job/doctype/job_opportunity/job_opportunity.json
msgid "Company Email Address"
msgstr "Bolag E-post Adress"

#. Label of the company_logo (Attach Image) field in DocType 'Job Opportunity'
#: frontend/src/pages/JobForm.vue:80
#: lms/job/doctype/job_opportunity/job_opportunity.json
msgid "Company Logo"
msgstr "Bolag Logotyp"

#. Label of the company_name (Data) field in DocType 'Job Opportunity'
#: frontend/src/pages/JobForm.vue:62
#: lms/job/doctype/job_opportunity/job_opportunity.json
msgid "Company Name"
msgstr "Bolag Namn"

#. Label of the company_type (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Company Type"
msgstr "Bolagstyp"

#. Label of the company_website (Data) field in DocType 'Job Opportunity'
#: frontend/src/pages/JobForm.vue:68
#: lms/job/doctype/job_opportunity/job_opportunity.json
msgid "Company Website"
msgstr "Bolag Webbplats"

#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseSubmission.vue:69
msgid "Compiler Message"
msgstr "Kompilator Meddelande"

#. Option for the 'Status' (Select) field in DocType 'LMS Course Progress'
#: frontend/src/components/Modals/BatchStudentProgress.vue:24
#: lms/lms/doctype/lms_course_progress/lms_course_progress.json
#: lms/lms/widgets/CourseCard.html:75 lms/templates/reviews.html:48
msgid "Complete"
msgstr "Klar"

#: lms/templates/emails/lms_invite_request_approved.html:7
msgid "Complete Sign Up"
msgstr "Slutför Registrering"

#: lms/templates/emails/payment_reminder.html:15
msgid "Complete Your Enrollment"
msgstr "Slutför din Registrering"

#: lms/lms/doctype/lms_payment/lms_payment.py:73
msgid "Complete Your Enrollment - Don't miss out!"
msgstr "Slutför din Registrering - Missa inte!"

#: frontend/src/components/VideoBlock.vue:144
msgid "Complete the upcoming quiz to continue watching the video. The quiz will open in {0} {1}."
msgstr "Fyll i nästa frågeformulär för att fortsätta titta på video. Frågesport kommer att öppnas om {0} {1}."

#. Option for the 'Status' (Select) field in DocType 'Cohort'
#. Option for the 'Stage' (Select) field in DocType 'LMS Batch Old'
#. Option for the 'Status' (Select) field in DocType 'LMS Certificate Request'
#: lms/lms/doctype/cohort/cohort.json
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
#: lms/lms/widgets/CourseCard.html:78
msgid "Completed"
msgstr "Klar"

#. Label of the enable_certification (Check) field in DocType 'LMS Course'
#: frontend/src/pages/CourseForm.vue:232
#: lms/lms/doctype/lms_course/lms_course.json
msgid "Completion Certificate"
msgstr "Kompletterande Certifikat"

#. Label of the condition (Code) field in DocType 'LMS Badge'
#: lms/lms/doctype/lms_badge/lms_badge.json
msgid "Condition"
msgstr "Villkor"

#: lms/lms/doctype/lms_badge/lms_badge.py:16
msgid "Condition must be in valid JSON format."
msgstr "Villkoret måste vara i giltigt JSON format."

#: lms/lms/doctype/lms_badge/lms_badge.py:21
msgid "Condition must be valid python code."
msgstr "Villkoret måste vara giltig python kod."

#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.js:7
msgid "Conduct Evaluation"
msgstr "Genomför Utvärdering"

#: frontend/src/pages/BatchForm.vue:141
msgid "Configurations"
msgstr "Konfigurationer"

#: frontend/src/components/UserDropdown.vue:180
msgid "Confirm"
msgstr "Bekräfta"

#. Label of the confirmation_email_sent (Check) field in DocType 'LMS Batch
#. Enrollment'
#: lms/lms/doctype/lms_batch_enrollment/lms_batch_enrollment.json
msgid "Confirmation Email Sent"
msgstr "Bekräftelse E-post Skickad"

#. Label of the confirmation_email_template (Link) field in DocType 'LMS Batch'
#: lms/lms/doctype/lms_batch/lms_batch.json
msgid "Confirmation Email Template"
msgstr "Bekräftelse E-post Mall"

#: lms/lms/doctype/lms_certificate/lms_certificate.py:29
msgid "Congratulations on getting certified!"
msgstr "Grattis till certifiering!"

#: frontend/src/components/CourseCardOverlay.vue:63
#: frontend/src/pages/Lesson.vue:53
msgid "Contact the Administrator to enroll for this course."
msgstr "Kontakta administratör för att registrera dig till denna kurs."

#. Label of the content (Text) field in DocType 'Course Lesson'
#. Label of the content (Rating) field in DocType 'LMS Batch Feedback'
#: frontend/src/components/Modals/EmailTemplateModal.vue:44
#: frontend/src/components/Modals/EmailTemplateModal.vue:57
#: frontend/src/pages/LessonForm.vue:62
#: lms/lms/doctype/course_lesson/course_lesson.json
#: lms/lms/doctype/lms_batch_feedback/lms_batch_feedback.json
msgid "Content"
msgstr "Innehåll "

#: frontend/src/components/CourseCardOverlay.vue:33
msgid "Continue Learning"
msgstr "Fortsätt lära dig"

#. Option for the 'Type' (Select) field in DocType 'Job Opportunity'
#: frontend/src/pages/Jobs.vue:178
#: lms/job/doctype/job_opportunity/job_opportunity.json
msgid "Contract"
msgstr "Avtal"

#: lms/lms/utils.py:442
msgid "Cookie Policy"
msgstr "Princip för Kakor"

#. Option for the 'Company Type' (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Corporate Organization"
msgstr "Organisation"

#. Option for the 'Status' (Select) field in DocType 'Exercise Latest
#. Submission'
#. Option for the 'Status' (Select) field in DocType 'Exercise Submission'
#: frontend/src/components/Quiz.vue:189
#: lms/lms/doctype/exercise_latest_submission/exercise_latest_submission.json
#: lms/lms/doctype/exercise_submission/exercise_submission.json
msgid "Correct"
msgstr "Korrekt"

#: frontend/src/components/Modals/Question.vue:79
msgid "Correct Answer"
msgstr "Rätt Svar"

#. Label of the country (Link) field in DocType 'User'
#. Label of the country (Link) field in DocType 'Job Opportunity'
#. Label of the country (Link) field in DocType 'Payment Country'
#: frontend/src/pages/Billing.vue:92 frontend/src/pages/JobForm.vue:40
#: frontend/src/pages/Jobs.vue:57 lms/fixtures/custom_field.json
#: lms/job/doctype/job_opportunity/job_opportunity.json
#: lms/lms/doctype/payment_country/payment_country.json
msgid "Country"
msgstr "Land"

#. Label of the course (Link) field in DocType 'Batch Course'
#. Label of the course (Link) field in DocType 'Cohort'
#. Label of the course (Link) field in DocType 'Cohort Mentor'
#. Label of the course (Link) field in DocType 'Cohort Staff'
#. Label of the course (Link) field in DocType 'Cohort Subgroup'
#. Label of the course (Link) field in DocType 'Course Chapter'
#. Label of the course (Link) field in DocType 'Course Lesson'
#. Label of the course (Link) field in DocType 'Exercise Latest Submission'
#. Label of the course (Link) field in DocType 'Exercise Submission'
#. Label of the course (Link) field in DocType 'LMS Assignment Submission'
#. Label of the course (Link) field in DocType 'LMS Batch Old'
#. Label of the course (Link) field in DocType 'LMS Certificate'
#. Label of the course (Link) field in DocType 'LMS Certificate Evaluation'
#. Label of the course (Link) field in DocType 'LMS Certificate Request'
#. Label of the course (Link) field in DocType 'LMS Course Interest'
#. Label of the course (Link) field in DocType 'LMS Course Mentor Mapping'
#. Label of the course (Link) field in DocType 'LMS Course Progress'
#. Label of the course (Link) field in DocType 'LMS Course Review'
#. Label of the course (Link) field in DocType 'LMS Enrollment'
#. Label of the course (Link) field in DocType 'LMS Exercise'
#. Label of the course (Link) field in DocType 'LMS Mentor Request'
#. Label of the course (Link) field in DocType 'LMS Program Course'
#. Label of the course (Link) field in DocType 'LMS Quiz'
#. Label of the course (Link) field in DocType 'LMS Quiz Submission'
#. Label of the course (Link) field in DocType 'LMS Video Watch Duration'
#. Label of the course (Link) field in DocType 'Related Courses'
#. Label of a Link in the LMS Workspace
#. Label of a shortcut in the LMS Workspace
#: frontend/src/components/Modals/BatchCourseModal.vue:20
#: frontend/src/components/Modals/BulkCertificates.vue:38
#: frontend/src/components/Modals/EvaluationModal.vue:20
#: frontend/src/components/Modals/Event.vue:24
#: lms/lms/doctype/batch_course/batch_course.json
#: lms/lms/doctype/cohort/cohort.json
#: lms/lms/doctype/cohort_mentor/cohort_mentor.json
#: lms/lms/doctype/cohort_staff/cohort_staff.json
#: lms/lms/doctype/cohort_subgroup/cohort_subgroup.json
#: lms/lms/doctype/course_chapter/course_chapter.json
#: lms/lms/doctype/course_lesson/course_lesson.json
#: lms/lms/doctype/exercise_latest_submission/exercise_latest_submission.json
#: lms/lms/doctype/exercise_submission/exercise_submission.json
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
#: lms/lms/doctype/lms_certificate/lms_certificate.json
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
#: lms/lms/doctype/lms_course_interest/lms_course_interest.json
#: lms/lms/doctype/lms_course_mentor_mapping/lms_course_mentor_mapping.json
#: lms/lms/doctype/lms_course_progress/lms_course_progress.json
#: lms/lms/doctype/lms_course_review/lms_course_review.json
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
#: lms/lms/doctype/lms_exercise/lms_exercise.json
#: lms/lms/doctype/lms_mentor_request/lms_mentor_request.json
#: lms/lms/doctype/lms_program_course/lms_program_course.json
#: lms/lms/doctype/lms_quiz/lms_quiz.json
#: lms/lms/doctype/lms_quiz_submission/lms_quiz_submission.json
#: lms/lms/doctype/lms_video_watch_duration/lms_video_watch_duration.json
#: lms/lms/doctype/related_courses/related_courses.json
#: lms/lms/report/course_progress_summary/course_progress_summary.js:9
#: lms/lms/report/course_progress_summary/course_progress_summary.py:51
#: lms/lms/workspace/lms/lms.json
msgid "Course"
msgstr "Utbildning"

#. Name of a DocType
#. Label of the chapter (Link) field in DocType 'Course Lesson'
#: lms/lms/doctype/course_chapter/course_chapter.json
#: lms/lms/doctype/course_lesson/course_lesson.json
msgid "Course Chapter"
msgstr "Kurs Kapitel"

#. Label of a shortcut in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "Course Completed"
msgstr "Klara Kurser"

#: frontend/src/pages/Statistics.vue:31
msgid "Course Completions"
msgstr "Kursavslutningar"

#. Name of a role
#: frontend/src/pages/ProfileRoles.vue:26
#: lms/lms/doctype/course_chapter/course_chapter.json
#: lms/lms/doctype/course_lesson/course_lesson.json
#: lms/lms/doctype/lms_category/lms_category.json
#: lms/lms/doctype/lms_course/lms_course.json
#: lms/lms/doctype/lms_program/lms_program.json
#: lms/lms/doctype/lms_programming_exercise/lms_programming_exercise.json
#: lms/lms/doctype/lms_programming_exercise_submission/lms_programming_exercise_submission.json
#: lms/lms/doctype/lms_question/lms_question.json
#: lms/lms/doctype/lms_quiz/lms_quiz.json
#: lms/lms/doctype/lms_video_watch_duration/lms_video_watch_duration.json
msgid "Course Creator"
msgstr "Kurs Skapare"

#. Label of a Card Break in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "Course Data"
msgstr "Kursdata"

#: frontend/src/pages/CourseForm.vue:181
msgid "Course Description"
msgstr "Kursbeskrivning"

#: frontend/src/pages/Statistics.vue:22
msgid "Course Enrollments"
msgstr "Kursregistreringar"

#. Name of a DocType
#: lms/lms/doctype/course_evaluator/course_evaluator.json
msgid "Course Evaluator"
msgstr "Kurs Utvärderare"

#: frontend/src/pages/CourseForm.vue:90
msgid "Course Image"
msgstr "Kursbild"

#. Name of a DocType
#: lms/lms/doctype/course_instructor/course_instructor.json
msgid "Course Instructor"
msgstr "Kurslärare"

#. Name of a DocType
#: lms/lms/doctype/course_lesson/course_lesson.json
msgid "Course Lesson"
msgstr "Kurslektion"

#: lms/www/lms.py:87
msgid "Course List"
msgstr "Kurslista"

#: lms/lms/report/course_progress_summary/course_progress_summary.py:58
msgid "Course Name"
msgstr "Kursnamn"

#: frontend/src/pages/CourseDetail.vue:78 frontend/src/pages/CourseForm.vue:293
msgid "Course Outline"
msgstr "Kursöversikt"

#. Name of a report
#: frontend/src/components/Modals/CourseProgressSummary.vue:5
#: lms/lms/report/course_progress_summary/course_progress_summary.json
msgid "Course Progress Summary"
msgstr "Kursframsteg Översikt"

#. Label of the section_break_7 (Section Break) field in DocType 'LMS Course'
#: lms/lms/doctype/lms_course/lms_course.json
msgid "Course Settings"
msgstr "Kursinställningar"

#. Label of a Card Break in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "Course Stats"
msgstr "Kursstatistik"

#. Label of the title (Data) field in DocType 'Batch Course'
#. Label of the course_title (Data) field in DocType 'Course Chapter'
#. Label of the course_title (Data) field in DocType 'LMS Certificate'
#. Label of the course_title (Data) field in DocType 'LMS Certificate Request'
#. Label of the course_title (Data) field in DocType 'LMS Program Course'
#: lms/lms/doctype/batch_course/batch_course.json
#: lms/lms/doctype/course_chapter/course_chapter.json
#: lms/lms/doctype/lms_certificate/lms_certificate.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
#: lms/lms/doctype/lms_program_course/lms_program_course.json
msgid "Course Title"
msgstr "Kurs Benämning"

#: frontend/src/pages/ProgramForm.vue:234
msgid "Course added to program"
msgstr "Kurs tillagd till program"

#: frontend/src/pages/CourseForm.vue:523
msgid "Course created successfully"
msgstr "Kurs skapad"

#: frontend/src/pages/CourseForm.vue:560
msgid "Course deleted successfully"
msgstr "Kurs är borttagen"

#: frontend/src/pages/ProgramForm.vue:303
msgid "Course moved successfully"
msgstr "Kurs flyttad"

#: frontend/src/pages/CourseForm.vue:543
msgid "Course updated successfully"
msgstr "Kurs uppdaterad"

#: lms/lms/doctype/lms_batch/lms_batch.py:54
#: lms/lms/doctype/lms_program/lms_program.py:19
msgid "Course {0} has already been added to this batch."
msgstr "Kurs {0} har redan lagts till i denna omgång."

#. Label of the courses (Table) field in DocType 'LMS Batch'
#. Label of the show_courses (Check) field in DocType 'LMS Settings'
#. Label of the courses (Check) field in DocType 'LMS Settings'
#: frontend/src/components/BatchCourses.vue:5
#: frontend/src/components/BatchOverlay.vue:37
#: frontend/src/components/BatchStudents.vue:25
#: frontend/src/components/Modals/BatchStudentProgress.vue:91
#: frontend/src/pages/BatchDetail.vue:44
#: frontend/src/pages/CourseCertification.vue:127
#: frontend/src/pages/Courses.vue:331 frontend/src/pages/Courses.vue:338
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Courses"
msgstr "Kurser"

#: lms/lms/web_template/lms_statistics/lms_statistics.html:14
#: lms/templates/statistics.html:28
msgid "Courses Completed"
msgstr "Klara Kurser"

#: frontend/src/components/BatchCourses.vue:154
msgid "Courses deleted successfully"
msgstr "Kurser borttagna"

#. Label of the cover_image (Attach Image) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Cover Image"
msgstr "Omslagsbild"

#: frontend/src/components/Modals/ChapterModal.vue:9
#: frontend/src/pages/Programs.vue:93
msgid "Create"
msgstr "Skapa"

#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.js:7
msgid "Create Certificate"
msgstr "Skapa Certifikat"

#: frontend/src/components/Controls/Link.vue:38
#: frontend/src/components/Controls/MultiSelect.vue:66
msgid "Create New"
msgstr "Skapa Ny"

#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseForm.vue:7
msgid "Create Programming Exercise"
msgstr "Skapa Programmeringsövning"

#: lms/templates/onboarding_header.html:19
msgid "Create a Course"
msgstr "Skapa Kurs"

#: frontend/src/components/Modals/LiveClassModal.vue:5
msgid "Create a Live Class"
msgstr "Skapa live lektion"

#: frontend/src/pages/Quizzes.vue:101
msgid "Create a Quiz"
msgstr "Skapa Frågesport"

#: frontend/src/components/AppSidebar.vue:576
msgid "Create a batch"
msgstr "Skapa grupp"

#: frontend/src/components/AppSidebar.vue:567
msgid "Create a course"
msgstr "Skapa Kurs"

#: frontend/src/components/AppSidebar.vue:577
msgid "Create a live class"
msgstr "Skapa live lektion"

#: frontend/src/components/Modals/AssignmentForm.vue:13
msgid "Create an Assignment"
msgstr "Skapa Uppgift"

#: frontend/src/components/AppSidebar.vue:501
msgid "Create your first batch"
msgstr "Skapa din första grupp"

#: frontend/src/components/AppSidebar.vue:432
msgid "Create your first course"
msgstr "Skapa din första kurs"

#: frontend/src/components/AppSidebar.vue:479
msgid "Create your first quiz"
msgstr "Skapa din första frågesport"

#: frontend/src/pages/Assignments.vue:173 frontend/src/pages/Courses.vue:321
msgid "Created"
msgstr "Skapad"

#: frontend/src/components/AppSidebar.vue:573
msgid "Creating a batch"
msgstr "Skapar grupp"

#: frontend/src/components/AppSidebar.vue:564
msgid "Creating a course"
msgstr "Skapar kurs"

#. Label of the currency (Link) field in DocType 'LMS Batch'
#. Label of the currency (Link) field in DocType 'LMS Course'
#. Label of the currency (Link) field in DocType 'LMS Payment'
#: frontend/src/pages/BatchForm.vue:272 frontend/src/pages/CourseForm.vue:262
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_course/lms_course.json
#: lms/lms/doctype/lms_payment/lms_payment.json
msgid "Currency"
msgstr "Valuta"

#. Label of the current_lesson (Link) field in DocType 'LMS Enrollment'
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
msgid "Current Lesson"
msgstr "Aktuell Lektion"

#: frontend/src/components/AppSidebar.vue:595
msgid "Custom Certificate Templates"
msgstr "Anpassade Certifikat Mallar"

#. Label of the custom_component (Code) field in DocType 'LMS Batch'
#: lms/lms/doctype/lms_batch/lms_batch.json
msgid "Custom HTML"
msgstr "Anpassad HTML"

#. Label of the custom_script (Code) field in DocType 'LMS Batch'
#: lms/lms/doctype/lms_batch/lms_batch.json
msgid "Custom Script (JavaScript)"
msgstr "Anpassad Skript (JavaScript)"

#. Label of the custom_signup_content (HTML Editor) field in DocType 'LMS
#. Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Custom Signup Content"
msgstr "Anpassad Registrering Innehåll"

#. Label of the customisations_tab (Tab Break) field in DocType 'LMS Batch'
#: lms/lms/doctype/lms_batch/lms_batch.json
msgid "Customisations"
msgstr "Anpassningar"

#. Label of the show_dashboard (Check) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Dashboard"
msgstr "Översikt Panel"

#. Label of the date (Date) field in DocType 'LMS Batch Timetable'
#. Label of the date (Date) field in DocType 'LMS Certificate Evaluation'
#. Label of the date (Date) field in DocType 'LMS Certificate Request'
#. Label of the date (Date) field in DocType 'LMS Live Class'
#. Label of the date (Date) field in DocType 'Scheduled Flow'
#: frontend/src/components/Modals/EvaluationModal.vue:26
#: frontend/src/components/Modals/Event.vue:40
#: frontend/src/components/Modals/LiveClassModal.vue:29
#: lms/lms/doctype/lms_batch_timetable/lms_batch_timetable.json
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
#: lms/lms/doctype/lms_live_class/lms_live_class.json
#: lms/lms/doctype/scheduled_flow/scheduled_flow.json
#: lms/templates/quiz/quiz.html:149
msgid "Date"
msgstr "Datum"

#: frontend/src/pages/BatchForm.vue:69
msgid "Date and Time"
msgstr "Datum och Tid"

#: lms/templates/emails/live_class_reminder.html:13
msgid "Date:"
msgstr "Datum:"

#. Label of the day (Select) field in DocType 'Evaluator Schedule'
#. Label of the day (Int) field in DocType 'LMS Batch Timetable'
#. Label of the day (Select) field in DocType 'LMS Certificate Request'
#: frontend/src/pages/ProfileEvaluator.vue:26
#: lms/lms/doctype/evaluator_schedule/evaluator_schedule.json
#: lms/lms/doctype/lms_batch_timetable/lms_batch_timetable.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
msgid "Day"
msgstr "Dag"

#: lms/templates/emails/mentor_request_creation_email.html:2
#: lms/templates/emails/mentor_request_status_update_email.html:2
msgid "Dear"
msgstr "Hej"

#: lms/templates/emails/batch_confirmation.html:2
#: lms/templates/emails/batch_start_reminder.html:2
#: lms/templates/emails/certification.html:2
#: lms/templates/emails/live_class_reminder.html:2
msgid "Dear "
msgstr "Hej "

#: frontend/src/components/Modals/EmailTemplateModal.vue:66
msgid "Dear {{ member_name }},\\n\\nYou have been enrolled in our upcoming batch {{ batch_name }}.\\n\\nThanks,\\nFrappe Learning"
msgstr "Kära {{ member_name }},\\n\\nDu har blivit inskriven i vår kommande grupp {{ batch_name }}.\\n\\nTack,\\nFrappe Lärande"

#. Label of the default_currency (Link) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Default Currency"
msgstr "Standard Valuta"

#. Label of the degree_type (Data) field in DocType 'Education Detail'
#: lms/lms/doctype/education_detail/education_detail.json
msgid "Degree Type"
msgstr "Examen Typ"

#: frontend/src/components/Controls/ChildTable.vue:56
#: frontend/src/components/CourseOutline.vue:253
#: frontend/src/components/CourseOutline.vue:311
#: frontend/src/pages/CourseForm.vue:573
#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseForm.vue:67
msgid "Delete"
msgstr "Ta bort"

#: frontend/src/components/CourseOutline.vue:56
msgid "Delete Chapter"
msgstr "Ta bort Kapitel"

#: frontend/src/pages/CourseForm.vue:567
msgid "Delete Course"
msgstr "Ta bort kurs"

#: frontend/src/components/CourseOutline.vue:305
msgid "Delete this chapter?"
msgstr "Ta bort detta kapitel?"

#: frontend/src/components/CourseOutline.vue:247
msgid "Delete this lesson?"
msgstr "Ta bort denna lektion?"

#: frontend/src/pages/CourseForm.vue:568
msgid "Deleting the course will also delete all its chapters and lessons. Are you sure you want to delete this course?"
msgstr "Om du tar bort kurs raderas också alla dess kapitel och lektioner. Är du säker på att du vill ta bort denna kurs?"

#: frontend/src/components/CourseOutline.vue:306
msgid "Deleting this chapter will also delete all its lessons and permanently remove it from the course. This action cannot be undone. Are you sure you want to continue?"
msgstr "Borttagning av detta kapitel tar också bort alla dess lektioner och de tas bort permanent från kurs. Denna åtgärd kan inte ångras. Är du säker på att du vill fortsätta?"

#: frontend/src/components/CourseOutline.vue:248
msgid "Deleting this lesson will permanently remove it from the course. This action cannot be undone. Are you sure you want to continue?"
msgstr "Borttagning av denna lektion kommer att ta bort den permanent från kurs. Denna åtgärd kan inte ångras. Är du säker på att du vill fortsätta?"

#. Label of the description (Text Editor) field in DocType 'Job Opportunity'
#. Label of the description (Small Text) field in DocType 'Certification'
#. Label of the description (Markdown Editor) field in DocType 'Cohort'
#. Label of the description (Markdown Editor) field in DocType 'Cohort
#. Subgroup'
#. Label of the description (Small Text) field in DocType 'LMS Badge'
#. Label of the description (Small Text) field in DocType 'LMS Batch'
#. Label of the description (Markdown Editor) field in DocType 'LMS Batch Old'
#. Label of the description (Text Editor) field in DocType 'LMS Course'
#. Label of the description (Small Text) field in DocType 'LMS Exercise'
#. Label of the description (Text) field in DocType 'LMS Live Class'
#. Label of the description (Small Text) field in DocType 'Work Experience'
#: frontend/src/components/Modals/LiveClassModal.vue:80
#: frontend/src/pages/JobForm.vue:125
#: lms/job/doctype/job_opportunity/job_opportunity.json
#: lms/lms/doctype/certification/certification.json
#: lms/lms/doctype/cohort/cohort.json
#: lms/lms/doctype/cohort_subgroup/cohort_subgroup.json
#: lms/lms/doctype/lms_badge/lms_badge.json
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
#: lms/lms/doctype/lms_course/lms_course.json
#: lms/lms/doctype/lms_exercise/lms_exercise.json
#: lms/lms/doctype/lms_live_class/lms_live_class.json
#: lms/lms/doctype/work_experience/work_experience.json
msgid "Description"
msgstr "Beskrivning"

#: frontend/src/components/Apps.vue:51
msgid "Desk"
msgstr "Skrivbord"

#: frontend/src/components/Modals/DiscussionModal.vue:22
#: frontend/src/pages/BatchForm.vue:14 frontend/src/pages/CourseForm.vue:25
#: frontend/src/pages/QuizForm.vue:50
msgid "Details"
msgstr "Detaljer"

#: frontend/src/pages/CourseForm.vue:172
msgid "Disable Self Enrollment"
msgstr "Inaktivera självregistrering"

#. Label of the disable_self_learning (Check) field in DocType 'LMS Course'
#: lms/lms/doctype/lms_course/lms_course.json
msgid "Disable Self Learning"
msgstr "Inaktivera självlärande"

#. Label of the disable_signup (Check) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Disable Signup"
msgstr "Inaktivera Registrering"

#. Label of the disabled (Check) field in DocType 'Job Opportunity'
#: frontend/src/components/Settings/ZoomSettings.vue:56
#: lms/job/doctype/job_opportunity/job_opportunity.json
msgid "Disabled"
msgstr "Inaktiverad"

#: frontend/src/components/DiscussionReplies.vue:57
#: lms/lms/widgets/NoPreviewModal.html:25 lms/templates/reviews.html:159
msgid "Discard"
msgstr "Ångra"

#. Label of the show_discussions (Check) field in DocType 'LMS Settings'
#: frontend/src/pages/Batch.vue:88
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Discussions"
msgstr "Diskussioner"

#. Option for the 'File Type' (Select) field in DocType 'Course Lesson'
#. Option for the 'Type' (Select) field in DocType 'LMS Assignment'
#. Option for the 'Type' (Select) field in DocType 'LMS Assignment Submission'
#: lms/lms/doctype/course_lesson/course_lesson.json
#: lms/lms/doctype/lms_assignment/lms_assignment.json
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
msgid "Document"
msgstr "Dokument"

#: lms/templates/emails/payment_reminder.html:11
msgid "Don’t miss this opportunity to enhance your skills. Click below to complete your enrollment"
msgstr "Missa inte detta tillfälle att förbättra dina kunskaper. Klicka nedan för att slutföra din registrering"

#. Label of the dream_companies (Data) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Dream Companies"
msgstr "Drömbolag"

#: lms/lms/doctype/lms_question/lms_question.py:33
msgid "Duplicate options found for this question."
msgstr "Duplicerade alternativ hittades för denna fråga."

#. Label of the duration (Data) field in DocType 'Cohort'
#. Label of the duration (Data) field in DocType 'LMS Batch Timetable'
#. Label of the duration (Int) field in DocType 'LMS Live Class'
#. Label of the duration (Int) field in DocType 'LMS Live Class Participant'
#: frontend/src/components/Modals/LiveClassModal.vue:36
#: lms/lms/doctype/cohort/cohort.json
#: lms/lms/doctype/lms_batch_timetable/lms_batch_timetable.json
#: lms/lms/doctype/lms_live_class/lms_live_class.json
#: lms/lms/doctype/lms_live_class_participant/lms_live_class_participant.json
msgid "Duration"
msgstr "Varaktighet"

#. Label of the duration (Data) field in DocType 'LMS Quiz'
#: frontend/src/pages/QuizForm.vue:67 lms/lms/doctype/lms_quiz/lms_quiz.json
msgid "Duration (in minutes)"
msgstr "Varaktighet (i minuter)"

#: frontend/src/components/Modals/LiveClassModal.vue:32
msgid "Duration of the live class in minutes"
msgstr "Livelektion varaktighet i minuter"

#. Label of the email (Link) field in DocType 'Cohort Join Request'
#: lms/lms/doctype/cohort_join_request/cohort_join_request.json
msgid "E-Mail"
msgstr "E-post"

#. Label of the email (Link) field in DocType 'Cohort Mentor'
#: lms/lms/doctype/cohort_mentor/cohort_mentor.json
msgid "E-mail"
msgstr "E-post"

#: frontend/src/components/BatchOverlay.vue:116
#: frontend/src/components/CourseCardOverlay.vue:116
#: frontend/src/components/Modals/ChapterModal.vue:9
#: frontend/src/pages/JobDetail.vue:34 frontend/src/pages/Lesson.vue:130
#: frontend/src/pages/Profile.vue:36 frontend/src/pages/Programs.vue:53
msgid "Edit"
msgstr "Redigera"

#: frontend/src/components/Modals/AssignmentForm.vue:14
msgid "Edit Assignment"
msgstr "Redigera Uppgift"

#: frontend/src/components/CourseOutline.vue:49
#: frontend/src/components/Modals/ChapterModal.vue:5
msgid "Edit Chapter"
msgstr "Redigera Kapitel"

#: frontend/src/components/Modals/EmailTemplateModal.vue:8
msgid "Edit Email Template"
msgstr "Redigera e-post mall"

#: frontend/src/pages/Profile.vue:72
msgid "Edit Profile"
msgstr "Redigera Profil"

#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseForm.vue:8
msgid "Edit Programming Exercise"
msgstr "Redigera Programmeringsövning"

#: frontend/src/components/Modals/ZoomAccountModal.vue:6
msgid "Edit Zoom Account"
msgstr "Redigera Zoom konto"

#: frontend/src/pages/QuizForm.vue:199
msgid "Edit the question"
msgstr "Redigera fråga"

#. Label of the education (Table) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Education"
msgstr "Utbildning"

#. Name of a DocType
#: lms/lms/doctype/education_detail/education_detail.json
msgid "Education Detail"
msgstr "Utbildning Detalj"

#. Label of the education_details (Section Break) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Education Details"
msgstr "Utbildning Detaljer"

#: frontend/src/components/Settings/Evaluators.vue:105
#: frontend/src/components/Settings/Members.vue:103
#: lms/templates/signup-form.html:10
msgid "Email"
msgstr "E-post"

#: frontend/src/components/Modals/Event.vue:16
msgid "Email ID"
msgstr "E-post"

#. Label of the email_sent (Check) field in DocType 'LMS Course Interest'
#: lms/lms/doctype/lms_course_interest/lms_course_interest.json
msgid "Email Sent"
msgstr "E-post Skickad"

#: frontend/src/pages/BatchForm.vue:154
msgid "Email Template"
msgstr "E-post Mall"

#: frontend/src/components/Modals/EmailTemplateModal.vue:117
msgid "Email Template created successfully"
msgstr "E-post mall skapad"

#: frontend/src/components/Modals/EmailTemplateModal.vue:146
msgid "Email Template updated successfully"
msgstr "E-post mall uppdaterad"

#. Label of the email_templates_tab (Tab Break) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Email Templates"
msgstr "E-Post Mallar"

#: frontend/src/components/Settings/EmailTemplates.vue:128
#: frontend/src/components/Settings/ZoomSettings.vue:161
msgid "Email Templates deleted successfully"
msgstr "E-post mallar raderade"

#. Label of the show_emails (Check) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Emails"
msgstr "E-post "

#. Option for the 'User Category' (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json lms/templates/signup-form.html:25
msgid "Employee"
msgstr "Personal"

#. Label of the enable (Check) field in DocType 'Zoom Settings'
#: lms/lms/doctype/zoom_settings/zoom_settings.json
msgid "Enable"
msgstr "Aktivera"

#: lms/lms/doctype/lms_settings/lms_settings.py:21
msgid "Enable Google API in Google Settings to send calendar invites for evaluations."
msgstr "Aktivera Google API i Google Inställningar för att skicka kalenderinbjudningar för utvärderingar."

#. Label of the enable_learning_paths (Check) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Enable Learning Paths"
msgstr "Aktivera Inlärningsvägar"

#. Label of the enable_negative_marking (Check) field in DocType 'LMS Quiz'
#: frontend/src/pages/QuizForm.vue:117 lms/lms/doctype/lms_quiz/lms_quiz.json
msgid "Enable Negative Marking"
msgstr "Aktivera Negativ Betygsättning"

#: frontend/src/components/Modals/ChapterModal.vue:24
msgid "Enable this only if you want to upload a SCORM package as a chapter."
msgstr "Aktivera detta endast om du vill ladda upp SCORM paket som kapitel."

#. Label of the enabled (Check) field in DocType 'LMS Badge'
#. Label of the enabled (Check) field in DocType 'LMS Zoom Settings'
#: frontend/src/components/Modals/ZoomAccountModal.vue:23
#: frontend/src/components/Settings/ZoomSettings.vue:53
#: lms/lms/doctype/lms_badge/lms_badge.json
#: lms/lms/doctype/lms_zoom_settings/lms_zoom_settings.json
msgid "Enabled"
msgstr "Aktiverad"

#: frontend/src/components/Modals/BulkCertificates.vue:53
msgid "Enabling this will publish the certificate on the certified participants page."
msgstr "Om du aktiverar detta kommer certifikatet att publiceras på sidan för certifierade deltagare."

#. Label of the end_date (Date) field in DocType 'Cohort'
#. Label of the end_date (Date) field in DocType 'LMS Batch'
#: frontend/src/pages/BatchForm.vue:82 lms/lms/doctype/cohort/cohort.json
#: lms/lms/doctype/lms_batch/lms_batch.json
msgid "End Date"
msgstr "Slut Datum"

#. Label of the end_date (Date) field in DocType 'Education Detail'
#: lms/lms/doctype/education_detail/education_detail.json
msgid "End Date (or expected)"
msgstr "Slutdatum (eller förväntat)"

#. Label of the end_time (Time) field in DocType 'Evaluator Schedule'
#. Label of the end_time (Time) field in DocType 'LMS Batch'
#. Label of the end_time (Time) field in DocType 'LMS Batch Old'
#. Label of the end_time (Time) field in DocType 'LMS Batch Timetable'
#. Label of the end_time (Time) field in DocType 'LMS Certificate Evaluation'
#. Label of the end_time (Time) field in DocType 'LMS Certificate Request'
#. Label of the end_time (Time) field in DocType 'Scheduled Flow'
#: frontend/src/pages/BatchForm.vue:98
#: frontend/src/pages/ProfileEvaluator.vue:32
#: lms/lms/doctype/evaluator_schedule/evaluator_schedule.json
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
#: lms/lms/doctype/lms_batch_timetable/lms_batch_timetable.json
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
#: lms/lms/doctype/scheduled_flow/scheduled_flow.json
msgid "End Time"
msgstr "Slut Tid"

#: frontend/src/components/LiveClass.vue:89
msgid "Ended"
msgstr "Avslutad"

#: frontend/src/components/BatchOverlay.vue:103
msgid "Enroll Now"
msgstr "Registrera Nu"

#: frontend/src/pages/Batches.vue:286 frontend/src/pages/Courses.vue:324
msgid "Enrolled"
msgstr "Inskriven"

#: frontend/src/components/CourseCard.vue:46
#: frontend/src/components/CourseCardOverlay.vue:138
#: frontend/src/pages/CourseDetail.vue:33
msgid "Enrolled Students"
msgstr "Inskrivna Studenter"

#: lms/lms/doctype/lms_batch_enrollment/lms_batch_enrollment.py:93
msgid "Enrollment Confirmation for {0}"
msgstr "Registreringsbekräftelse för {0}"

#: lms/lms/web_template/lms_statistics/lms_statistics.html:14
#: lms/templates/statistics.html:20
msgid "Enrollment Count"
msgstr "Antal Inskrivna"

#: lms/lms/utils.py:1943
msgid "Enrollment Failed"
msgstr "Registrering Misslyckad"

#. Label of the enrollments (Int) field in DocType 'LMS Course'
#. Label of a chart in the LMS Workspace
#. Label of a shortcut in the LMS Workspace
#: lms/lms/doctype/lms_course/lms_course.json lms/lms/workspace/lms/lms.json
msgid "Enrollments"
msgstr "Inskrivningar"

#: lms/lms/doctype/lms_settings/lms_settings.py:26
msgid "Enter Client Id and Client Secret in Google Settings to send calendar invites for evaluations."
msgstr "Ange Klient Id och Klient Hemlighet i Google inställningar för att skicka kalender inbjudningar för utvärderingar."

#: frontend/src/components/Assignment.vue:113
msgid "Enter a URL"
msgstr "Ange URL"

#: lms/templates/quiz/quiz.html:53
msgid "Enter the correct answer"
msgstr "Ange korrekt svar"

#: frontend/src/components/Modals/ZoomAccountModal.vue:163
msgid "Error creating Zoom Account"
msgstr "Fel vid skapande av Zoom-konto"

#: frontend/src/components/Modals/EmailTemplateModal.vue:122
msgid "Error creating email template"
msgstr "Fel vid skapande av e-post mall"

#: lms/lms/doctype/lms_batch/lms_batch.py:204
msgid "Error creating live class. Please try again. {0}"
msgstr "Fel vid skapande av liveklass. Vänligen försök igen. {0}"

#: frontend/src/pages/Quizzes.vue:212
msgid "Error creating quiz: {0}"
msgstr "Fel vid skapande av frågesport: {0}"

#: frontend/src/components/Settings/EmailTemplates.vue:133
#: frontend/src/components/Settings/ZoomSettings.vue:166
msgid "Error deleting email templates"
msgstr "Fel vid borttagning av e-post mallar"

#: frontend/src/components/Modals/ZoomAccountModal.vue:200
msgid "Error updating Zoom Account"
msgstr "Fel vid uppdatering av Zoom konto"

#: frontend/src/components/Modals/EmailTemplateModal.vue:151
msgid "Error updating email template"
msgstr "Fel vid uppdatering av e-post mall"

#. Label of a Link in the LMS Workspace
#. Label of a shortcut in the LMS Workspace
#: frontend/src/components/Modals/Event.vue:374 lms/lms/workspace/lms/lms.json
msgid "Evaluation"
msgstr "Utvärdering"

#. Label of the section_break_6 (Section Break) field in DocType 'LMS
#. Certificate Evaluation'
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
msgid "Evaluation Details"
msgstr "Utvärdering Detaljer"

#. Label of the evaluation_end_date (Date) field in DocType 'LMS Batch'
#: frontend/src/pages/BatchForm.vue:115
#: lms/lms/doctype/lms_batch/lms_batch.json
msgid "Evaluation End Date"
msgstr "Utvärdering Slutdatum"

#. Label of a Link in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "Evaluation Request"
msgstr "Utvärdering Begäran"

#: lms/lms/doctype/lms_batch/lms_batch.py:87
msgid "Evaluation end date cannot be less than the batch end date."
msgstr "Utvärdering slutdatum får inte vara tidigare än grupp slutdatum."

#: frontend/src/components/Modals/Event.vue:256
msgid "Evaluation saved successfully"
msgstr "Utvärdering sparad"

#. Label of the evaluator (Link) field in DocType 'Batch Course'
#. Label of the evaluator (Link) field in DocType 'Course Evaluator'
#. Label of the evaluator (Link) field in DocType 'LMS Assignment Submission'
#. Label of the evaluator (Link) field in DocType 'LMS Certificate'
#. Label of the evaluator (Link) field in DocType 'LMS Certificate Evaluation'
#. Label of the evaluator (Link) field in DocType 'LMS Certificate Request'
#. Label of the evaluator (Link) field in DocType 'LMS Course'
#: frontend/src/components/Modals/BatchCourseModal.vue:37
#: frontend/src/components/Modals/BulkCertificates.vue:22
#: frontend/src/pages/CourseForm.vue:251 frontend/src/pages/ProfileRoles.vue:32
#: lms/lms/doctype/batch_course/batch_course.json
#: lms/lms/doctype/course_evaluator/course_evaluator.json
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
#: lms/lms/doctype/lms_certificate/lms_certificate.json
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
#: lms/lms/doctype/lms_course/lms_course.json
#: lms/templates/upcoming_evals.html:33
msgid "Evaluator"
msgstr "Utvärderare"

#. Label of the evaluator_name (Data) field in DocType 'LMS Certificate'
#. Label of the evaluator_name (Data) field in DocType 'LMS Certificate
#. Evaluation'
#. Label of the evaluator_name (Data) field in DocType 'LMS Certificate
#. Request'
#: lms/lms/doctype/lms_certificate/lms_certificate.json
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
msgid "Evaluator Name"
msgstr "Utvärderarens Namn"

#. Name of a DocType
#: lms/lms/doctype/evaluator_schedule/evaluator_schedule.json
msgid "Evaluator Schedule"
msgstr "Utvärderare Schema"

#: frontend/src/components/Settings/Evaluators.vue:163
msgid "Evaluator added successfully"
msgstr "Utvärderare tillagd"

#: frontend/src/components/Settings/Evaluators.vue:196
msgid "Evaluator deleted successfully"
msgstr "Utvärderare borttagen"

#: lms/lms/api.py:1419
msgid "Evaluator does not exist."
msgstr "Utvärderare finns inte."

#: lms/lms/doctype/lms_course/lms_course.py:67
msgid "Evaluator is required for paid certificates."
msgstr "Utvärderare erfordras för betalda certifikat."

#. Label of the event (Select) field in DocType 'LMS Badge'
#. Label of the event (Link) field in DocType 'LMS Live Class'
#: lms/lms/doctype/lms_badge/lms_badge.json
#: lms/lms/doctype/lms_live_class/lms_live_class.json
msgid "Event"
msgstr "Händelse"

#: frontend/src/pages/BatchForm.vue:109
msgid "Example: IST (+5:30)"
msgstr "Exempel: IST (+5:30)"

#. Label of the exercise (Link) field in DocType 'Exercise Latest Submission'
#. Label of the exercise (Link) field in DocType 'Exercise Submission'
#. Label of the exercise (Link) field in DocType 'LMS Programming Exercise
#. Submission'
#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseSubmissions.vue:274
#: lms/lms/doctype/exercise_latest_submission/exercise_latest_submission.json
#: lms/lms/doctype/exercise_submission/exercise_submission.json
#: lms/lms/doctype/lms_programming_exercise_submission/lms_programming_exercise_submission.json
msgid "Exercise"
msgstr "Övning"

#. Name of a DocType
#: lms/lms/doctype/exercise_latest_submission/exercise_latest_submission.json
msgid "Exercise Latest Submission"
msgstr "Träna Senaste Inlämning"

#. Name of a DocType
#: lms/lms/doctype/exercise_submission/exercise_submission.json
msgid "Exercise Submission"
msgstr "Övning inlämning"

#. Label of the exercise_title (Data) field in DocType 'Exercise Latest
#. Submission'
#. Label of the exercise_title (Data) field in DocType 'Exercise Submission'
#. Label of the exercise_title (Data) field in DocType 'LMS Programming
#. Exercise Submission'
#: lms/lms/doctype/exercise_latest_submission/exercise_latest_submission.json
#: lms/lms/doctype/exercise_submission/exercise_submission.json
#: lms/lms/doctype/lms_programming_exercise_submission/lms_programming_exercise_submission.json
msgid "Exercise Title"
msgstr "Övning Benämning"

#: frontend/src/components/AppSidebar.vue:142
msgid "Expand"
msgstr "Expandera"

#. Label of the expected_output (Data) field in DocType 'LMS Test Case'
#. Label of the expected_output (Data) field in DocType 'LMS Test Case
#. Submission'
#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseSubmission.vue:127
#: lms/lms/doctype/lms_test_case/lms_test_case.json
#: lms/lms/doctype/lms_test_case_submission/lms_test_case_submission.json
msgid "Expected Output"
msgstr "Förväntad Utdata"

#. Label of the expiration_date (Data) field in DocType 'Certification'
#: lms/lms/doctype/certification/certification.json
msgid "Expiration Date"
msgstr "Utgång Datum"

#. Label of the expiry_date (Date) field in DocType 'LMS Certificate'
#: frontend/src/components/Modals/BulkCertificates.vue:33
#: frontend/src/components/Modals/Event.vue:126
#: lms/lms/doctype/lms_certificate/lms_certificate.json
msgid "Expiry Date"
msgstr "Utgång Datum"

#. Label of the explanation_1 (Small Text) field in DocType 'LMS Question'
#. Label of the explanation_3 (Small Text) field in DocType 'LMS Question'
#. Label of the explanation_4 (Small Text) field in DocType 'LMS Question'
#: frontend/src/components/Modals/Question.vue:75
#: lms/lms/doctype/lms_question/lms_question.json
msgid "Explanation"
msgstr "Förklaring"

#. Label of the explanation_2 (Small Text) field in DocType 'LMS Question'
#: lms/lms/doctype/lms_question/lms_question.json
msgid "Explanation "
msgstr "Förklaring "

#: lms/lms/web_template/course_cards/course_cards.html:15
#: lms/lms/web_template/recently_published_courses/recently_published_courses.html:16
msgid "Explore More"
msgstr "Utforska mer"

#. Option for the 'Status' (Select) field in DocType 'LMS Assignment
#. Submission'
#. Option for the 'Status' (Select) field in DocType 'LMS Certificate
#. Evaluation'
#: frontend/src/components/Modals/Event.vue:366
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
msgid "Fail"
msgstr "Misslyckad "

#. Option for the 'Status' (Select) field in DocType 'LMS Programming Exercise
#. Submission'
#. Option for the 'Status' (Select) field in DocType 'LMS Test Case Submission'
#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseSubmissions.vue:37
#: lms/lms/doctype/lms_programming_exercise_submission/lms_programming_exercise_submission.json
#: lms/lms/doctype/lms_test_case_submission/lms_test_case_submission.json
msgid "Failed"
msgstr "Misslyckad"

#: lms/lms/doctype/lms_live_class/lms_live_class.py:139
msgid "Failed to fetch attendance data from Zoom for class {0}: {1}"
msgstr "Misslyckades med att hämta närvarodata från Zoom för lektion {0}: {1}"

#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseSubmission.vue:358
msgid "Failed to submit. Please try again. {0}"
msgstr "Misslyckades med inlämning. Försök igen. {0}"

#: frontend/src/utils/index.js:668
msgid "Failed to update meta tags {0}"
msgstr "Misslyckades med att uppdatera metataggar {0}"

#. Label of the featured (Check) field in DocType 'LMS Course'
#: frontend/src/components/CourseCard.vue:20
#: frontend/src/pages/CourseForm.vue:167
#: lms/lms/doctype/lms_course/lms_course.json
msgid "Featured"
msgstr "Utvald"

#. Label of the feedback (Small Text) field in DocType 'LMS Batch Feedback'
#: frontend/src/components/BatchFeedback.vue:30
#: frontend/src/pages/Batch.vue:146
#: lms/lms/doctype/lms_batch_feedback/lms_batch_feedback.json
msgid "Feedback"
msgstr "Återkoppling"

#: frontend/src/components/Assignment.vue:64
msgid "Feel free to make edits to your submission if needed."
msgstr "Känn dig fri att göra ändringar i din inlämning om det behövs."

#. Label of the field_to_check (Select) field in DocType 'LMS Badge'
#: lms/lms/doctype/lms_badge/lms_badge.json
msgid "Field To Check"
msgstr "Fält att Kontrollera"

#. Label of the major (Data) field in DocType 'Education Detail'
#: lms/lms/doctype/education_detail/education_detail.json
msgid "Field of Major/Study"
msgstr "Huvudämne/Studieinriktning"

#. Label of the file_type (Select) field in DocType 'Course Lesson'
#: lms/lms/doctype/course_lesson/course_lesson.json
msgid "File Type"
msgstr "Fil Typ"

#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseSubmissions.vue:23
msgid "Filter by Exercise"
msgstr "Filtrera efter Övning"

#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseSubmissions.vue:28
msgid "Filter by Member"
msgstr "Filtrera efter Medlem"

#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseSubmissions.vue:39
msgid "Filter by Status"
msgstr "Filtrera efter Status"

#: frontend/src/components/Modals/EditProfile.vue:59
#: frontend/src/components/Settings/Members.vue:110
msgid "First Name"
msgstr "Förnamn"

#. Option for the 'Time Preference' (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Fixed 9-5"
msgstr "Fast 9-5"

#. Option for the 'Time Preference' (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Flexible Time"
msgstr "Flexibel Tid"

#. Option for the 'Attire Preference' (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Formal Wear"
msgstr "Formella Kläder"

#: lms/lms/widgets/CourseCard.html:114
msgid "Free"
msgstr "Gratis"

#. Option for the 'Type' (Select) field in DocType 'Job Opportunity'
#: frontend/src/pages/Jobs.vue:179
#: lms/job/doctype/job_opportunity/job_opportunity.json
msgid "Freelance"
msgstr "Frilansare"

#. Option for the 'User Category' (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json lms/templates/signup-form.html:27
msgid "Freelancer/Just looking"
msgstr "Frilansare/Tittar bara"

#. Option for the 'Grade Type' (Select) field in DocType 'Education Detail'
#: lms/lms/doctype/education_detail/education_detail.json
msgid "French (e.g. Distinction)"
msgstr "Franska (t.ex. Distinktion)"

#. Option for the 'Day' (Select) field in DocType 'Evaluator Schedule'
#. Option for the 'Day' (Select) field in DocType 'LMS Certificate Request'
#: lms/lms/doctype/evaluator_schedule/evaluator_schedule.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
msgid "Friday"
msgstr "Fredag"

#. Label of the unavailable_from (Date) field in DocType 'Course Evaluator'
#: frontend/src/pages/ProfileEvaluator.vue:99
#: lms/lms/doctype/course_evaluator/course_evaluator.json
msgid "From"
msgstr "Från"

#. Label of the from_date (Date) field in DocType 'Work Experience'
#: lms/lms/doctype/work_experience/work_experience.json
msgid "From Date"
msgstr "Från Datum"

#. Label of the full_name (Data) field in DocType 'Course Evaluator'
#. Label of the full_name (Data) field in DocType 'Invite Request'
#. Label of the full_name (Data) field in DocType 'LMS Program Member'
#: lms/lms/doctype/course_evaluator/course_evaluator.json
#: lms/lms/doctype/invite_request/invite_request.json
#: lms/lms/doctype/lms_program_member/lms_program_member.json
#: lms/templates/signup-form.html:5
msgid "Full Name"
msgstr "Fullständig Namn"

#. Option for the 'Type' (Select) field in DocType 'Job Opportunity'
#: frontend/src/pages/Jobs.vue:176
#: lms/job/doctype/job_opportunity/job_opportunity.json
msgid "Full Time"
msgstr "Heltid"

#. Name of a DocType
#. Label of the function (Data) field in DocType 'Function'
#. Label of the function (Link) field in DocType 'Preferred Function'
#: lms/lms/doctype/function/function.json
#: lms/lms/doctype/preferred_function/preferred_function.json
msgid "Function"
msgstr "Funktion"

#: frontend/src/pages/Billing.vue:43
msgid "GST Amount"
msgstr "Moms Belopp"

#: frontend/src/pages/Billing.vue:110
msgid "GST Number"
msgstr "Moms Nummer"

#. Label of the gstin (Data) field in DocType 'LMS Payment'
#: lms/lms/doctype/lms_payment/lms_payment.json
msgid "GSTIN"
msgstr "GSTIN"

#. Label of the general_tab (Tab Break) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "General"
msgstr "Allmän"

#: frontend/src/components/Modals/BulkCertificates.vue:5
#: frontend/src/pages/Batch.vue:12
msgid "Generate Certificates"
msgstr "Skapa Certifikat"

#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.js:15
msgid "Generate Google Meet Link"
msgstr "Skapa Google Meet länk"

#: frontend/src/components/CourseCardOverlay.vue:89
msgid "Get Certificate"
msgstr "Hämta Certifikat"

#: frontend/src/components/CertificationLinks.vue:34
#: frontend/src/components/CertificationLinks.vue:50
#: frontend/src/pages/CertifiedParticipants.vue:11
msgid "Get Certified"
msgstr "Bli Certifierad"

#: lms/templates/onboarding_header.html:8
msgid "Get Started"
msgstr "Kom Igång"

#. Label of the github (Data) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Github ID"
msgstr "Github ID"

#. Label of the google_meet_link (Data) field in DocType 'LMS Certificate
#. Request'
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
msgid "Google Meet Link"
msgstr "Google Meet Länk"

#. Label of the grade (Data) field in DocType 'Education Detail'
#: frontend/src/components/Assignment.vue:158
#: lms/lms/doctype/education_detail/education_detail.json
msgid "Grade"
msgstr "Betyg"

#. Label of the grade_assignment (Check) field in DocType 'LMS Assignment'
#: lms/lms/doctype/lms_assignment/lms_assignment.json
msgid "Grade Assignment"
msgstr "Betyg Tilldelning"

#. Label of the grade_type (Select) field in DocType 'Education Detail'
#: lms/lms/doctype/education_detail/education_detail.json
msgid "Grade Type"
msgstr "Betyg Typ"

#: frontend/src/components/Assignment.vue:153
msgid "Grading"
msgstr "Betygsättning"

#. Label of the grant_only_once (Check) field in DocType 'LMS Badge'
#: lms/lms/doctype/lms_badge/lms_badge.json
msgid "Grant only once"
msgstr "Bevilja endast en gång"

#: lms/templates/signup-form.html:56
msgid "Have an account? Login"
msgstr "Har konto? Logga in"

#. Label of the headline (Data) field in DocType 'User'
#: frontend/src/components/Modals/EditProfile.vue:69
#: lms/fixtures/custom_field.json
msgid "Headline"
msgstr "Huvudrubrik"

#: lms/lms/widgets/HelloWorld.html:13
msgid "Hello"
msgstr "Hej"

#: frontend/src/components/AppSidebar.vue:128
msgid "Help"
msgstr "Hjälp"

#: lms/templates/courses_created.html:15
msgid "Help others learn something new by creating a course."
msgstr "Hjälp andra att lära sig något nytt genom att skapa kurs."

#: frontend/src/components/BatchFeedback.vue:15
msgid "Help us improve by providing your feedback."
msgstr "Hjälp oss att förbättra oss genom att ge oss din återkoppling."

#: lms/templates/reviews.html:101
msgid "Help us improve our course material."
msgstr "Hjälp oss att förbättra vårt kursmaterial."

#: frontend/src/pages/PersonaForm.vue:16
msgid "Help us understand your needs"
msgstr "Hjälp oss att förstå dina behov"

#: lms/lms/notification/certificate_request_reminder/certificate_request_reminder.html:1
#: lms/templates/emails/certificate_request_notification.html:1
msgid "Hey {0}"
msgstr "Hej {0}"

#: lms/templates/emails/job_report.html:3
msgid "Hey,"
msgstr "Hej,"

#: lms/templates/emails/payment_reminder.html:2
msgid "Hi"
msgstr "Hej"

#: lms/templates/emails/lms_course_interest.html:3
msgid "Hi {0},"
msgstr "Hej {0},"

#: lms/templates/emails/lms_invite_request_approved.html:3
msgid "Hi,"
msgstr "Hej,"

#. Label of the hide_private (Check) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Hide my Private Information from others"
msgstr "Dölj min privata information från andra"

#. Label of the hints (Small Text) field in DocType 'LMS Exercise'
#: lms/lms/doctype/lms_exercise/lms_exercise.json
msgid "Hints"
msgstr "Tips"

#. Label of the host (Link) field in DocType 'LMS Live Class'
#: lms/lms/doctype/lms_live_class/lms_live_class.json
msgid "Host"
msgstr "Värd"

#. Label of the current (Check) field in DocType 'Work Experience'
#: lms/lms/doctype/work_experience/work_experience.json
msgid "I am currently working here"
msgstr "Jag arbetar för närvarande här"

#: lms/templates/emails/certification.html:6
msgid "I am delighted to inform you that you have successfully earned your certification for the {0} course. Congratulations!"
msgstr "Jag är glad att kunna informera dig om att du har fått din certifiering för {0} kurs. Grattis!"

#. Label of the looking_for_job (Check) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "I am looking for a job"
msgstr "Jag söker jobb"

#: frontend/src/pages/ProfileEvaluator.vue:94
msgid "I am unavailable"
msgstr "Jag är inte tillgänglig"

#: frontend/src/pages/QuizForm.vue:338
msgid "ID"
msgstr "ID"

#. Label of the icon (Data) field in DocType 'LMS Sidebar Item'
#: frontend/src/components/Modals/PageModal.vue:28
#: lms/lms/doctype/lms_sidebar_item/lms_sidebar_item.json
msgid "Icon"
msgstr "Ikon"

#. Label of the user_category (Check) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Identify User Category"
msgstr "Identifiera Användarkategori"

#: frontend/src/components/LessonHelp.vue:11
msgid "If Include in Preview is enabled for a lesson then the lesson will also be accessible to non logged in users."
msgstr "Om Inkludera i Förhandsvisning är aktiverat för en lektion då lektionen kommer också att vara tillgänglig för ej inloggade användare."

#: frontend/src/components/Quiz.vue:46
msgid "If you answer incorrectly, {0} {1} will be deducted from your score for each incorrect answer."
msgstr "Om du svarar fel kommer {0} {1} att dras av från dina poäng för varje felaktigt svar."

#: lms/templates/emails/mentor_request_creation_email.html:5
msgid "If you are not any more interested to mentor the course"
msgstr "Om du inte längre är intresserad av att vara mentor för kurs"

#: frontend/src/components/Quiz.vue:23
msgid "If you fail to do so, the quiz will be automatically submitted when the timer ends."
msgstr "Om du inte gör det kommer frågesporten att skickas in automatiskt när tidur stängs."

#: lms/templates/emails/payment_reminder.html:19
msgid "If you have any questions or need assistance, feel free to reach out to our support team."
msgstr "Om du har några frågor eller behöver hjälp är du välkommen att kontakta vårt supportteam."

#: lms/templates/emails/batch_confirmation.html:29
#: lms/templates/emails/batch_start_reminder.html:27
#: lms/templates/emails/live_class_reminder.html:24
msgid "If you have any questions or require assistance, feel free to contact us."
msgstr "Om du har några frågor eller behöver hjälp är du välkommen att kontakta oss."

#. Description of the 'Amount (USD)' (Currency) field in DocType 'LMS Batch'
#. Description of the 'Amount (USD)' (Currency) field in DocType 'LMS Course'
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_course/lms_course.json
msgid "If you set an amount here, then the USD equivalent setting will not get applied."
msgstr "Om du anger belopp här kommer motsvarande USD inställning inte att tillämpas."

#: lms/lms/doctype/lms_quiz/lms_quiz.py:66
msgid "If you want open ended questions then make sure each question in the quiz is of open ended type."
msgstr "Om du vill ha öppna frågor ska du se till att varje fråga i frågesporten är av typen öppna frågor."

#. Option for the 'File Type' (Select) field in DocType 'Course Lesson'
#. Label of the image (Code) field in DocType 'Exercise Latest Submission'
#. Label of the image (Code) field in DocType 'Exercise Submission'
#. Option for the 'Type' (Select) field in DocType 'LMS Assignment'
#. Option for the 'Type' (Select) field in DocType 'LMS Assignment Submission'
#. Label of the image (Attach Image) field in DocType 'LMS Badge'
#. Label of the image (Code) field in DocType 'LMS Exercise'
#: lms/lms/doctype/course_lesson/course_lesson.json
#: lms/lms/doctype/exercise_latest_submission/exercise_latest_submission.json
#: lms/lms/doctype/exercise_submission/exercise_submission.json
#: lms/lms/doctype/lms_assignment/lms_assignment.json
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
#: lms/lms/doctype/lms_badge/lms_badge.json
#: lms/lms/doctype/lms_exercise/lms_exercise.json
msgid "Image"
msgstr "Bild"

#: frontend/src/components/Modals/EditCoverImage.vue:58
#: frontend/src/components/UnsplashImageBrowser.vue:52
msgid "Image search powered by"
msgstr "Bildsökning drivs av"

#: lms/lms/doctype/lms_quiz/lms_quiz.py:232
msgid "Image: Corrupted Data Stream"
msgstr "Bild: Skadad Dataström"

#. Option for the 'Stage' (Select) field in DocType 'LMS Batch Old'
#. Option for the 'Status' (Select) field in DocType 'LMS Certificate
#. Evaluation'
#. Option for the 'Status' (Select) field in DocType 'LMS Course'
#: frontend/src/components/Modals/Event.vue:358
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
#: lms/lms/doctype/lms_course/lms_course.json
msgid "In Progress"
msgstr "Pågående"

#. Option for the 'Status' (Select) field in DocType 'LMS Batch Old'
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
msgid "Inactive"
msgstr "Inaktiv"

#. Label of the include_in_preview (Check) field in DocType 'Course Lesson'
#: lms/lms/doctype/course_lesson/course_lesson.json
msgid "Include In Preview"
msgstr "Inkludera i Förhandsgranskning"

#. Option for the 'Status' (Select) field in DocType 'LMS Course Progress'
#: lms/lms/doctype/lms_course_progress/lms_course_progress.json
msgid "Incomplete"
msgstr "Ej komplett"

#. Option for the 'Status' (Select) field in DocType 'Exercise Latest
#. Submission'
#. Option for the 'Status' (Select) field in DocType 'Exercise Submission'
#: frontend/src/components/Quiz.vue:194
#: lms/lms/doctype/exercise_latest_submission/exercise_latest_submission.json
#: lms/lms/doctype/exercise_submission/exercise_submission.json
msgid "Incorrect"
msgstr "Inkorrekt"

#. Label of the index_ (Int) field in DocType 'LMS Exercise'
#: lms/lms/doctype/lms_exercise/lms_exercise.json
msgid "Index"
msgstr "Index"

#. Label of the index_label (Data) field in DocType 'LMS Exercise'
#: lms/lms/doctype/lms_exercise/lms_exercise.json
msgid "Index Label"
msgstr "Index Etikett"

#. Option for the 'Collaboration Preference' (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Individual Work"
msgstr "Individuellt Arbete"

#. Name of a DocType
#. Label of the industry (Data) field in DocType 'Industry'
#. Label of the industry (Link) field in DocType 'Preferred Industry'
#: lms/lms/doctype/industry/industry.json
#: lms/lms/doctype/preferred_industry/preferred_industry.json
msgid "Industry"
msgstr "Industri"

#. Label of the input (Data) field in DocType 'LMS Test Case'
#. Label of the input (Data) field in DocType 'LMS Test Case Submission'
#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseSubmission.vue:113
#: lms/lms/doctype/lms_test_case/lms_test_case.json
#: lms/lms/doctype/lms_test_case_submission/lms_test_case_submission.json
msgid "Input"
msgstr "Indata"

#. Label of the institution_name (Data) field in DocType 'Education Detail'
#: lms/lms/doctype/education_detail/education_detail.json
msgid "Institution Name"
msgstr "Institution Namn"

#. Label of the instructor (Link) field in DocType 'Cohort'
#. Label of the instructor (Link) field in DocType 'Course Instructor'
#: lms/lms/doctype/cohort/cohort.json
#: lms/lms/doctype/course_instructor/course_instructor.json
msgid "Instructor"
msgstr "Lärare"

#. Label of the instructor_content (Text) field in DocType 'Course Lesson'
#: lms/lms/doctype/course_lesson/course_lesson.json
msgid "Instructor Content"
msgstr "Lärares Innehåll"

#. Label of the instructor_notes (Markdown Editor) field in DocType 'Course
#. Lesson'
#: frontend/src/pages/Lesson.vue:184 frontend/src/pages/LessonForm.vue:42
#: lms/lms/doctype/course_lesson/course_lesson.json
msgid "Instructor Notes"
msgstr "Lärare Anteckningar"

#. Label of the instructors (Table MultiSelect) field in DocType 'LMS Batch'
#. Label of the instructors (Rating) field in DocType 'LMS Batch Feedback'
#. Label of the instructors (Table MultiSelect) field in DocType 'LMS Course'
#: frontend/src/pages/BatchForm.vue:27 frontend/src/pages/CourseForm.vue:44
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_batch_feedback/lms_batch_feedback.json
#: lms/lms/doctype/lms_course/lms_course.json
msgid "Instructors"
msgstr "Lärare"

#: lms/templates/assignment.html:17
msgid "Instructors Comments"
msgstr "Lärare Kommentarer"

#. Label of a Link in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "Interest"
msgstr "Intresse"

#: frontend/src/components/AppSidebar.vue:556
#: frontend/src/components/AppSidebar.vue:559
msgid "Introduction"
msgstr "Introduktion"

#: lms/lms/doctype/invite_request/invite_request.py:83
msgid "Invalid Invite Code."
msgstr "Ogiltig Inbjudan Kod."

#: lms/lms/doctype/course_lesson/course_lesson.py:20
msgid "Invalid Quiz ID"
msgstr "Ogiltigt Frågesport ID"

#: lms/lms/doctype/course_lesson/course_lesson.py:34
msgid "Invalid Quiz ID in content"
msgstr "Ogiltigt Frågesport ID"

#. Label of the invite_code (Data) field in DocType 'Cohort Subgroup'
#. Label of the invite_code (Data) field in DocType 'Invite Request'
#: lms/lms/doctype/cohort_subgroup/cohort_subgroup.json
#: lms/lms/doctype/invite_request/invite_request.json
msgid "Invite Code"
msgstr "Inbjudningskod"

#. Label of the invite_email (Data) field in DocType 'Invite Request'
#: lms/lms/doctype/invite_request/invite_request.json
msgid "Invite Email"
msgstr "E-post Inbjudan"

#. Option for the 'Membership' (Select) field in DocType 'LMS Batch Old'
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
msgid "Invite Only"
msgstr "Endast inbjudan"

#. Name of a DocType
#: lms/lms/doctype/invite_request/invite_request.json
msgid "Invite Request"
msgstr "Inbjudan Begäran"

#: frontend/src/components/AppSidebar.vue:490
msgid "Invite your team and students"
msgstr "Bjud in ditt team och dina studenter"

#. Label of the is_correct (Check) field in DocType 'LMS Option'
#. Label of the is_correct_1 (Check) field in DocType 'LMS Question'
#. Label of the is_correct_2 (Check) field in DocType 'LMS Question'
#. Label of the is_correct_3 (Check) field in DocType 'LMS Question'
#. Label of the is_correct_4 (Check) field in DocType 'LMS Question'
#. Label of the is_correct (Check) field in DocType 'LMS Quiz Result'
#: lms/lms/doctype/lms_option/lms_option.json
#: lms/lms/doctype/lms_question/lms_question.json
#: lms/lms/doctype/lms_quiz_result/lms_quiz_result.json
msgid "Is Correct"
msgstr "Är korrekt"

#. Label of the is_scorm_package (Check) field in DocType 'Course Chapter'
#. Label of the is_scorm_package (Check) field in DocType 'Course Lesson'
#: lms/lms/doctype/course_chapter/course_chapter.json
#: lms/lms/doctype/course_lesson/course_lesson.json
msgid "Is SCORM Package"
msgstr "Är SCORM App"

#. Label of the issue_date (Date) field in DocType 'Certification'
#. Label of the issue_date (Date) field in DocType 'LMS Certificate'
#: frontend/src/components/Modals/BulkCertificates.vue:28
#: frontend/src/components/Modals/Event.vue:121
#: lms/lms/doctype/certification/certification.json
#: lms/lms/doctype/lms_certificate/lms_certificate.json
msgid "Issue Date"
msgstr "Utfärdande Datum"

#: frontend/src/components/AppSidebar.vue:592
msgid "Issue a Certificate"
msgstr "Utfärda Certifikat"

#. Label of the issued_on (Date) field in DocType 'LMS Badge Assignment'
#: frontend/src/pages/CourseCertification.vue:27
#: lms/lms/doctype/lms_badge_assignment/lms_badge_assignment.json
msgid "Issued On"
msgstr "Utfärdad"

#: frontend/src/pages/ProfileAbout.vue:56
#: frontend/src/pages/ProfileCertificates.vue:17
#: lms/templates/certificates_section.html:11
msgid "Issued on"
msgstr "Utfärdad"

#. Label of the items_in_sidebar_section (Section Break) field in DocType 'LMS
#. Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Items in Sidebar"
msgstr "Artiklar i Sidofält"

#: frontend/src/pages/ProgramForm.vue:277
msgid "Items removed successfully"
msgstr "Artiklar borttagna"

#: lms/templates/signup-form.html:6
msgid "Jane Doe"
msgstr "Okänd Person"

#. Option for the 'Language' (Select) field in DocType 'LMS Programming
#. Exercise'
#: lms/lms/doctype/lms_programming_exercise/lms_programming_exercise.json
msgid "JavaScript"
msgstr "JavaScript"

#. Label of the job (Link) field in DocType 'LMS Job Application'
#: lms/job/doctype/lms_job_application/lms_job_application.json
msgid "Job"
msgstr "Jobb"

#. Label of the subtitle (Data) field in DocType 'Job Settings'
#: lms/job/doctype/job_settings/job_settings.json
msgid "Job Board Subtitle"
msgstr "Jobb Styrelse Underbenämning"

#. Label of the title (Data) field in DocType 'Job Settings'
#: lms/job/doctype/job_settings/job_settings.json
msgid "Job Board Title"
msgstr "Jobb Styrelse Benämning"

#: frontend/src/pages/JobForm.vue:14
msgid "Job Details"
msgstr "Jobb Detaljer"

#: lms/www/lms.py:176
msgid "Job Openings"
msgstr "Jobb Erbjudande"

#. Name of a DocType
#: lms/job/doctype/job_opportunity/job_opportunity.json
msgid "Job Opportunity"
msgstr "Jobb Möjlighet"

#. Name of a DocType
#: lms/job/doctype/job_settings/job_settings.json
msgid "Job Settings"
msgstr "Jobb Inställningar"

#. Label of the job_title (Data) field in DocType 'Job Opportunity'
#. Label of the job_title (Data) field in DocType 'LMS Job Application'
#: lms/job/doctype/job_opportunity/job_opportunity.json
#: lms/job/doctype/lms_job_application/lms_job_application.json
msgid "Job Title"
msgstr "Jobb Benämning"

#. Label of the jobs (Check) field in DocType 'LMS Settings'
#: frontend/src/pages/JobDetail.vue:10 frontend/src/pages/Jobs.vue:8
#: frontend/src/pages/Jobs.vue:185
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Jobs"
msgstr "Jobb"

#: frontend/src/components/LiveClass.vue:78
#: lms/templates/upcoming_evals.html:15
msgid "Join"
msgstr "Anslut"

#: frontend/src/components/UpcomingEvaluations.vue:90
msgid "Join Call"
msgstr "Delta i Samtal"

#: frontend/src/components/Modals/Event.vue:74
msgid "Join Meeting"
msgstr "Delta i Möte"

#. Label of the join_url (Small Text) field in DocType 'LMS Live Class'
#: lms/lms/doctype/lms_live_class/lms_live_class.json
msgid "Join URL"
msgstr "Gå med URL"

#. Label of the joined_at (Datetime) field in DocType 'LMS Live Class
#. Participant'
#: lms/lms/doctype/lms_live_class_participant/lms_live_class_participant.json
msgid "Joined At"
msgstr "Ansluten"

#: frontend/src/components/Modals/LiveClassAttendance.vue:18
msgid "Joined at"
msgstr "Anslöt"

#. Name of a Workspace
#: lms/lms/workspace/lms/lms.json
msgid "LMS"
msgstr "LMS"

#. Name of a DocType
#: lms/lms/doctype/lms_assessment/lms_assessment.json
msgid "LMS Assessment"
msgstr "Bedömning"

#. Name of a DocType
#: lms/lms/doctype/lms_assignment/lms_assignment.json
msgid "LMS Assignment"
msgstr "Tilldelning"

#. Name of a DocType
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
msgid "LMS Assignment Submission"
msgstr "Tilldelning Godkännande"

#. Name of a DocType
#: lms/lms/doctype/lms_badge/lms_badge.json
msgid "LMS Badge"
msgstr "Emblem"

#. Name of a DocType
#: lms/lms/doctype/lms_badge_assignment/lms_badge_assignment.json
msgid "LMS Badge Assignment"
msgstr "Emblem Tilldelning"

#. Name of a DocType
#. Option for the 'Payment for Document Type' (Select) field in DocType 'LMS
#. Payment'
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_payment/lms_payment.json
msgid "LMS Batch"
msgstr "Grupp"

#. Name of a DocType
#: lms/lms/doctype/lms_batch_enrollment/lms_batch_enrollment.json
msgid "LMS Batch Enrollment"
msgstr "Grupp Registrering"

#. Name of a DocType
#: lms/lms/doctype/lms_batch_feedback/lms_batch_feedback.json
msgid "LMS Batch Feedback"
msgstr "Grupp Återkoppling"

#. Name of a DocType
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
msgid "LMS Batch Old"
msgstr "Gammal Grupp"

#. Name of a DocType
#: lms/lms/doctype/lms_batch_timetable/lms_batch_timetable.json
msgid "LMS Batch Timetable"
msgstr "Grupp Tidtabell"

#. Name of a DocType
#: lms/lms/doctype/lms_category/lms_category.json
msgid "LMS Category"
msgstr "Kategori"

#. Name of a DocType
#: lms/lms/doctype/lms_certificate/lms_certificate.json
msgid "LMS Certificate"
msgstr "Certifikat"

#. Name of a DocType
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
msgid "LMS Certificate Evaluation"
msgstr "Certifikat Utvärdering"

#. Name of a DocType
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
msgid "LMS Certificate Request"
msgstr "Certifikat Begäran"

#. Name of a DocType
#. Option for the 'Payment for Document Type' (Select) field in DocType 'LMS
#. Payment'
#: lms/lms/doctype/lms_course/lms_course.json
#: lms/lms/doctype/lms_payment/lms_payment.json
msgid "LMS Course"
msgstr "Kurs"

#. Name of a DocType
#: lms/lms/doctype/lms_course_interest/lms_course_interest.json
msgid "LMS Course Interest"
msgstr "Kurs Intresse"

#. Name of a DocType
#: lms/lms/doctype/lms_course_mentor_mapping/lms_course_mentor_mapping.json
msgid "LMS Course Mentor Mapping"
msgstr "LMS Kurs Mentor Tilldelning"

#. Name of a DocType
#: lms/lms/doctype/lms_course_progress/lms_course_progress.json
msgid "LMS Course Progress"
msgstr "Kurs Framsteg"

#. Name of a DocType
#: lms/lms/doctype/lms_course_review/lms_course_review.json
msgid "LMS Course Review"
msgstr "LMS Kurs Granskning"

#. Name of a DocType
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
msgid "LMS Enrollment"
msgstr "Inskrivning"

#. Name of a DocType
#: lms/lms/doctype/lms_exercise/lms_exercise.json
msgid "LMS Exercise"
msgstr "Övning"

#. Name of a DocType
#: lms/job/doctype/lms_job_application/lms_job_application.json
msgid "LMS Job Application"
msgstr "Jobb Ansökan"

#. Name of a DocType
#: lms/lms/doctype/lms_live_class/lms_live_class.json
msgid "LMS Live Class"
msgstr "Live Klass"

#. Name of a DocType
#: lms/lms/doctype/lms_live_class_participant/lms_live_class_participant.json
msgid "LMS Live Class Participant"
msgstr "Deltagare i Live Lektion"

#. Name of a DocType
#: lms/lms/doctype/lms_mentor_request/lms_mentor_request.json
msgid "LMS Mentor Request"
msgstr "Mentor Begäran"

#. Name of a DocType
#: lms/lms/doctype/lms_option/lms_option.json
msgid "LMS Option"
msgstr "Alternativ"

#. Name of a DocType
#: lms/lms/doctype/lms_payment/lms_payment.json
msgid "LMS Payment"
msgstr "Betalning"

#. Name of a DocType
#: lms/lms/doctype/lms_program/lms_program.json
msgid "LMS Program"
msgstr "Program"

#. Name of a DocType
#: lms/lms/doctype/lms_program_course/lms_program_course.json
msgid "LMS Program Course"
msgstr "Program Kurs"

#. Name of a DocType
#: lms/lms/doctype/lms_program_member/lms_program_member.json
msgid "LMS Program Member"
msgstr "Program Medlem"

#. Name of a DocType
#: lms/lms/doctype/lms_programming_exercise/lms_programming_exercise.json
msgid "LMS Programming Exercise"
msgstr "Programmeringsövning"

#. Name of a DocType
#: lms/lms/doctype/lms_programming_exercise_submission/lms_programming_exercise_submission.json
msgid "LMS Programming Exercise Submission"
msgstr "Programmeringsövning Inlämning"

#. Name of a DocType
#: lms/lms/doctype/lms_question/lms_question.json
msgid "LMS Question"
msgstr "Fråga"

#. Name of a DocType
#: lms/lms/doctype/lms_quiz/lms_quiz.json
msgid "LMS Quiz"
msgstr "Frågesport"

#. Name of a DocType
#: lms/lms/doctype/lms_quiz_question/lms_quiz_question.json
msgid "LMS Quiz Question"
msgstr "Frågesport Fråga"

#. Name of a DocType
#: lms/lms/doctype/lms_quiz_result/lms_quiz_result.json
msgid "LMS Quiz Result"
msgstr "Frågesport Resultat"

#. Name of a DocType
#: lms/lms/doctype/lms_quiz_submission/lms_quiz_submission.json
msgid "LMS Quiz Submission"
msgstr "Frågesport Inlämning"

#. Name of a DocType
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "LMS Settings"
msgstr "Inställningar"

#. Name of a DocType
#: lms/lms/doctype/lms_sidebar_item/lms_sidebar_item.json
msgid "LMS Sidebar Item"
msgstr "Sidofält Post"

#. Name of a DocType
#: lms/lms/doctype/lms_source/lms_source.json
msgid "LMS Source"
msgstr "Källa"

#. Name of a role
#: lms/job/doctype/job_opportunity/job_opportunity.json
#: lms/job/doctype/lms_job_application/lms_job_application.json
#: lms/lms/doctype/cohort_join_request/cohort_join_request.json
#: lms/lms/doctype/course_chapter/course_chapter.json
#: lms/lms/doctype/function/function.json
#: lms/lms/doctype/industry/industry.json
#: lms/lms/doctype/lms_assignment/lms_assignment.json
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
#: lms/lms/doctype/lms_badge_assignment/lms_badge_assignment.json
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_batch_enrollment/lms_batch_enrollment.json
#: lms/lms/doctype/lms_batch_feedback/lms_batch_feedback.json
#: lms/lms/doctype/lms_certificate/lms_certificate.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
#: lms/lms/doctype/lms_course_progress/lms_course_progress.json
#: lms/lms/doctype/lms_course_review/lms_course_review.json
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
#: lms/lms/doctype/lms_live_class/lms_live_class.json
#: lms/lms/doctype/lms_programming_exercise/lms_programming_exercise.json
#: lms/lms/doctype/lms_programming_exercise_submission/lms_programming_exercise_submission.json
#: lms/lms/doctype/lms_quiz/lms_quiz.json
#: lms/lms/doctype/lms_quiz_submission/lms_quiz_submission.json
#: lms/lms/doctype/lms_settings/lms_settings.json
#: lms/lms/doctype/lms_source/lms_source.json
#: lms/lms/doctype/lms_video_watch_duration/lms_video_watch_duration.json
#: lms/lms/doctype/user_skill/user_skill.json
msgid "LMS Student"
msgstr "Student"

#. Name of a DocType
#: lms/lms/doctype/lms_test_case/lms_test_case.json
msgid "LMS Test Case"
msgstr "Testfall"

#. Name of a DocType
#: lms/lms/doctype/lms_test_case_submission/lms_test_case_submission.json
msgid "LMS Test Case Submission"
msgstr "Testfall Inlämning"

#. Name of a DocType
#: lms/lms/doctype/lms_timetable_legend/lms_timetable_legend.json
msgid "LMS Timetable Legend"
msgstr "Tidtabell Legend"

#. Name of a DocType
#: lms/lms/doctype/lms_timetable_template/lms_timetable_template.json
msgid "LMS Timetable Template"
msgstr "Tidtabell Mall"

#. Name of a DocType
#: lms/lms/doctype/lms_video_watch_duration/lms_video_watch_duration.json
msgid "LMS Video Watch Duration"
msgstr "Videovisningstid"

#. Name of a DocType
#: lms/lms/doctype/lms_zoom_settings/lms_zoom_settings.json
msgid "LMS Zoom Settings"
msgstr "Zoom Inställningar"

#. Label of the label (Data) field in DocType 'LMS Timetable Legend'
#: lms/lms/doctype/lms_timetable_legend/lms_timetable_legend.json
msgid "Label"
msgstr "Etikett"

#. Label of the language (Select) field in DocType 'LMS Programming Exercise'
#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseForm.vue:22
#: lms/lms/doctype/lms_programming_exercise/lms_programming_exercise.json
msgid "Language"
msgstr "Språk"

#: frontend/src/components/Modals/EditProfile.vue:64
msgid "Last Name"
msgstr "Efternamn"

#. Label of the latest_submission (Link) field in DocType 'Exercise Latest
#. Submission'
#: lms/lms/doctype/exercise_latest_submission/exercise_latest_submission.json
msgid "Latest Submission"
msgstr "Senaste Inlämning"

#. Label of the launch_file (Code) field in DocType 'Course Chapter'
#: lms/lms/doctype/course_chapter/course_chapter.json
msgid "Launch File"
msgstr "Startfil"

#. Label of the left_at (Datetime) field in DocType 'LMS Live Class
#. Participant'
#: lms/lms/doctype/lms_live_class_participant/lms_live_class_participant.json
msgid "Left At"
msgstr "Lämnade"

#: frontend/src/components/Modals/LiveClassAttendance.vue:21
msgid "Left at"
msgstr "Lämnade"

#. Label of the lesson (Link) field in DocType 'Exercise Latest Submission'
#. Label of the lesson (Link) field in DocType 'Exercise Submission'
#. Label of the lesson (Link) field in DocType 'Lesson Reference'
#. Label of the lesson (Link) field in DocType 'LMS Assignment Submission'
#. Label of the lesson (Link) field in DocType 'LMS Course Progress'
#. Label of the lesson (Link) field in DocType 'LMS Exercise'
#. Label of the lesson (Link) field in DocType 'LMS Quiz'
#. Label of the lesson (Link) field in DocType 'LMS Video Watch Duration'
#. Label of the lesson (Link) field in DocType 'Scheduled Flow'
#. Label of a Link in the LMS Workspace
#: lms/lms/doctype/exercise_latest_submission/exercise_latest_submission.json
#: lms/lms/doctype/exercise_submission/exercise_submission.json
#: lms/lms/doctype/lesson_reference/lesson_reference.json
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
#: lms/lms/doctype/lms_course_progress/lms_course_progress.json
#: lms/lms/doctype/lms_exercise/lms_exercise.json
#: lms/lms/doctype/lms_quiz/lms_quiz.json
#: lms/lms/doctype/lms_video_watch_duration/lms_video_watch_duration.json
#: lms/lms/doctype/scheduled_flow/scheduled_flow.json
#: lms/lms/workspace/lms/lms.json
msgid "Lesson"
msgstr "Lektion"

#. Name of a DocType
#: lms/lms/doctype/lesson_reference/lesson_reference.json
msgid "Lesson Reference"
msgstr "Lektion Referens"

#. Label of the lesson_title (Data) field in DocType 'Scheduled Flow'
#: lms/lms/doctype/scheduled_flow/scheduled_flow.json
msgid "Lesson Title"
msgstr "Lektion Benämning"

#: frontend/src/pages/LessonForm.vue:426
msgid "Lesson created successfully"
msgstr "Lektion skapad"

#: frontend/src/components/CourseOutline.vue:226
msgid "Lesson deleted successfully"
msgstr "Lektion raderad"

#: frontend/src/components/CourseOutline.vue:241
msgid "Lesson moved successfully"
msgstr "Lektion flyttad"

#: frontend/src/pages/LessonForm.vue:450
msgid "Lesson updated successfully"
msgstr "Lektion uppdaterad"

#. Label of the lessons (Table) field in DocType 'Course Chapter'
#. Group in Course Chapter's connections
#. Label of the lessons (Int) field in DocType 'LMS Course'
#: frontend/src/components/CourseCard.vue:37
#: frontend/src/components/CourseCardOverlay.vue:131
#: lms/lms/doctype/course_chapter/course_chapter.json
#: lms/lms/doctype/lms_course/lms_course.json
msgid "Lessons"
msgstr "Lektioner"

#: lms/lms/web_template/lms_statistics/lms_statistics.html:14
#: lms/templates/statistics.html:36
msgid "Lessons Completed"
msgstr "Klara Lektioner"

#: lms/templates/onboarding_header.html:11
msgid "Lets start setting up your content on the LMS so that you can reclaim time and focus on growth."
msgstr "Låt oss börja konfigurera ditt innehåll på LMS så att du kan återfå tid och fokusera på tillväxt."

#. Option for the 'Grade Type' (Select) field in DocType 'Education Detail'
#: lms/lms/doctype/education_detail/education_detail.json
msgid "Letter Grade (e.g. A, B-)"
msgstr "Bokstavsbetyg (t.ex. A, B-)"

#. Label of the limit_questions_to (Int) field in DocType 'LMS Quiz'
#: frontend/src/pages/QuizForm.vue:110 lms/lms/doctype/lms_quiz/lms_quiz.json
msgid "Limit Questions To"
msgstr "Begränsa frågor till"

#: lms/lms/doctype/lms_quiz/lms_quiz.py:38
msgid "Limit cannot be greater than or equal to the number of questions in the quiz."
msgstr "Gränsen kan inte vara större än eller lika med antalet frågor i frågesport."

#: frontend/src/pages/ProfileAbout.vue:74
msgid "LinkedIn"
msgstr "LinkedIn"

#. Label of the linkedin (Data) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "LinkedIn ID"
msgstr "LinkedIn ID"

#. Group in Cohort's connections
#. Group in Cohort Subgroup's connections
#: lms/lms/doctype/cohort/cohort.json
#: lms/lms/doctype/cohort_subgroup/cohort_subgroup.json
msgid "Links"
msgstr "Länkar"

#. Option for the 'Status' (Select) field in DocType 'Cohort'
#: frontend/src/pages/Courses.vue:307 lms/lms/doctype/cohort/cohort.json
msgid "Live"
msgstr "Live"

#. Label of the live_class (Link) field in DocType 'LMS Live Class Participant'
#. Label of the show_live_class (Check) field in DocType 'LMS Settings'
#: frontend/src/components/LiveClass.vue:14
#: lms/lms/doctype/lms_live_class_participant/lms_live_class_participant.json
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Live Class"
msgstr "Live Klass"

#. Label of the livecode_url (Data) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "LiveCode URL"
msgstr "LiveCode URL"

#: frontend/src/components/Modals/CourseProgressSummary.vue:87
#: frontend/src/components/Settings/Evaluators.vue:81
#: frontend/src/components/Settings/Members.vue:79
#: frontend/src/pages/Assignments.vue:66 frontend/src/pages/Batches.vue:80
#: frontend/src/pages/CertifiedParticipants.vue:98
#: frontend/src/pages/Courses.vue:75
#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseSubmissions.vue:129
#: frontend/src/pages/ProgrammingExercises/ProgrammingExercises.vue:87
#: frontend/src/pages/QuizSubmissionList.vue:39
#: frontend/src/pages/Quizzes.vue:94
msgid "Load More"
msgstr "Ladda Mer"

#. Option for the 'Auto Recording' (Select) field in DocType 'LMS Live Class'
#: lms/lms/doctype/lms_live_class/lms_live_class.json
msgid "Local"
msgstr "Lokal"

#. Label of the location (Data) field in DocType 'Education Detail'
#. Label of the location (Data) field in DocType 'Work Experience'
#: lms/lms/doctype/education_detail/education_detail.json
#: lms/lms/doctype/work_experience/work_experience.json
msgid "Location"
msgstr "Plats"

#. Label of the location_preference (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Location Preference"
msgstr "Platspreferens"

#: frontend/src/components/NoPermission.vue:28
#: frontend/src/components/QuizBlock.vue:9 frontend/src/pages/Batch.vue:196
#: frontend/src/pages/Lesson.vue:59
msgid "Login"
msgstr "Logga In"

#: frontend/src/components/UserDropdown.vue:174
msgid "Login to Frappe Cloud?"
msgstr "Logga in på Frappe Cloud?"

#: frontend/src/pages/JobDetail.vue:63
msgid "Login to apply"
msgstr "Logga in för att ansöka"

#: lms/templates/emails/payment_reminder.html:23
msgid "Looking forward to seeing you enrolled!"
msgstr "Ser fram emot att se dig registrerad!"

#. Label of the default_home (Check) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Make LMS the default home"
msgstr "LMS som Standard Sida"

#: frontend/src/components/Modals/AnnouncementModal.vue:5
#: frontend/src/pages/Batch.vue:16
msgid "Make an Announcement"
msgstr "Skapa Meddelande"

#: frontend/src/pages/Billing.vue:123
msgid "Make sure to enter the correct billing name as the same will be used in your invoice."
msgstr "Ange rätt faktura adress eftersom det kommer att användas på din faktura."

#: frontend/src/components/BatchOverlay.vue:69
msgid "Manage Batch"
msgstr "Hantera Grupp"

#. Option for the 'Role' (Select) field in DocType 'Cohort Staff'
#: lms/lms/doctype/cohort_staff/cohort_staff.json
msgid "Manager"
msgstr "Ansvarig"

#. Option for the 'User Category' (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json lms/templates/signup-form.html:24
msgid "Manager (Sales/Marketing/Customer)"
msgstr "Ansvarig (Försäljning/Marknadsföring/Kund)"

#. Label of the manifest_file (Code) field in DocType 'Course Chapter'
#: lms/lms/doctype/course_chapter/course_chapter.json
msgid "Manifest File"
msgstr "Manifestfil"

#: frontend/src/components/Quiz.vue:120
msgid "Mark"
msgstr "Markera"

#: frontend/src/pages/Notifications.vue:12
msgid "Mark all as read"
msgstr "Markera alla som lästa"

#. Label of the marks (Int) field in DocType 'LMS Quiz Question'
#. Label of the marks (Int) field in DocType 'LMS Quiz Result'
#: frontend/src/components/Modals/Question.vue:40
#: frontend/src/components/Modals/Question.vue:106
#: frontend/src/components/Quiz.vue:120 frontend/src/pages/QuizForm.vue:348
#: frontend/src/pages/QuizSubmission.vue:64
#: lms/lms/doctype/lms_quiz_question/lms_quiz_question.json
#: lms/lms/doctype/lms_quiz_result/lms_quiz_result.json
#: lms/templates/quiz/quiz.html:59
msgid "Marks"
msgstr "Märken"

#. Label of the marks_to_cut (Int) field in DocType 'LMS Quiz'
#: lms/lms/doctype/lms_quiz/lms_quiz.json
msgid "Marks To Cut"
msgstr "Markera att klippa"

#: lms/lms/doctype/lms_quiz_submission/lms_quiz_submission.py:41
msgid "Marks for question number {0} cannot be greater than the marks allotted for that question."
msgstr "Poängen för fråga nummer {0} får inte vara högre än de poäng som tilldelats för denna fråga."

#. Label of the marks_out_of (Int) field in DocType 'LMS Quiz Result'
#: frontend/src/pages/QuizSubmission.vue:67
#: lms/lms/doctype/lms_quiz_result/lms_quiz_result.json
msgid "Marks out of"
msgstr "Poäng utav"

#: frontend/src/pages/QuizForm.vue:122
msgid "Marks to Cut"
msgstr "Markeringar att klippa"

#. Label of the max_attempts (Int) field in DocType 'LMS Quiz'
#: frontend/src/pages/Quizzes.vue:249 lms/lms/doctype/lms_quiz/lms_quiz.json
msgid "Max Attempts"
msgstr "Maximalt antal försök"

#: frontend/src/pages/QuizForm.vue:62
msgid "Maximum Attempts"
msgstr "Maximalt Antal Försök"

#. Label of the medium (Select) field in DocType 'LMS Batch'
#: frontend/src/pages/BatchForm.vue:187
#: lms/lms/doctype/lms_batch/lms_batch.json
msgid "Medium"
msgstr "Medium"

#. Label of the medium (Data) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Medium ID"
msgstr "Medium ID"

#: lms/templates/emails/batch_confirmation.html:16
#: lms/templates/emails/batch_start_reminder.html:19
msgid "Medium:"
msgstr "Medium:"

#. Label of the meeting_id (Data) field in DocType 'LMS Live Class'
#: lms/lms/doctype/lms_live_class/lms_live_class.json
msgid "Meeting ID"
msgstr "Mötes ID"

#. Label of the member (Link) field in DocType 'Exercise Latest Submission'
#. Label of the member (Link) field in DocType 'Exercise Submission'
#. Label of the member (Link) field in DocType 'LMS Assignment Submission'
#. Label of the member (Link) field in DocType 'LMS Badge Assignment'
#. Label of the member (Link) field in DocType 'LMS Batch Enrollment'
#. Label of the member (Link) field in DocType 'LMS Batch Feedback'
#. Label of the member (Link) field in DocType 'LMS Certificate'
#. Label of the member (Link) field in DocType 'LMS Certificate Evaluation'
#. Label of the member (Link) field in DocType 'LMS Certificate Request'
#. Label of the member (Link) field in DocType 'LMS Course Progress'
#. Label of the member (Link) field in DocType 'LMS Enrollment'
#. Option for the 'Role' (Select) field in DocType 'LMS Enrollment'
#. Label of the member (Link) field in DocType 'LMS Live Class Participant'
#. Label of the member (Link) field in DocType 'LMS Mentor Request'
#. Label of the member (Link) field in DocType 'LMS Payment'
#. Label of the member (Link) field in DocType 'LMS Program Member'
#. Label of the member (Link) field in DocType 'LMS Programming Exercise
#. Submission'
#. Label of the member (Link) field in DocType 'LMS Quiz Submission'
#. Label of the member (Link) field in DocType 'LMS Video Watch Duration'
#. Label of the member (Link) field in DocType 'LMS Zoom Settings'
#: frontend/src/components/Modals/CourseProgressSummary.vue:207
#: frontend/src/components/Modals/LiveClassAttendance.vue:14
#: frontend/src/components/Modals/VideoStatistics.vue:22
#: frontend/src/components/Modals/ZoomAccountModal.vue:42
#: frontend/src/components/Settings/ZoomSettings.vue:178
#: frontend/src/pages/AssignmentSubmissionList.vue:14
#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseSubmissions.vue:268
#: frontend/src/pages/QuizSubmission.vue:31
#: frontend/src/pages/QuizSubmissionList.vue:91
#: lms/lms/doctype/exercise_latest_submission/exercise_latest_submission.json
#: lms/lms/doctype/exercise_submission/exercise_submission.json
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
#: lms/lms/doctype/lms_badge_assignment/lms_badge_assignment.json
#: lms/lms/doctype/lms_batch_enrollment/lms_batch_enrollment.json
#: lms/lms/doctype/lms_batch_feedback/lms_batch_feedback.json
#: lms/lms/doctype/lms_certificate/lms_certificate.json
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
#: lms/lms/doctype/lms_course_progress/lms_course_progress.json
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
#: lms/lms/doctype/lms_live_class_participant/lms_live_class_participant.json
#: lms/lms/doctype/lms_mentor_request/lms_mentor_request.json
#: lms/lms/doctype/lms_payment/lms_payment.json
#: lms/lms/doctype/lms_program_member/lms_program_member.json
#: lms/lms/doctype/lms_programming_exercise_submission/lms_programming_exercise_submission.json
#: lms/lms/doctype/lms_quiz_submission/lms_quiz_submission.json
#: lms/lms/doctype/lms_video_watch_duration/lms_video_watch_duration.json
#: lms/lms/doctype/lms_zoom_settings/lms_zoom_settings.json
#: lms/lms/report/course_progress_summary/course_progress_summary.py:64
msgid "Member"
msgstr "Medlem"

#. Label of the member_cohort (Link) field in DocType 'Exercise Latest
#. Submission'
#: lms/lms/doctype/exercise_latest_submission/exercise_latest_submission.json
msgid "Member Cohort"
msgstr "Medlem Kohort"

#. Label of the member_email (Link) field in DocType 'Exercise Latest
#. Submission'
#: lms/lms/doctype/exercise_latest_submission/exercise_latest_submission.json
msgid "Member Email"
msgstr "Medlem E-post"

#. Label of the member_image (Attach Image) field in DocType 'LMS Batch
#. Feedback'
#. Label of the member_image (Attach Image) field in DocType 'LMS Enrollment'
#. Label of the member_image (Attach Image) field in DocType 'LMS Live Class
#. Participant'
#. Label of the member_image (Attach) field in DocType 'LMS Programming
#. Exercise Submission'
#. Label of the member_image (Attach Image) field in DocType 'LMS Video Watch
#. Duration'
#: lms/lms/doctype/lms_batch_feedback/lms_batch_feedback.json
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
#: lms/lms/doctype/lms_live_class_participant/lms_live_class_participant.json
#: lms/lms/doctype/lms_programming_exercise_submission/lms_programming_exercise_submission.json
#: lms/lms/doctype/lms_video_watch_duration/lms_video_watch_duration.json
msgid "Member Image"
msgstr "Medlemsbild"

#. Label of the member_name (Data) field in DocType 'LMS Assignment Submission'
#. Label of the member_name (Data) field in DocType 'LMS Badge Assignment'
#. Label of the member_name (Data) field in DocType 'LMS Batch Enrollment'
#. Label of the member_name (Data) field in DocType 'LMS Batch Feedback'
#. Label of the member_name (Data) field in DocType 'LMS Certificate'
#. Label of the member_name (Data) field in DocType 'LMS Certificate
#. Evaluation'
#. Label of the member_name (Data) field in DocType 'LMS Certificate Request'
#. Label of the member_name (Data) field in DocType 'LMS Course Progress'
#. Label of the member_name (Data) field in DocType 'LMS Enrollment'
#. Label of the member_name (Data) field in DocType 'LMS Live Class
#. Participant'
#. Label of the member_name (Data) field in DocType 'LMS Mentor Request'
#. Label of the member_name (Data) field in DocType 'LMS Programming Exercise
#. Submission'
#. Label of the member_name (Data) field in DocType 'LMS Quiz Submission'
#. Label of the member_name (Data) field in DocType 'LMS Video Watch Duration'
#. Label of the member_name (Data) field in DocType 'LMS Zoom Settings'
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
#: lms/lms/doctype/lms_badge_assignment/lms_badge_assignment.json
#: lms/lms/doctype/lms_batch_enrollment/lms_batch_enrollment.json
#: lms/lms/doctype/lms_batch_feedback/lms_batch_feedback.json
#: lms/lms/doctype/lms_certificate/lms_certificate.json
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
#: lms/lms/doctype/lms_course_progress/lms_course_progress.json
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
#: lms/lms/doctype/lms_live_class_participant/lms_live_class_participant.json
#: lms/lms/doctype/lms_mentor_request/lms_mentor_request.json
#: lms/lms/doctype/lms_programming_exercise_submission/lms_programming_exercise_submission.json
#: lms/lms/doctype/lms_quiz_submission/lms_quiz_submission.json
#: lms/lms/doctype/lms_video_watch_duration/lms_video_watch_duration.json
#: lms/lms/doctype/lms_zoom_settings/lms_zoom_settings.json
#: lms/lms/report/course_progress_summary/course_progress_summary.py:71
msgid "Member Name"
msgstr "Medlems Namn"

#. Label of the member_subgroup (Link) field in DocType 'Exercise Latest
#. Submission'
#: lms/lms/doctype/exercise_latest_submission/exercise_latest_submission.json
msgid "Member Subgroup"
msgstr "Medlem Undergrupp"

#. Label of the member_type (Select) field in DocType 'LMS Enrollment'
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
msgid "Member Type"
msgstr "Medlem Typ"

#. Label of the member_username (Data) field in DocType 'LMS Batch Enrollment'
#. Label of the member_username (Data) field in DocType 'LMS Enrollment'
#. Label of the member_username (Data) field in DocType 'LMS Live Class
#. Participant'
#. Label of the member_username (Data) field in DocType 'LMS Video Watch
#. Duration'
#: lms/lms/doctype/lms_batch_enrollment/lms_batch_enrollment.json
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
#: lms/lms/doctype/lms_live_class_participant/lms_live_class_participant.json
#: lms/lms/doctype/lms_video_watch_duration/lms_video_watch_duration.json
msgid "Member Username"
msgstr "Medlem Användarnamn"

#: frontend/src/pages/ProgramForm.vue:256
msgid "Member added to program"
msgstr "Medlem tillagd till program"

#: lms/lms/doctype/lms_batch_enrollment/lms_batch_enrollment.py:25
msgid "Member already enrolled in this batch"
msgstr "Medlem som redan är inskriven i denna grupp"

#: lms/lms/doctype/lms_program/lms_program.py:29
msgid "Member {0} has already been added to this batch."
msgstr "Medlem {0} har redan lagts till denna grupp."

#. Group in LMS Batch Old's connections
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
msgid "Members"
msgstr "Medlemmar"

#. Label of the membership (Select) field in DocType 'LMS Batch Old'
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
msgid "Membership"
msgstr "Medlemskap"

#. Option for the 'Required Role' (Select) field in DocType 'Cohort Web Page'
#. Label of the mentor (Link) field in DocType 'LMS Course Mentor Mapping'
#. Option for the 'Member Type' (Select) field in DocType 'LMS Enrollment'
#: lms/lms/doctype/cohort_web_page/cohort_web_page.json
#: lms/lms/doctype/lms_course_mentor_mapping/lms_course_mentor_mapping.json
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
msgid "Mentor"
msgstr "Mentor"

#. Label of the mentor_name (Data) field in DocType 'LMS Course Mentor Mapping'
#: lms/lms/doctype/lms_course_mentor_mapping/lms_course_mentor_mapping.json
msgid "Mentor Name"
msgstr "Mentor Namn"

#. Label of the mentor_request_section (Section Break) field in DocType 'LMS
#. Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Mentor Request"
msgstr "Mentor Begäran"

#. Label of the mentor_request_creation (Link) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
#: lms/patches/create_mentor_request_email_templates.py:11
#: lms/patches/create_mentor_request_email_templates.py:18
#: lms/patches/create_mentor_request_email_templates.py:28
msgid "Mentor Request Creation Template"
msgstr "Mentor Begäran Skapande Mall"

#. Label of the mentor_request_status_update (Link) field in DocType 'LMS
#. Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
#: lms/patches/create_mentor_request_email_templates.py:31
#: lms/patches/create_mentor_request_email_templates.py:38
#: lms/patches/create_mentor_request_email_templates.py:48
msgid "Mentor Request Status Update Template"
msgstr "Mentor Begäran Status Uppdatering Mall"

#. Label of the meta_description (Small Text) field in DocType 'LMS Settings'
#: frontend/src/pages/BatchForm.vue:284 frontend/src/pages/CourseForm.vue:274
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Meta Description"
msgstr "Meta Beskrivning"

#. Label of the meta_image (Attach Image) field in DocType 'LMS Batch'
#. Label of the meta_image (Attach Image) field in DocType 'LMS Settings'
#: frontend/src/pages/BatchForm.vue:200
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Meta Image"
msgstr "Meta Bild"

#. Label of the meta_keywords (Small Text) field in DocType 'LMS Settings'
#: frontend/src/pages/BatchForm.vue:290 frontend/src/pages/CourseForm.vue:280
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Meta Keywords"
msgstr "Meta Nyckelord"

#: frontend/src/pages/BatchForm.vue:279 frontend/src/pages/CourseForm.vue:269
msgid "Meta Tags"
msgstr "Meta Taggar"

#: lms/lms/api.py:1459
msgid "Meta tags should be a list."
msgstr "Metataggar bör vara en lista."

#. Label of the milestone (Check) field in DocType 'LMS Batch Timetable'
#: lms/lms/doctype/lms_batch_timetable/lms_batch_timetable.json
msgid "Milestone"
msgstr "Milstolpe"

#: lms/lms/doctype/lms_question/lms_question.py:48
msgid "Minimum two options are required for multiple choice questions."
msgstr "Minst två alternativ erfordras för flervalsfrågor."

#. Name of a role
#: frontend/src/pages/ProfileRoles.vue:20
#: lms/lms/doctype/course_chapter/course_chapter.json
#: lms/lms/doctype/course_evaluator/course_evaluator.json
#: lms/lms/doctype/course_lesson/course_lesson.json
#: lms/lms/doctype/lms_assignment/lms_assignment.json
#: lms/lms/doctype/lms_badge_assignment/lms_badge_assignment.json
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_batch_enrollment/lms_batch_enrollment.json
#: lms/lms/doctype/lms_category/lms_category.json
#: lms/lms/doctype/lms_certificate/lms_certificate.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
#: lms/lms/doctype/lms_course/lms_course.json
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
#: lms/lms/doctype/lms_live_class/lms_live_class.json
#: lms/lms/doctype/lms_program/lms_program.json
#: lms/lms/doctype/lms_programming_exercise/lms_programming_exercise.json
#: lms/lms/doctype/lms_programming_exercise_submission/lms_programming_exercise_submission.json
#: lms/lms/doctype/lms_question/lms_question.json
#: lms/lms/doctype/lms_quiz/lms_quiz.json
#: lms/lms/doctype/lms_settings/lms_settings.json
#: lms/lms/doctype/lms_source/lms_source.json
#: lms/lms/doctype/lms_timetable_template/lms_timetable_template.json
#: lms/lms/doctype/lms_video_watch_duration/lms_video_watch_duration.json
#: lms/lms/doctype/lms_zoom_settings/lms_zoom_settings.json
msgid "Moderator"
msgstr "Moderator"

#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseSubmissions.vue:286
#: frontend/src/pages/Quizzes.vue:263
msgid "Modified"
msgstr "Ändrad"

#: lms/lms/doctype/lms_badge/lms_badge.js:40
msgid "Modified By"
msgstr "Modifierad Av"

#: lms/lms/api.py:218
msgid "Module Name is incorrect or does not exist."
msgstr "Modul Namn är felaktigt eller existerar inte."

#: lms/lms/api.py:214
msgid "Module is incorrect."
msgstr "Modul är felaktig."

#. Option for the 'Day' (Select) field in DocType 'Evaluator Schedule'
#. Option for the 'Day' (Select) field in DocType 'LMS Certificate Request'
#: lms/lms/doctype/evaluator_schedule/evaluator_schedule.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
msgid "Monday"
msgstr "Måndag"

#: frontend/src/components/AppSidebar.vue:600
msgid "Monetization"
msgstr "Intäktsgenerering"

#: frontend/src/components/AppSidebar.vue:39
msgid "More"
msgstr "Mer"

#. Label of the multiple (Check) field in DocType 'LMS Question'
#: lms/lms/doctype/lms_question/lms_question.json
msgid "Multiple Correct Answers"
msgstr "Flera korrekta svar"

#: frontend/src/pages/ProfileEvaluator.vue:4
msgid "My availability"
msgstr "Min tillgänglighet"

#: frontend/src/pages/ProfileEvaluator.vue:127
msgid "My calendar"
msgstr "Min kalender"

#: frontend/src/components/Modals/EmailTemplateModal.vue:24
msgid "Name"
msgstr "Namn"

#. Option for the 'Event' (Select) field in DocType 'LMS Badge'
#: frontend/src/components/Settings/Categories.vue:27
#: frontend/src/components/Settings/EmailTemplates.vue:17
#: frontend/src/components/Settings/Evaluators.vue:17
#: frontend/src/components/Settings/Members.vue:17
#: frontend/src/components/Settings/ZoomSettings.vue:17
#: frontend/src/pages/Assignments.vue:19 frontend/src/pages/Batches.vue:17
#: frontend/src/pages/Courses.vue:17 frontend/src/pages/Courses.vue:310
#: frontend/src/pages/ProgrammingExercises/ProgrammingExercises.vue:32
#: frontend/src/pages/Programs.vue:14 frontend/src/pages/Quizzes.vue:10
#: lms/lms/doctype/lms_badge/lms_badge.json
msgid "New"
msgstr "Ny"

#: lms/www/lms.py:151
msgid "New Batch"
msgstr "Ny Grupp"

#: frontend/src/pages/CourseForm.vue:661 lms/www/lms.py:95
msgid "New Course"
msgstr "Ny Kurs"

#: frontend/src/components/Modals/EmailTemplateModal.vue:7
msgid "New Email Template"
msgstr "Ny e-post mall"

#: frontend/src/pages/Jobs.vue:23
msgid "New Job"
msgstr "Nya Jobb"

#: lms/job/doctype/lms_job_application/lms_job_application.py:27
msgid "New Job Applicant"
msgstr "Ny Jobb Sökande"

#: frontend/src/pages/Programs.vue:90
msgid "New Program"
msgstr "Ny Program"

#: frontend/src/pages/ProgramForm.vue:133
msgid "New Program Course"
msgstr "Ny Program Kurs"

#: frontend/src/pages/ProgramForm.vue:134
msgid "New Program Member"
msgstr "Ny Program Medlem"

#: frontend/src/pages/QuizForm.vue:137
msgid "New Question"
msgstr "Ny Fråga"

#: frontend/src/pages/QuizForm.vue:404 frontend/src/pages/QuizForm.vue:412
msgid "New Quiz"
msgstr "Nytt Frågesport"

#: lms/www/new-sign-up.html:3
msgid "New Sign Up"
msgstr "Ny Registrering"

#: frontend/src/components/Modals/ZoomAccountModal.vue:6
msgid "New Zoom Account"
msgstr "Ny Zoom konto"

#: lms/lms/utils.py:609
msgid "New comment in batch {0}"
msgstr "Ny kommentar i grupp {0}"

#: lms/lms/utils.py:602
msgid "New reply on the topic {0} in course {1}"
msgstr "Nytt svar i ämne {0} i kurs {1}"

#: frontend/src/components/Discussions.vue:8
#: frontend/src/components/Discussions.vue:63
msgid "New {0}"
msgstr "Ny {0}"

#: frontend/src/components/Quiz.vue:237 frontend/src/pages/Lesson.vue:139
msgid "Next"
msgstr "Nästa"

#: lms/templates/quiz/quiz.html:125
msgid "Next Question"
msgstr "Nästa Fråga"

#: frontend/src/components/Assessments.vue:75 lms/templates/assessments.html:58
msgid "No Assessments"
msgstr "Inga Bedömningar"

#: lms/templates/notifications.html:26
msgid "No Notifications"
msgstr "Inga Aviseringar"

#: frontend/src/components/Quiz.vue:307
msgid "No Quiz submissions found"
msgstr "Inga frågesport inlämningar hittades"

#: frontend/src/pages/Quizzes.vue:19
msgid "No Quizzes"
msgstr "Inga frågesporter"

#. Option for the 'Auto Recording' (Select) field in DocType 'LMS Live Class'
#: lms/lms/doctype/lms_live_class/lms_live_class.json
msgid "No Recording"
msgstr "Ingen Inspelning"

#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseSubmissions.vue:13
msgid "No Submissions"
msgstr "Inga Inlämningar"

#: lms/templates/upcoming_evals.html:43
msgid "No Upcoming Evaluations"
msgstr "Inga Kommande Utvärderingar"

#: frontend/src/components/Annoucements.vue:24
msgid "No announcements"
msgstr "Inga tillkännagivanden"

#: lms/templates/certificates_section.html:23
msgid "No certificates"
msgstr "Inga certifikat"

#: frontend/src/components/BatchCourses.vue:67
msgid "No courses added"
msgstr "Inga kurser tillagda"

#: lms/templates/courses_created.html:14
msgid "No courses created"
msgstr "Inga kurser skapade"

#: frontend/src/pages/Programs.vue:81
msgid "No courses in this program"
msgstr "Inga kurser i detta program"

#: lms/templates/courses_under_review.html:14
msgid "No courses under review"
msgstr "Inga kurser under granskning"

#: frontend/src/components/BatchFeedback.vue:60
msgid "No feedback received yet."
msgstr "Ingen återkoppling mottagen ännu."

#: frontend/src/pages/ProfileAbout.vue:12
msgid "No introduction"
msgstr "Ingen introduktion"

#: frontend/src/components/LiveClass.vue:97
msgid "No live classes scheduled"
msgstr "Inga live lektioner schemalagda"

#: frontend/src/pages/QuizForm.vue:188
msgid "No questions added yet"
msgstr "Inga frågor tillagda än"

#: frontend/src/components/Modals/QuizInVideo.vue:93
msgid "No quizzes added yet."
msgstr "Inga frågesporter har lagts till ännu."

#: frontend/src/components/Modals/EvaluationModal.vue:62
msgid "No slots available for this date."
msgstr "Inga lediga tider för detta datum."

#: frontend/src/components/Modals/AnnouncementModal.vue:90
msgid "No students in this batch"
msgstr "Inga studenter i denna grupp"

#: frontend/src/pages/AssignmentSubmissionList.vue:67
msgid "No submissions"
msgstr "Inga inlämningar"

#: frontend/src/components/EmptyState.vue:5 lms/templates/course_list.html:13
msgid "No {0}"
msgstr "Ingen {0}"

#: lms/templates/quiz/quiz.html:147
msgid "No."
msgstr "Nr."

#: lms/lms/user.py:29
msgid "Not Allowed"
msgstr "Ej Tillåtet"

#. Option for the 'Status' (Select) field in DocType 'LMS Assignment
#. Submission'
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
msgid "Not Applicable"
msgstr "Ej Tillämpningbar"

#: lms/templates/assessments.html:48
msgid "Not Attempted"
msgstr "Ej Försökt"

#: lms/lms/widgets/NoPreviewModal.html:6
msgid "Not Available for Preview"
msgstr "Inte tillgänglig för förhandsgranskning"

#. Option for the 'Status' (Select) field in DocType 'LMS Assignment
#. Submission'
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
msgid "Not Graded"
msgstr "Ej Betygsatt"

#: frontend/src/components/NoPermission.vue:7 frontend/src/pages/Batch.vue:164
msgid "Not Permitted"
msgstr "Ej Tillåtet"

#: frontend/src/components/Assignment.vue:36
#: frontend/src/components/Settings/BrandSettings.vue:10
#: frontend/src/components/Settings/PaymentSettings.vue:9
#: frontend/src/components/Settings/SettingDetails.vue:10
#: frontend/src/pages/QuizForm.vue:8 frontend/src/pages/QuizSubmission.vue:9
msgid "Not Saved"
msgstr "Ej Sparad"

#: frontend/src/pages/Notifications.vue:53
msgid "Nothing to see here."
msgstr "Inget att se här."

#. Label of the notifications (Check) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Notifications"
msgstr "Aviseringar"

#: lms/lms/widgets/NoPreviewModal.html:30
msgid "Notify me when available"
msgstr "Meddela mig när den är tillgänglig"

#: frontend/src/components/BatchStudents.vue:48
msgid "Number of Students"
msgstr "Antal Studerande"

#: frontend/src/pages/BatchForm.vue:150
msgid "Number of seats available"
msgstr "Antal platser tillgängliga"

#. Label of the sb_00 (Section Break) field in DocType 'Zoom Settings'
#: lms/lms/doctype/zoom_settings/zoom_settings.json
msgid "OAuth Client ID"
msgstr "OAuth Klient ID"

#. Option for the 'Location Preference' (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Office close to Home"
msgstr "Kontor nära Hemmet"

#. Option for the 'Medium' (Select) field in DocType 'LMS Batch'
#: lms/lms/doctype/lms_batch/lms_batch.json
msgid "Offline"
msgstr "Frånkopplad"

#: lms/templates/emails/certification.html:16
msgid "Once again, congratulations on this significant accomplishment."
msgstr "Än en gång gratulationer till detta betydelsefulla resultat."

#: frontend/src/components/Assignment.vue:60
msgid "Once the moderator grades your submission, you'll find the details here."
msgstr "När moderator betygsatt din inlämning, hittar du detaljerna här."

#. Option for the 'Medium' (Select) field in DocType 'LMS Batch'
#: lms/lms/doctype/lms_batch/lms_batch.json
msgid "Online"
msgstr "Uppkopplad"

#: frontend/src/pages/ProgramForm.vue:157
msgid "Only courses for which self learning is disabled can be added to program."
msgstr "Endast kurser för vilka självinlärning är inaktiverat kan läggas till program."

#: lms/templates/assignment.html:6
msgid "Only files of type {0} will be accepted."
msgstr "Endast filer av typ {0} kommer att accepteras."

#: frontend/src/pages/CourseForm.vue:597 frontend/src/utils/index.js:503
msgid "Only image file is allowed."
msgstr "Endast bildfiler är tillåtna."

#: frontend/src/components/Modals/ChapterModal.vue:218
msgid "Only zip files are allowed"
msgstr "Endast zip filer är tillåtna"

#. Option for the 'Status' (Select) field in DocType 'Job Opportunity'
#. Option for the 'Membership' (Select) field in DocType 'LMS Batch Old'
#: lms/job/doctype/job_opportunity/job_opportunity.json
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
msgid "Open"
msgstr "Öppen"

#: lms/templates/emails/assignment_submission.html:8
msgid "Open Assignment"
msgstr "Öppna Uppgift"

#: lms/templates/emails/lms_message.html:13
msgid "Open Course"
msgstr "Öppen Kurs"

#. Option for the 'Type' (Select) field in DocType 'LMS Question'
#. Option for the 'Type' (Select) field in DocType 'LMS Quiz Question'
#: lms/lms/doctype/lms_question/lms_question.json
#: lms/lms/doctype/lms_quiz_question/lms_quiz_question.json
msgid "Open Ended"
msgstr "Öppen Avslutad"

#. Label of the option (Data) field in DocType 'LMS Option'
#: frontend/src/components/Modals/Question.vue:70
#: lms/lms/doctype/lms_option/lms_option.json
msgid "Option"
msgstr "Alternativ"

#. Label of the option_1 (Small Text) field in DocType 'LMS Question'
#: lms/lms/doctype/lms_question/lms_question.json
msgid "Option 1"
msgstr "Alternativ 1"

#. Label of the option_2 (Small Text) field in DocType 'LMS Question'
#: lms/lms/doctype/lms_question/lms_question.json
msgid "Option 2"
msgstr "Alternativ 2"

#. Label of the option_3 (Small Text) field in DocType 'LMS Question'
#: lms/lms/doctype/lms_question/lms_question.json
msgid "Option 3"
msgstr "Alternativ 3"

#. Label of the option_4 (Small Text) field in DocType 'LMS Question'
#: lms/lms/doctype/lms_question/lms_question.json
msgid "Option 4"
msgstr "Alternativ 4"

#: frontend/src/components/Modals/Question.vue:56
msgid "Options"
msgstr "Alternativ "

#. Label of the order_id (Data) field in DocType 'LMS Payment'
#: lms/lms/doctype/lms_payment/lms_payment.json
msgid "Order ID"
msgstr "Order ID"

#. Label of the organization (Data) field in DocType 'Certification'
#: lms/lms/doctype/certification/certification.json
msgid "Organization"
msgstr "Organisation"

#: frontend/src/pages/Billing.vue:32
msgid "Original Amount"
msgstr "Ursprungligt Belopp"

#. Option for the 'User Category' (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json lms/templates/signup-form.html:28
msgid "Others"
msgstr "Övriga"

#. Label of the output (Data) field in DocType 'LMS Test Case Submission'
#: lms/lms/doctype/lms_test_case_submission/lms_test_case_submission.json
msgid "Output"
msgstr "Utmatning"

#: lms/lms/doctype/lms_badge/lms_badge.js:39
msgid "Owner"
msgstr "Ansvarig"

#. Label of the pan (Data) field in DocType 'LMS Payment'
#: lms/lms/doctype/lms_payment/lms_payment.json
msgid "PAN"
msgstr "PAN"

#. Option for the 'File Type' (Select) field in DocType 'Course Lesson'
#. Option for the 'Type' (Select) field in DocType 'LMS Assignment'
#. Option for the 'Type' (Select) field in DocType 'LMS Assignment Submission'
#: lms/lms/doctype/course_lesson/course_lesson.json
#: lms/lms/doctype/lms_assignment/lms_assignment.json
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
msgid "PDF"
msgstr "PDF"

#. Label of the pages (Table) field in DocType 'Cohort'
#: lms/lms/doctype/cohort/cohort.json
msgid "Pages"
msgstr "Sidor"

#. Label of the paid_batch (Check) field in DocType 'LMS Batch'
#: frontend/src/pages/BatchForm.vue:260
#: lms/lms/doctype/lms_batch/lms_batch.json
msgid "Paid Batch"
msgstr "Betald Parti"

#. Label of the paid_certificate (Check) field in DocType 'LMS Course'
#: frontend/src/pages/CourseForm.vue:237
#: lms/lms/doctype/lms_course/lms_course.json
msgid "Paid Certificate"
msgstr "Betalt Certifikat"

#: frontend/src/components/CourseCardOverlay.vue:165
msgid "Paid Certificate after Evaluation"
msgstr "Betald Certifikat efter Utvärdering"

#. Label of the paid_course (Check) field in DocType 'LMS Course'
#: frontend/src/pages/CourseForm.vue:227
#: lms/lms/doctype/lms_course/lms_course.json
msgid "Paid Course"
msgstr "Betald Kurs"

#: frontend/src/pages/Billing.vue:115
msgid "Pan Number"
msgstr "Pan Nummer"

#. Option for the 'Type' (Select) field in DocType 'Job Opportunity'
#: frontend/src/pages/Jobs.vue:177
#: lms/job/doctype/job_opportunity/job_opportunity.json
msgid "Part Time"
msgstr "Deltid"

#. Option for the 'Status' (Select) field in DocType 'LMS Course Progress'
#: lms/lms/doctype/lms_course_progress/lms_course_progress.json
msgid "Partially Complete"
msgstr "Delvis Klar"

#. Option for the 'Status' (Select) field in DocType 'LMS Assignment
#. Submission'
#. Option for the 'Status' (Select) field in DocType 'LMS Certificate
#. Evaluation'
#: frontend/src/components/Modals/Event.vue:362
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
msgid "Pass"
msgstr "Godkänd"

#. Option for the 'Status' (Select) field in DocType 'LMS Programming Exercise
#. Submission'
#. Option for the 'Status' (Select) field in DocType 'LMS Test Case Submission'
#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseSubmissions.vue:36
#: lms/lms/doctype/lms_programming_exercise_submission/lms_programming_exercise_submission.json
#: lms/lms/doctype/lms_test_case_submission/lms_test_case_submission.json
msgid "Passed"
msgstr "Godkänd"

#. Label of the passing_percentage (Int) field in DocType 'LMS Quiz'
#. Label of the passing_percentage (Int) field in DocType 'LMS Quiz Submission'
#: frontend/src/pages/QuizForm.vue:78 frontend/src/pages/Quizzes.vue:242
#: lms/lms/doctype/lms_quiz/lms_quiz.json
#: lms/lms/doctype/lms_quiz_submission/lms_quiz_submission.json
msgid "Passing Percentage"
msgstr "Passerande Procent"

#. Label of the password (Password) field in DocType 'LMS Live Class'
#: lms/lms/doctype/lms_live_class/lms_live_class.json
msgid "Password"
msgstr "Lösenord"

#: frontend/src/pages/CourseForm.vue:197
msgid "Paste the youtube link of a short video introducing the course"
msgstr "Klistra in youtube länk i kort video för kursintroduktion"

#. Label of the payment (Link) field in DocType 'LMS Batch Enrollment'
#. Label of the payment (Link) field in DocType 'LMS Enrollment'
#: lms/lms/doctype/lms_batch_enrollment/lms_batch_enrollment.json
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
msgid "Payment"
msgstr "Betalning"

#. Name of a DocType
#: lms/lms/doctype/payment_country/payment_country.json
msgid "Payment Country"
msgstr "Betalning Land"

#. Label of the payment_details_section (Section Break) field in DocType 'LMS
#. Payment'
#: lms/lms/doctype/lms_payment/lms_payment.json
msgid "Payment Details"
msgstr "Betalningsdetaljer"

#. Label of the payment_gateway (Data) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Payment Gateway"
msgstr "Betalning Typ"

#. Label of the payment_id (Data) field in DocType 'LMS Payment'
#: lms/lms/doctype/lms_payment/lms_payment.json
msgid "Payment ID"
msgstr "Betalning ID"

#. Label of the payment_received (Check) field in DocType 'LMS Payment'
#: lms/lms/doctype/lms_payment/lms_payment.json
msgid "Payment Received"
msgstr "Betalning Mottagen"

#. Label of the payment_reminder_template (Link) field in DocType 'LMS
#. Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Payment Reminder Template"
msgstr "Betalningspåminnelse Mall"

#. Label of the payment_settings_tab (Tab Break) field in DocType 'LMS
#. Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Payment Settings"
msgstr "Betalning Inställningar"

#: frontend/src/pages/Billing.vue:21
msgid "Payment for "
msgstr "Betalning för "

#. Label of the payment_for_certificate (Check) field in DocType 'LMS Payment'
#: lms/lms/doctype/lms_payment/lms_payment.json
msgid "Payment for Certificate"
msgstr "Betalning för Certifikat"

#. Label of the payment_for_document (Dynamic Link) field in DocType 'LMS
#. Payment'
#: lms/lms/doctype/lms_payment/lms_payment.json
msgid "Payment for Document"
msgstr "Betalning för Dokument"

#. Label of the payment_for_document_type (Select) field in DocType 'LMS
#. Payment'
#: lms/lms/doctype/lms_payment/lms_payment.json
msgid "Payment for Document Type"
msgstr "Betalning för DocType Typ"

#. Label of the payments_app_is_not_installed (HTML) field in DocType 'LMS
#. Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Payments app is not installed"
msgstr "Betalning App är inte installerad"

#. Option for the 'Status' (Select) field in DocType 'Cohort Join Request'
#. Option for the 'Status' (Select) field in DocType 'Invite Request'
#. Option for the 'Status' (Select) field in DocType 'LMS Certificate
#. Evaluation'
#. Option for the 'Status' (Select) field in DocType 'LMS Mentor Request'
#: frontend/src/components/Modals/Event.vue:354
#: lms/lms/doctype/cohort_join_request/cohort_join_request.json
#: lms/lms/doctype/invite_request/invite_request.json
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
#: lms/lms/doctype/lms_mentor_request/lms_mentor_request.json
msgid "Pending"
msgstr "Pågående"

#. Label of the percentage (Int) field in DocType 'LMS Quiz Submission'
#: frontend/src/pages/QuizSubmission.vue:44
#: frontend/src/pages/QuizSubmissionList.vue:102
#: lms/lms/doctype/lms_quiz_submission/lms_quiz_submission.json
msgid "Percentage"
msgstr "Procentuell"

#. Option for the 'Grade Type' (Select) field in DocType 'Education Detail'
#: lms/lms/doctype/education_detail/education_detail.json
msgid "Percentage (e.g. 70%)"
msgstr "Procent (t.ex. 70%)"

#: frontend/src/components/Modals/BatchStudentProgress.vue:44
msgid "Percentage/Status"
msgstr "Procentandel/Status"

#. Label of the persona_captured (Check) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Persona Captured"
msgstr "Persona Fångad"

#: frontend/src/pages/Billing.vue:99
msgid "Phone Number"
msgstr "Telefon Nummer"

#: lms/lms/doctype/lms_settings/lms_settings.py:34
msgid "Please add <a href='{0}'>{1}</a> for <a href='{2}'>{3}</a> to send calendar invites for evaluations."
msgstr "Lägg till <a href='{0}'>{1}</a> för <a href='{2}'>{3}</a> för att skicka kalender inbjudningar för utvärderingar."

#: frontend/src/components/LiveClass.vue:8
msgid "Please add a zoom account to the batch to create live classes."
msgstr "Lägg till Zoom konto i gruppen för att skapa live lektioner."

#: lms/lms/user.py:75
msgid "Please ask your administrator to verify your sign-up"
msgstr "Be Administratör att verifiera din registrering"

#: lms/lms/user.py:73
msgid "Please check your email for verification"
msgstr "Kontrollera din E-post för verifiering"

#: lms/templates/emails/community_course_membership.html:7
msgid "Please click on the following button to set your new password"
msgstr "Klicka på följande knapp för att ange ditt nya lösenord"

#: lms/lms/utils.py:2077 lms/lms/utils.py:2081
msgid "Please complete the previous courses in the program to enroll in this course."
msgstr "Slutför tidigare kurser i program för att anmäla dig till denna kurs."

#: lms/lms/doctype/lms_batch/lms_batch.py:211
msgid "Please enable the zoom account to use this feature."
msgstr "Aktivera zoom konto för att använda denna funktion."

#: frontend/src/components/CourseOutline.vue:328
msgid "Please enroll for this course to view this lesson"
msgstr "Anmäl dig till denna kurs för att se denna lektion"

#: frontend/src/components/Quiz.vue:16
msgid "Please ensure that you complete all the questions in {0} minutes."
msgstr "Se till att besvara alla frågor på {0} minuter."

#: frontend/src/components/Modals/LiveClassModal.vue:186
msgid "Please enter a title."
msgstr "Ange benämning."

#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.py:31
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.py:84
msgid "Please enter a valid URL."
msgstr "Ange giltig URL."

#: frontend/src/components/Modals/LiveClassModal.vue:198
msgid "Please enter a valid time in the format HH:mm."
msgstr "Ange giltig tid i format HH:mm."

#: frontend/src/components/Modals/QuizInVideo.vue:181
msgid "Please enter a valid timestamp"
msgstr "Ange giltig tidsstämpel"

#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.py:78
msgid "Please enter the URL for assignment submission."
msgstr "Ange URL för uppgift inlämning."

#: lms/templates/quiz/quiz.js:176
msgid "Please enter your answer"
msgstr "Ange ditt svar"

#: lms/lms/doctype/lms_batch/lms_batch.py:63
msgid "Please install the Payments App to create a paid batch. Refer to the documentation for more details. {0}"
msgstr "Installera betalning app för att skapa betald grupp. Se dokumentation för mer information. {0}"

#: lms/lms/doctype/lms_course/lms_course.py:55
msgid "Please install the Payments App to create a paid course. Refer to the documentation for more details. {0}"
msgstr "Installera betalning app för att skapa betald kurs. Se dokumentation för mer information. {0}"

#: frontend/src/pages/Billing.vue:254
msgid "Please let us know where you heard about us from."
msgstr "Låt oss veta varifrån du hörde talas om oss."

#: frontend/src/components/QuizBlock.vue:5
msgid "Please login to access the quiz."
msgstr "Logga in för att komma åt frågesport."

#: frontend/src/components/NoPermission.vue:25 frontend/src/pages/Batch.vue:175
msgid "Please login to access this page."
msgstr "Logga in för att komma till denna sida."

#: lms/lms/api.py:210
msgid "Please login to continue with payment."
msgstr "Logga in för att fortsätta med betalning."

#: lms/lms/notification/certificate_request_reminder/certificate_request_reminder.html:7
#: lms/templates/emails/certificate_request_notification.html:7
msgid "Please prepare well and be on time for the evaluations."
msgstr "Förbered dig väl och kom i tid till utvärderingarna."

#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseSubmission.vue:135
msgid "Please run the code to execute the test cases."
msgstr "Kör kod för att utföra testfall."

#: frontend/src/components/UpcomingEvaluations.vue:98
msgid "Please schedule an evaluation to get certified."
msgstr "Boka gärna utvärdering för att bli certifierad."

#: frontend/src/components/Modals/LiveClassModal.vue:189
msgid "Please select a date."
msgstr "Välj Datum"

#: frontend/src/components/Modals/LiveClassModal.vue:213
msgid "Please select a duration."
msgstr "Välj varaktighet."

#: frontend/src/components/Modals/LiveClassModal.vue:210
msgid "Please select a future date and time."
msgstr "Välj framtida datum och tid."

#: frontend/src/components/Modals/QuizInVideo.vue:186
msgid "Please select a quiz"
msgstr "Välj frågesport"

#: frontend/src/components/Modals/LiveClassModal.vue:192
msgid "Please select a time."
msgstr "Välj tid."

#: frontend/src/components/Modals/LiveClassModal.vue:195
msgid "Please select a timezone."
msgstr "Välj tidszon."

#: frontend/src/components/Quiz.vue:533
msgid "Please select an option"
msgstr "Välj ett alternativ"

#: lms/templates/emails/job_report.html:6
msgid "Please take appropriate action at {0}"
msgstr "Vidta lämpliga åtgärder {0}"

#: frontend/src/components/Modals/ChapterModal.vue:175
msgid "Please upload a SCORM package"
msgstr "Ladda upp SCORM App"

#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.py:81
msgid "Please upload the assignment file."
msgstr "Ladda upp tilldelning fil."

#. Option for the 'Grade Type' (Select) field in DocType 'Education Detail'
#: lms/lms/doctype/education_detail/education_detail.json
msgid "Point of Score (e.g. 70)"
msgstr "Poäng (t.ex. 70)"

#: frontend/src/components/Modals/Question.vue:62
msgid "Possibilities"
msgstr "Möjligheter"

#: frontend/src/components/Modals/Question.vue:91
msgid "Possibility"
msgstr "Möjlighet"

#. Label of the possibility_1 (Small Text) field in DocType 'LMS Question'
#: lms/lms/doctype/lms_question/lms_question.json
msgid "Possible Answer 1"
msgstr "Möjligt svar 1"

#. Label of the possibility_2 (Small Text) field in DocType 'LMS Question'
#: lms/lms/doctype/lms_question/lms_question.json
msgid "Possible Answer 2"
msgstr "Möjligt svar 2"

#. Label of the possibility_3 (Small Text) field in DocType 'LMS Question'
#: lms/lms/doctype/lms_question/lms_question.json
msgid "Possible Answer 3"
msgstr "Möjligt svar 3"

#. Label of the possibility_4 (Small Text) field in DocType 'LMS Question'
#: lms/lms/doctype/lms_question/lms_question.json
msgid "Possible Answer 4"
msgstr "Möjligt svar 4"

#: frontend/src/components/DiscussionReplies.vue:54
#: frontend/src/components/DiscussionReplies.vue:89
msgid "Post"
msgstr "Post"

#: frontend/src/pages/Billing.vue:95
msgid "Postal Code"
msgstr "Postnummer"

#: frontend/src/components/AppSidebar.vue:122
msgid "Powered by Learning"
msgstr "Drivs av Lärande"

#. Name of a DocType
#: lms/lms/doctype/preferred_function/preferred_function.json
msgid "Preferred Function"
msgstr "Föredragen Funktion"

#. Label of the preferred_functions (Table MultiSelect) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Preferred Functions"
msgstr "Föredragna Funktioner"

#. Label of the preferred_industries (Table MultiSelect) field in DocType
#. 'User'
#: lms/fixtures/custom_field.json
msgid "Preferred Industries"
msgstr "Föredragna Branscher"

#. Name of a DocType
#: lms/lms/doctype/preferred_industry/preferred_industry.json
msgid "Preferred Industry"
msgstr "Föredragen Bransch"

#. Label of the preferred_location (Data) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Preferred Location"
msgstr "Föredragen Plats"

#. Label of the prevent_skipping_videos (Check) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Prevent Skipping Videos"
msgstr "Förhindra att videor hoppas över"

#. Label of the image (Attach Image) field in DocType 'LMS Course'
#: lms/lms/doctype/lms_course/lms_course.json
msgid "Preview Image"
msgstr "Förhandsgranska Bild"

#: frontend/src/pages/CourseForm.vue:195
msgid "Preview Video"
msgstr "Förhandsgranska Video"

#: frontend/src/pages/Lesson.vue:114
msgid "Previous"
msgstr "Föregående"

#. Label of the pricing_tab (Tab Break) field in DocType 'LMS Batch'
#: frontend/src/pages/BatchForm.vue:255
#: lms/lms/doctype/lms_batch/lms_batch.json
msgid "Pricing"
msgstr "Prissättning"

#. Label of the pricing_tab (Tab Break) field in DocType 'LMS Course'
#: frontend/src/pages/CourseForm.vue:221
#: lms/lms/doctype/lms_course/lms_course.json
msgid "Pricing and Certification"
msgstr "Prissättning och Certifiering"

#. Label of the exception_country (Table MultiSelect) field in DocType 'LMS
#. Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Primary Countries"
msgstr "Primära Länder"

#. Label of the subgroup (Link) field in DocType 'Cohort Mentor'
#: lms/lms/doctype/cohort_mentor/cohort_mentor.json
msgid "Primary Subgroup"
msgstr "Primär Undergrupp"

#: lms/lms/utils.py:441
msgid "Privacy Policy"
msgstr "Integritet Princip"

#. Option for the 'Visibility' (Select) field in DocType 'LMS Batch Old'
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
msgid "Private"
msgstr "Privat"

#. Description of the 'Hide my Private Information from others' (Check) field
#. in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Private Information includes your Grade and Work Environment Preferences"
msgstr "Privat information inkluderar dina betyg och arbetsmiljöpreferenser"

#. Label of the problem_statement (Text Editor) field in DocType 'LMS
#. Programming Exercise'
#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseForm.vue:41
#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseSubmission.vue:25
#: lms/lms/doctype/lms_programming_exercise/lms_programming_exercise.json
msgid "Problem Statement"
msgstr "Problembeskrivning"

#: frontend/src/pages/Billing.vue:129
msgid "Proceed to Payment"
msgstr "Fortsätt till Betalning"

#. Label of the profession (Data) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Profession"
msgstr "Yrke"

#: frontend/src/components/Modals/EditProfile.vue:37
msgid "Profile Image"
msgstr "Profilbild"

#: frontend/src/pages/ProgramForm.vue:155
msgid "Program Course"
msgstr "Program Kurs"

#. Label of the program_courses (Table) field in DocType 'LMS Program'
#: frontend/src/pages/ProgramForm.vue:17
#: lms/lms/doctype/lms_program/lms_program.json
msgid "Program Courses"
msgstr "Program Kurser"

#: frontend/src/pages/ProgramForm.vue:170
msgid "Program Member"
msgstr "Program Medlem"

#. Label of the program_members (Table) field in DocType 'LMS Program'
#: frontend/src/pages/ProgramForm.vue:79
#: lms/lms/doctype/lms_program/lms_program.json
msgid "Program Members"
msgstr "Program Medlemmar"

#: frontend/src/components/Assessments.vue:249
msgid "Programming Exercise"
msgstr "Programmeringsövning"

#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseSubmission.vue:420
msgid "Programming Exercise Submission"
msgstr "Programmeringsövning Inlämning"

#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseSubmission.vue:411
#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseSubmissions.vue:298
msgid "Programming Exercise Submissions"
msgstr "Programmeringsövning Inlämningar"

#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseForm.vue:211
msgid "Programming Exercise created successfully"
msgstr "Programmeringsövning skapad"

#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseForm.vue:247
msgid "Programming Exercise deleted successfully"
msgstr "Programmeringsövning raderad"

#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseForm.vue:230
msgid "Programming Exercise updated successfully"
msgstr "Programmeringsövning uppdaterad"

#. Label of the programming_exercises (Check) field in DocType 'LMS Settings'
#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseSubmissions.vue:308
#: frontend/src/pages/ProgrammingExercises/ProgrammingExercises.vue:158
#: frontend/src/pages/ProgrammingExercises/ProgrammingExercises.vue:166
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Programming Exercises"
msgstr "Programmeringsövningar"

#: frontend/src/pages/Programs.vue:206 frontend/src/pages/Programs.vue:212
#: lms/www/lms.py:295
msgid "Programs"
msgstr "Program"

#. Label of the progress (Float) field in DocType 'LMS Enrollment'
#. Label of the progress (Int) field in DocType 'LMS Program Member'
#: frontend/src/components/Modals/BatchStudentProgress.vue:94
#: frontend/src/components/Modals/CourseProgressSummary.vue:213
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
#: lms/lms/doctype/lms_program_member/lms_program_member.json
msgid "Progress"
msgstr "Framsteg"

#: lms/lms/report/course_progress_summary/course_progress_summary.py:77
msgid "Progress (%)"
msgstr "Framsteg(%)"

#: frontend/src/components/Modals/CourseProgressSummary.vue:103
msgid "Progress Distribution"
msgstr "Fördelning av Framsteg"

#: frontend/src/components/CourseCardOverlay.vue:99
msgid "Progress Summary"
msgstr "Framsteg Översikt"

#: frontend/src/components/BatchStudents.vue:41
msgid "Progress of students in courses and assessments"
msgstr "Studenters framsteg i kurser och bedömningar"

#. Option for the 'Required Role' (Select) field in DocType 'Cohort Web Page'
#. Option for the 'Visibility' (Select) field in DocType 'LMS Batch Old'
#: lms/lms/doctype/cohort_web_page/cohort_web_page.json
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
msgid "Public"
msgstr "Publik"

#. Label of the published (Check) field in DocType 'LMS Certificate'
#: lms/lms/doctype/lms_certificate/lms_certificate.json
msgid "Publish on Participant Page"
msgstr "Publicera på deltagarsidan"

#. Label of the published (Check) field in DocType 'LMS Batch'
#. Label of the published (Check) field in DocType 'LMS Course'
#: frontend/src/components/Modals/BulkCertificates.vue:51
#: frontend/src/components/Modals/Event.vue:108
#: frontend/src/pages/BatchForm.vue:52 frontend/src/pages/CourseForm.vue:150
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_course/lms_course.json
msgid "Published"
msgstr "Publicerad"

#: frontend/src/pages/Statistics.vue:10
#: lms/lms/web_template/lms_statistics/lms_statistics.html:14
#: lms/templates/statistics.html:4
msgid "Published Courses"
msgstr "Publicerade Kurser"

#. Label of the published_on (Date) field in DocType 'LMS Course'
#: frontend/src/pages/CourseForm.vue:154
#: lms/lms/doctype/lms_course/lms_course.json
msgid "Published On"
msgstr "Publicerad"

#. Label of the purchased_certificate (Check) field in DocType 'LMS Enrollment'
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
msgid "Purchased Certificate"
msgstr "Förvärvad Certifikat"

#. Option for the 'Language' (Select) field in DocType 'LMS Programming
#. Exercise'
#: lms/lms/doctype/lms_programming_exercise/lms_programming_exercise.json
msgid "Python"
msgstr "Python"

#. Label of the question (Small Text) field in DocType 'Course Lesson'
#. Label of the question (Text Editor) field in DocType 'LMS Assignment'
#. Label of the question (Text Editor) field in DocType 'LMS Assignment
#. Submission'
#. Label of the question (Text Editor) field in DocType 'LMS Question'
#. Label of the question (Link) field in DocType 'LMS Quiz Question'
#. Label of the question (Text) field in DocType 'LMS Quiz Result'
#: frontend/src/components/Assignment.vue:20
#: frontend/src/components/Modals/AssignmentForm.vue:32
#: frontend/src/components/Modals/Question.vue:27
#: frontend/src/pages/QuizForm.vue:343 frontend/src/pages/QuizSubmission.vue:56
#: lms/lms/doctype/course_lesson/course_lesson.json
#: lms/lms/doctype/lms_assignment/lms_assignment.json
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
#: lms/lms/doctype/lms_question/lms_question.json
#: lms/lms/doctype/lms_quiz_question/lms_quiz_question.json
#: lms/lms/doctype/lms_quiz_result/lms_quiz_result.json
#: lms/templates/quiz/quiz.html:104
msgid "Question"
msgstr "Fråga"

#: lms/templates/quiz/quiz.html:62
msgid "Question "
msgstr "Fråga "

#. Label of the question_detail (Text) field in DocType 'LMS Quiz Question'
#: lms/lms/doctype/lms_quiz_question/lms_quiz_question.json
msgid "Question Detail"
msgstr "Fråga Detalj"

#. Label of the question_name (Link) field in DocType 'LMS Quiz Result'
#: lms/lms/doctype/lms_quiz_result/lms_quiz_result.json
msgid "Question Name"
msgstr "Fråga Namn"

#: frontend/src/components/Modals/Question.vue:284
msgid "Question added successfully"
msgstr "Fråga tillagd"

#: frontend/src/components/Modals/Question.vue:334
msgid "Question updated successfully"
msgstr "Fråga uppdaterad"

#: frontend/src/components/Quiz.vue:112
msgid "Question {0}"
msgstr "Fråga {0}"

#: frontend/src/components/Quiz.vue:214
msgid "Question {0} of {1}"
msgstr "Fråga {0} av {1}"

#. Label of the questions (Table) field in DocType 'LMS Quiz'
#: frontend/src/pages/QuizForm.vue:131 lms/lms/doctype/lms_quiz/lms_quiz.json
msgid "Questions"
msgstr "Frågor"

#: frontend/src/pages/QuizForm.vue:385
msgid "Questions deleted successfully"
msgstr "Frågor är borttagna"

#. Label of the quiz (Link) field in DocType 'LMS Quiz Submission'
#. Label of a Link in the LMS Workspace
#: frontend/src/components/Assessments.vue:247
#: frontend/src/components/Modals/QuizInVideo.vue:21
#: frontend/src/components/Modals/QuizInVideo.vue:216
#: frontend/src/pages/QuizSubmission.vue:26 frontend/src/utils/quiz.js:24
#: lms/lms/doctype/lms_quiz_submission/lms_quiz_submission.json
#: lms/lms/workspace/lms/lms.json
msgid "Quiz"
msgstr "Frågesport"

#. Label of the quiz_id (Data) field in DocType 'Course Lesson'
#: lms/lms/doctype/course_lesson/course_lesson.json
msgid "Quiz ID"
msgstr "Frågesport ID"

#. Label of a Link in the LMS Workspace
#: frontend/src/pages/QuizPage.vue:57 lms/lms/workspace/lms/lms.json
msgid "Quiz Submission"
msgstr "Frågesport Inlämning"

#: frontend/src/pages/QuizSubmission.vue:131
#: frontend/src/pages/QuizSubmissionList.vue:111
#: frontend/src/pages/QuizSubmissionList.vue:116
msgid "Quiz Submissions"
msgstr "Frågesport Inlämningar"

#: frontend/src/components/Quiz.vue:251
msgid "Quiz Summary"
msgstr "Frågesport Sammanfattning"

#. Label of the quiz_title (Data) field in DocType 'LMS Quiz Submission'
#: lms/lms/doctype/lms_quiz_submission/lms_quiz_submission.json
msgid "Quiz Title"
msgstr "Frågesport Benämning"

#: frontend/src/pages/Quizzes.vue:201
msgid "Quiz created successfully"
msgstr "Frågesport skapad"

#: lms/plugins.py:96
msgid "Quiz is not available to Guest users. Please login to continue."
msgstr "Frågesport är inte tillgänglig för gästanvändare. Logga in för att fortsätta."

#: frontend/src/pages/QuizForm.vue:310
msgid "Quiz updated successfully"
msgstr "Frågesport uppdaterad"

#. Description of the 'Quiz ID' (Data) field in DocType 'Course Lesson'
#: lms/lms/doctype/course_lesson/course_lesson.json
msgid "Quiz will appear at the bottom of the lesson."
msgstr "Frågesport kommer att visas längst ner i lektionen."

#: frontend/src/components/AppSidebar.vue:584
#: frontend/src/pages/QuizForm.vue:396 frontend/src/pages/Quizzes.vue:275
#: frontend/src/pages/Quizzes.vue:285 lms/www/lms.py:251
msgid "Quizzes"
msgstr "Frågesporter"

#: frontend/src/pages/Quizzes.vue:223
msgid "Quizzes deleted successfully"
msgstr "Frågesporter raderade"

#: frontend/src/components/Modals/QuizInVideo.vue:35
msgid "Quizzes in this video"
msgstr "Frågesporter i denna video"

#. Label of the rating (Rating) field in DocType 'LMS Certificate Evaluation'
#. Label of the rating (Data) field in DocType 'LMS Course'
#. Label of the rating (Rating) field in DocType 'LMS Course Review'
#: frontend/src/components/CourseCardOverlay.vue:147
#: frontend/src/components/Modals/Event.vue:86
#: frontend/src/components/Modals/ReviewModal.vue:18
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
#: lms/lms/doctype/lms_course/lms_course.json
#: lms/lms/doctype/lms_course_review/lms_course_review.json
#: lms/templates/reviews.html:125
msgid "Rating"
msgstr "Bedömning"

#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.py:17
msgid "Rating cannot be 0"
msgstr "Betyg kan inte vara 0"

#. Option for the 'Stage' (Select) field in DocType 'LMS Batch Old'
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
msgid "Ready"
msgstr "Klart"

#. Label of the reference_docname (Dynamic Link) field in DocType 'LMS Batch
#. Timetable'
#: lms/lms/doctype/lms_batch_timetable/lms_batch_timetable.json
msgid "Reference DocName"
msgstr "Referens Dokument Namn"

#. Label of the reference_doctype (Link) field in DocType 'LMS Batch Timetable'
#. Label of the reference_doctype (Link) field in DocType 'LMS Timetable
#. Legend'
#: lms/lms/doctype/lms_batch_timetable/lms_batch_timetable.json
#: lms/lms/doctype/lms_timetable_legend/lms_timetable_legend.json
msgid "Reference DocType"
msgstr "Referens DocType"

#. Label of the reference_doctype (Link) field in DocType 'LMS Badge'
#: lms/lms/doctype/lms_badge/lms_badge.json
msgid "Reference Document Type"
msgstr "Referens Dokument Typ"

#: lms/templates/emails/community_course_membership.html:17
msgid "Regards"
msgstr "Hälsningar"

#: frontend/src/components/BatchOverlay.vue:89
msgid "Register Now"
msgstr "Registrera Nu"

#. Option for the 'Status' (Select) field in DocType 'Invite Request'
#: lms/lms/doctype/invite_request/invite_request.json
msgid "Registered"
msgstr "Registrerad"

#: lms/lms/user.py:36
msgid "Registered but disabled"
msgstr "Registrerad men inaktiverad"

#. Option for the 'Status' (Select) field in DocType 'Cohort Join Request'
#. Option for the 'Status' (Select) field in DocType 'Invite Request'
#. Option for the 'Status' (Select) field in DocType 'LMS Mentor Request'
#: lms/lms/doctype/cohort_join_request/cohort_join_request.json
#: lms/lms/doctype/invite_request/invite_request.json
#: lms/lms/doctype/lms_mentor_request/lms_mentor_request.json
msgid "Rejected"
msgstr "Avvisad"

#. Label of the related_courses (Table) field in DocType 'LMS Course'
#. Name of a DocType
#: frontend/src/components/RelatedCourses.vue:5
#: frontend/src/pages/CourseForm.vue:206
#: lms/lms/doctype/lms_course/lms_course.json
#: lms/lms/doctype/related_courses/related_courses.json
msgid "Related Courses"
msgstr "Relaterade Kurser"

#: frontend/src/pages/BatchForm.vue:236 frontend/src/pages/CourseForm.vue:127
msgid "Remove"
msgstr "Ta bort"

#: frontend/src/components/Modals/AnnouncementModal.vue:27
msgid "Reply To"
msgstr "Svara till"

#: lms/lms/widgets/RequestInvite.html:7
msgid "Request Invite"
msgstr "Begär Inbjudan"

#: lms/patches/create_mentor_request_email_templates.py:20
msgid "Request for Mentorship"
msgstr "Begäran om Mentorskap"

#. Label of the required_role (Select) field in DocType 'Cohort Web Page'
#: lms/lms/doctype/cohort_web_page/cohort_web_page.json
msgid "Required Role"
msgstr "Erfordrad Roll"

#. Option for the 'Membership' (Select) field in DocType 'LMS Batch Old'
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
msgid "Restricted"
msgstr "Begränsad"

#. Label of the result (Table) field in DocType 'LMS Quiz Submission'
#: lms/lms/doctype/lms_quiz_submission/lms_quiz_submission.json
msgid "Result"
msgstr "Resultat"

#. Label of the resume (Attach) field in DocType 'LMS Job Application'
#: lms/job/doctype/lms_job_application/lms_job_application.json
msgid "Resume"
msgstr "Återuppta"

#: frontend/src/components/Quiz.vue:85 frontend/src/components/Quiz.vue:288
msgid "Resume Video"
msgstr "Återuppta Video"

#. Label of the review (Small Text) field in DocType 'LMS Course Review'
#. Label of a Link in the LMS Workspace
#: frontend/src/components/Modals/ReviewModal.vue:20
#: lms/lms/doctype/lms_course_review/lms_course_review.json
#: lms/lms/workspace/lms/lms.json lms/templates/reviews.html:143
msgid "Review"
msgstr "Recension"

#: lms/templates/reviews.html:100
msgid "Review the course"
msgstr "Granska Kurs"

#. Label of the reviewed_by (Link) field in DocType 'LMS Mentor Request'
#: lms/lms/doctype/lms_mentor_request/lms_mentor_request.json
msgid "Reviewed By"
msgstr "Granskats av"

#: lms/templates/reviews.html:4
msgid "Reviews"
msgstr "Recension "

#. Label of the role (Select) field in DocType 'Cohort Staff'
#. Label of the role (Select) field in DocType 'LMS Enrollment'
#: lms/lms/doctype/cohort_staff/cohort_staff.json
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
msgid "Role"
msgstr "Roll"

#. Label of the role (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Role Preference"
msgstr "Rollpreferens"

#: frontend/src/pages/ProfileRoles.vue:117
msgid "Role updated successfully"
msgstr "Roll uppdaterad"

#: frontend/src/components/AppSidebar.vue:612
msgid "Roles"
msgstr "Roller"

#. Label of the route (Data) field in DocType 'LMS Sidebar Item'
#: lms/lms/doctype/lms_sidebar_item/lms_sidebar_item.json
msgid "Route"
msgstr "Sökväg"

#: lms/lms/doctype/lms_batch/lms_batch.py:139
msgid "Row #{0} Date cannot be outside the batch duration."
msgstr "Rad #{0} Datum kan inte vara utanför grupp varaktighet."

#: lms/lms/doctype/lms_batch/lms_batch.py:134
msgid "Row #{0} End time cannot be outside the batch duration."
msgstr "Rad #{0} Sluttid kan inte vara utanför grupp varaktighet."

#: lms/lms/doctype/lms_batch/lms_batch.py:116
msgid "Row #{0} Start time cannot be greater than or equal to end time."
msgstr "Rad #{0} Starttid kan inte vara senare än eller lika med sluttid."

#: lms/lms/doctype/lms_batch/lms_batch.py:125
msgid "Row #{0} Start time cannot be outside the batch duration."
msgstr "Rad #{0} Starttid kan inte vara utanför grupp varaktighet."

#: lms/lms/doctype/lms_quiz/lms_quiz.py:32
msgid "Rows {0} have the duplicate questions."
msgstr "Rader {0} har duplicerade frågor."

#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseSubmission.vue:56
#: lms/templates/livecode/extension_footer.html:21
msgid "Run"
msgstr "Kör"

#. Label of the scorm_section (Section Break) field in DocType 'Course Chapter'
#: lms/lms/doctype/course_chapter/course_chapter.json
msgid "SCORM"
msgstr "SCORM"

#. Label of the scorm_package (Link) field in DocType 'Course Chapter'
#: frontend/src/components/Modals/ChapterModal.vue:22
#: lms/lms/doctype/course_chapter/course_chapter.json
msgid "SCORM Package"
msgstr "SCORM App"

#. Label of the scorm_package_path (Code) field in DocType 'Course Chapter'
#: lms/lms/doctype/course_chapter/course_chapter.json
msgid "SCORM Package Path"
msgstr "SCORM App Sökväg"

#. Label of the seo_tab (Tab Break) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "SEO"
msgstr "SEO"

#. Option for the 'Day' (Select) field in DocType 'Evaluator Schedule'
#. Option for the 'Day' (Select) field in DocType 'LMS Certificate Request'
#: lms/lms/doctype/evaluator_schedule/evaluator_schedule.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
msgid "Saturday"
msgstr "Lördag"

#: frontend/src/components/AssessmentPlugin.vue:12
#: frontend/src/components/Assignment.vue:46
#: frontend/src/components/Controls/Code.vue:18
#: frontend/src/components/Controls/CodeEditor.vue:25
#: frontend/src/components/Modals/AssignmentForm.vue:59
#: frontend/src/components/Modals/EmailTemplateModal.vue:12
#: frontend/src/components/Modals/Event.vue:101
#: frontend/src/components/Modals/Event.vue:129
#: frontend/src/components/Modals/Question.vue:112
#: frontend/src/components/Modals/ZoomAccountModal.vue:10
#: frontend/src/pages/BatchForm.vue:8 frontend/src/pages/CourseForm.vue:17
#: frontend/src/pages/JobForm.vue:8 frontend/src/pages/LessonForm.vue:14
#: frontend/src/pages/ProgramForm.vue:7
#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseForm.vue:101
#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseModal.vue:9
#: frontend/src/pages/QuizForm.vue:43 frontend/src/pages/QuizSubmission.vue:14
#: frontend/src/pages/Quizzes.vue:105
msgid "Save"
msgstr "Spara"

#. Label of the schedule (Table) field in DocType 'Course Evaluator'
#: lms/lms/doctype/course_evaluator/course_evaluator.json
msgid "Schedule"
msgstr "Schema"

#: frontend/src/components/Modals/EvaluationModal.vue:5
#: frontend/src/components/UpcomingEvaluations.vue:11
msgid "Schedule Evaluation"
msgstr "Schemalägg Utvärdering"

#. Name of a DocType
#: lms/lms/doctype/scheduled_flow/scheduled_flow.json
msgid "Scheduled Flow"
msgstr "Schemalagt Flöde"

#. Label of the scope (Select) field in DocType 'Cohort Web Page'
#: lms/lms/doctype/cohort_web_page/cohort_web_page.json
msgid "Scope"
msgstr "Omfatning"

#. Label of the score (Int) field in DocType 'LMS Quiz Submission'
#: frontend/src/pages/QuizSubmission.vue:39
#: frontend/src/pages/QuizSubmissionList.vue:96
#: lms/lms/doctype/lms_quiz_submission/lms_quiz_submission.json
#: lms/templates/quiz/quiz.html:148
msgid "Score"
msgstr "Resultat"

#. Label of the score_out_of (Int) field in DocType 'LMS Quiz Submission'
#: lms/lms/doctype/lms_quiz_submission/lms_quiz_submission.json
msgid "Score Out Of"
msgstr "Resultat av"

#: frontend/src/components/Settings/Evaluators.vue:25
#: frontend/src/components/Settings/Members.vue:25
#: frontend/src/pages/Jobs.vue:41
msgid "Search"
msgstr "Sök"

#: frontend/src/components/Modals/CourseProgressSummary.vue:18
msgid "Search by Member Name"
msgstr "Sök efter Medlemsnamn"

#: frontend/src/pages/CertifiedParticipants.vue:23
msgid "Search by Name"
msgstr "Sök efter Namn"

#: frontend/src/pages/Batches.vue:45 frontend/src/pages/Courses.vue:41
msgid "Search by Title"
msgstr "Sök efter Benämning"

#: frontend/src/pages/Assignments.vue:34
#: frontend/src/pages/ProgrammingExercises/ProgrammingExercises.vue:47
msgid "Search by title"
msgstr "Sök efter benämning"

#: frontend/src/components/Controls/IconPicker.vue:36
msgid "Search for an icon"
msgstr "Sök efter ikon"

#. Label of the seat_count (Int) field in DocType 'LMS Batch'
#: frontend/src/pages/BatchForm.vue:147
#: lms/lms/doctype/lms_batch/lms_batch.json
msgid "Seat Count"
msgstr "Antal Platser"

#: frontend/src/components/BatchCard.vue:18
#: frontend/src/components/BatchOverlay.vue:17
msgid "Seat Left"
msgstr "Antal Plater Kvar"

#: lms/lms/doctype/lms_batch/lms_batch.py:103
msgid "Seat count cannot be negative."
msgstr "Antal platser kan inte vara negativt."

#: frontend/src/components/BatchCard.vue:15
#: frontend/src/components/BatchOverlay.vue:14
msgid "Seats Left"
msgstr "Antal Platser Kvar"

#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseModal.vue:23
msgid "Select a Programming Exercise"
msgstr "Välj Programmeringsövning"

#: frontend/src/components/Modals/Question.vue:101
msgid "Select a question"
msgstr "Välj fråga"

#: frontend/src/components/AssessmentPlugin.vue:28
msgid "Select a quiz"
msgstr "Välj Frågesport"

#: frontend/src/components/Modals/EvaluationModal.vue:40
msgid "Select a slot"
msgstr "Välj tid"

#: frontend/src/components/AssessmentPlugin.vue:35
msgid "Select an assignment"
msgstr "Välj uppgift"

#: lms/lms/doctype/lms_batch_enrollment/lms_batch_enrollment.js:7
msgid "Send Confirmation Email"
msgstr "Skicka bekräftelse via e-post"

#. Label of the send_calendar_invite_for_evaluations (Check) field in DocType
#. 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Send calendar invite for evaluations"
msgstr "Skicka kalenderinbjudan för utvärderingar"

#. Label of the sessions_on (Data) field in DocType 'LMS Batch Old'
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
msgid "Sessions On Days"
msgstr "Sessioner på dagar"

#: lms/templates/emails/community_course_membership.html:1
msgid "Set your Password"
msgstr "Ange Lösenord"

#: frontend/src/components/AppSidebar.vue:560
msgid "Setting up"
msgstr "Konfigurera"

#: frontend/src/components/AppSidebar.vue:605
msgid "Setting up payment gateway"
msgstr "Konfigurerar Betalningsport"

#: frontend/src/components/AppSidebar.vue:610
#: frontend/src/components/Settings/Settings.vue:7
#: frontend/src/pages/BatchForm.vue:46 frontend/src/pages/CourseForm.vue:143
#: frontend/src/pages/ProfileRoles.vue:4
#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseSubmission.vue:19
#: frontend/src/pages/QuizForm.vue:86
msgid "Settings"
msgstr "Inställningar"

#: frontend/src/pages/ProfileAbout.vue:62
msgid "Share on"
msgstr "Dela på"

#: frontend/src/pages/BatchForm.vue:35
msgid "Short Description"
msgstr "Kort Beskrivning"

#. Label of the short_introduction (Small Text) field in DocType 'LMS Course'
#: frontend/src/pages/CourseForm.vue:80
#: lms/lms/doctype/lms_course/lms_course.json
msgid "Short Introduction"
msgstr "Kort Introduktion"

#: frontend/src/pages/BatchForm.vue:38
msgid "Short description of the batch"
msgstr "Kort beskrivning av grupp"

#. Label of the show_answer (Check) field in DocType 'LMS Assignment'
#: lms/lms/doctype/lms_assignment/lms_assignment.json
msgid "Show Answer"
msgstr "Visa Svar"

#. Label of the show_answers (Check) field in DocType 'LMS Quiz'
#: frontend/src/pages/QuizForm.vue:93 frontend/src/pages/Quizzes.vue:256
#: lms/lms/doctype/lms_quiz/lms_quiz.json
msgid "Show Answers"
msgstr "Visa Svar"

#. Label of the show_submission_history (Check) field in DocType 'LMS Quiz'
#: frontend/src/pages/QuizForm.vue:98 lms/lms/doctype/lms_quiz/lms_quiz.json
msgid "Show Submission History"
msgstr "Visa inlämningshistorik"

#. Label of the column_break_2 (Column Break) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Show Tab in Batch"
msgstr "Visa flik i Grupp"

#. Label of the show_usd_equivalent (Check) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Show USD Equivalent"
msgstr "Visa USD ekvivalent"

#. Label of the show_day_view (Check) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Show day view in timetable"
msgstr "Visa dagsvy i Tidtabell"

#. Label of the show_live_class (Check) field in DocType 'LMS Batch'
#: lms/lms/doctype/lms_batch/lms_batch.json
msgid "Show live class"
msgstr "Visa live klass"

#. Label of the shuffle_questions (Check) field in DocType 'LMS Quiz'
#: frontend/src/pages/QuizForm.vue:105 lms/lms/doctype/lms_quiz/lms_quiz.json
msgid "Shuffle Questions"
msgstr "Blanda frågor"

#. Label of the sidebar_tab (Tab Break) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Sidebar"
msgstr "Sidofält"

#. Label of the sidebar_items (Table) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Sidebar Items"
msgstr "Sidofält Element"

#: lms/lms/user.py:29
msgid "Sign Up is disabled"
msgstr "Registrering är inaktiverad"

#: lms/templates/signup-form.html:53
msgid "Sign up"
msgstr "Registrera"

#. Label of the signup_email (Data) field in DocType 'Invite Request'
#: lms/lms/doctype/invite_request/invite_request.json
msgid "Signup Email"
msgstr "Registrering E-post"

#. Label of the signup_settings_tab (Tab Break) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Signup Settings"
msgstr "Registrering Inställningar"

#. Label of a chart in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "Signups"
msgstr "Registreringar"

#. Label of the skill (Table MultiSelect) field in DocType 'User'
#. Label of the skill (Data) field in DocType 'User Skill'
#: lms/fixtures/custom_field.json lms/lms/doctype/user_skill/user_skill.json
msgid "Skill"
msgstr "Färdighet"

#. Label of the skill_details (Section Break) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Skill Details"
msgstr "Kompetens Detaljer"

#. Label of the skill_name (Link) field in DocType 'Skills'
#: lms/lms/doctype/skills/skills.json
msgid "Skill Name"
msgstr "Färdighet Namn"

#. Name of a DocType
#: lms/lms/doctype/skills/skills.json
msgid "Skills"
msgstr "Färdigheter"

#: frontend/src/pages/PersonaForm.vue:51 lms/templates/onboarding_header.html:6
msgid "Skip"
msgstr "Hoppa Över"

#: lms/lms/doctype/course_evaluator/course_evaluator.py:63
msgid "Slot Times are overlapping for some schedules."
msgstr "Tider överlappar för vissa schema."

#: frontend/src/pages/ProfileEvaluator.vue:201
msgid "Slot added successfully"
msgstr "Tid tillagd"

#: frontend/src/pages/ProfileEvaluator.vue:240
msgid "Slot deleted successfully"
msgstr "Tid raderad"

#. Label of the slug (Data) field in DocType 'Cohort'
#. Label of the slug (Data) field in DocType 'Cohort Subgroup'
#. Label of the slug (Data) field in DocType 'Cohort Web Page'
#: lms/lms/doctype/cohort/cohort.json
#: lms/lms/doctype/cohort_subgroup/cohort_subgroup.json
#: lms/lms/doctype/cohort_web_page/cohort_web_page.json
msgid "Slug"
msgstr "Slug"

#: frontend/src/components/BatchCard.vue:25
#: frontend/src/components/BatchOverlay.vue:24
msgid "Sold Out"
msgstr "Slutsåld"

#. Label of the solution (Code) field in DocType 'Exercise Latest Submission'
#. Label of the solution (Code) field in DocType 'Exercise Submission'
#: lms/lms/doctype/exercise_latest_submission/exercise_latest_submission.json
#: lms/lms/doctype/exercise_submission/exercise_submission.json
msgid "Solution"
msgstr "Lösning"

#. Label of the source (Link) field in DocType 'LMS Batch Enrollment'
#. Label of the source (Link) field in DocType 'LMS Payment'
#. Label of the source (Data) field in DocType 'LMS Source'
#. Label of the source (Data) field in DocType 'LMS Video Watch Duration'
#: lms/lms/doctype/lms_batch_enrollment/lms_batch_enrollment.json
#: lms/lms/doctype/lms_payment/lms_payment.json
#: lms/lms/doctype/lms_source/lms_source.json
#: lms/lms/doctype/lms_video_watch_duration/lms_video_watch_duration.json
msgid "Source"
msgstr "Från"

#. Option for the 'Role' (Select) field in DocType 'Cohort Staff'
#. Option for the 'Member Type' (Select) field in DocType 'LMS Enrollment'
#: lms/lms/doctype/cohort_staff/cohort_staff.json
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
msgid "Staff"
msgstr "Personal"

#. Label of the stage (Select) field in DocType 'LMS Batch Old'
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
msgid "Stage"
msgstr "Fas"

#: frontend/src/components/LiveClass.vue:70 frontend/src/components/Quiz.vue:81
#: lms/templates/quiz/quiz.html:39
msgid "Start"
msgstr "Start"

#. Label of the start_date (Date) field in DocType 'Education Detail'
#. Label of the start_date (Date) field in DocType 'LMS Batch'
#. Label of the start_date (Date) field in DocType 'LMS Batch Old'
#: frontend/src/pages/BatchForm.vue:75
#: lms/lms/doctype/education_detail/education_detail.json
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
msgid "Start Date"
msgstr "Start Datum"

#: lms/templates/emails/batch_start_reminder.html:13
msgid "Start Date:"
msgstr "Start Datum:"

#: frontend/src/components/CourseCardOverlay.vue:76
#: frontend/src/pages/Lesson.vue:45 frontend/src/pages/SCORMChapter.vue:28
#: lms/templates/emails/lms_course_interest.html:9
msgid "Start Learning"
msgstr "Börja lära dig"

#. Label of the start_time (Time) field in DocType 'Evaluator Schedule'
#. Label of the start_time (Time) field in DocType 'LMS Batch'
#. Label of the start_time (Time) field in DocType 'LMS Batch Old'
#. Label of the start_time (Time) field in DocType 'LMS Batch Timetable'
#. Label of the start_time (Time) field in DocType 'LMS Certificate Evaluation'
#. Label of the start_time (Time) field in DocType 'LMS Certificate Request'
#. Label of the start_time (Time) field in DocType 'Scheduled Flow'
#: frontend/src/pages/BatchForm.vue:91
#: frontend/src/pages/ProfileEvaluator.vue:29
#: lms/lms/doctype/evaluator_schedule/evaluator_schedule.json
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
#: lms/lms/doctype/lms_batch_timetable/lms_batch_timetable.json
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
#: lms/lms/doctype/scheduled_flow/scheduled_flow.json
msgid "Start Time"
msgstr "Start Tid"

#: lms/lms/doctype/course_evaluator/course_evaluator.py:34
msgid "Start Time cannot be greater than End Time"
msgstr "Starttid kan inte vara senare än sluttid"

#. Label of the start_url (Small Text) field in DocType 'LMS Live Class'
#: lms/lms/doctype/lms_live_class/lms_live_class.json
msgid "Start URL"
msgstr "Start URL"

#: frontend/src/components/Quiz.vue:81
msgid "Start the Quiz"
msgstr "Starta frågesport"

#. Option for the 'Company Type' (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Startup Organization"
msgstr "Uppstart Organisation"

#: frontend/src/pages/Billing.vue:83
msgid "State/Province"
msgstr "Län"

#. Label of the tab_4_tab (Tab Break) field in DocType 'LMS Course'
#. Label of the statistics (Check) field in DocType 'LMS Settings'
#: frontend/src/components/BatchStudents.vue:5
#: frontend/src/pages/Statistics.vue:225
#: lms/lms/doctype/lms_course/lms_course.json
#: lms/lms/doctype/lms_settings/lms_settings.json lms/www/lms.py:204
msgid "Statistics"
msgstr "Statistik"

#. Label of the status (Select) field in DocType 'Job Opportunity'
#. Label of the status (Select) field in DocType 'Cohort'
#. Label of the status (Select) field in DocType 'Cohort Join Request'
#. Label of the status (Select) field in DocType 'Exercise Latest Submission'
#. Label of the status (Select) field in DocType 'Exercise Submission'
#. Label of the status (Select) field in DocType 'Invite Request'
#. Label of the status (Select) field in DocType 'LMS Assignment Submission'
#. Label of the status (Select) field in DocType 'LMS Batch Old'
#. Label of the status (Select) field in DocType 'LMS Certificate Evaluation'
#. Label of the status (Select) field in DocType 'LMS Certificate Request'
#. Label of the status (Select) field in DocType 'LMS Course'
#. Label of the status (Select) field in DocType 'LMS Course Progress'
#. Label of the status (Select) field in DocType 'LMS Mentor Request'
#. Label of the status (Select) field in DocType 'LMS Programming Exercise
#. Submission'
#. Label of the status (Select) field in DocType 'LMS Test Case Submission'
#: frontend/src/components/Modals/Event.vue:91
#: frontend/src/components/Settings/ZoomSettings.vue:182
#: frontend/src/pages/AssignmentSubmissionList.vue:19
#: frontend/src/pages/JobForm.vue:46
#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseSubmissions.vue:280
#: lms/job/doctype/job_opportunity/job_opportunity.json
#: lms/lms/doctype/cohort/cohort.json
#: lms/lms/doctype/cohort_join_request/cohort_join_request.json
#: lms/lms/doctype/exercise_latest_submission/exercise_latest_submission.json
#: lms/lms/doctype/exercise_submission/exercise_submission.json
#: lms/lms/doctype/invite_request/invite_request.json
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
#: lms/lms/doctype/lms_course/lms_course.json
#: lms/lms/doctype/lms_course_progress/lms_course_progress.json
#: lms/lms/doctype/lms_mentor_request/lms_mentor_request.json
#: lms/lms/doctype/lms_programming_exercise_submission/lms_programming_exercise_submission.json
#: lms/lms/doctype/lms_test_case_submission/lms_test_case_submission.json
msgid "Status"
msgstr "Status"

#: lms/templates/assessments.html:17
msgid "Status/Score"
msgstr "Status/Resultat"

#. Option for the 'User Category' (Select) field in DocType 'User'
#. Option for the 'Required Role' (Select) field in DocType 'Cohort Web Page'
#. Option for the 'Member Type' (Select) field in DocType 'LMS Enrollment'
#: frontend/src/pages/ProfileRoles.vue:38 lms/fixtures/custom_field.json
#: lms/lms/doctype/cohort_web_page/cohort_web_page.json
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
#: lms/templates/signup-form.html:26
msgid "Student"
msgstr "Studerande"

#: frontend/src/components/CourseReviews.vue:11
msgid "Student Reviews"
msgstr "Student Recensioner"

#. Label of the show_students (Check) field in DocType 'LMS Settings'
#: frontend/src/components/BatchStudents.vue:11
#: frontend/src/components/BatchStudents.vue:67
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Students"
msgstr "Studenter"

#: frontend/src/components/BatchStudents.vue:285
msgid "Students deleted successfully"
msgstr "Studenter borttagna"

#. Description of the 'Paid Batch' (Check) field in DocType 'LMS Batch'
#: lms/lms/doctype/lms_batch/lms_batch.json
msgid "Students will be enrolled in a paid batch once they complete the payment"
msgstr "Studenter kommer att registreras i betald grupp när de slutför betalning"

#. Label of the subgroup (Link) field in DocType 'Cohort Join Request'
#. Option for the 'Scope' (Select) field in DocType 'Cohort Web Page'
#. Label of the subgroup (Link) field in DocType 'LMS Enrollment'
#: lms/lms/doctype/cohort_join_request/cohort_join_request.json
#: lms/lms/doctype/cohort_web_page/cohort_web_page.json
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
msgid "Subgroup"
msgstr "Undergrupp"

#: frontend/src/components/Modals/AnnouncementModal.vue:20
#: frontend/src/components/Modals/EmailTemplateModal.vue:31
msgid "Subject"
msgstr "Ämne"

#: frontend/src/components/Modals/AnnouncementModal.vue:93
msgid "Subject is required"
msgstr "Ämne erfordras"

#: frontend/src/components/Assignment.vue:32
msgid "Submission"
msgstr "Inlämning"

#: frontend/src/components/Modals/AssignmentForm.vue:27
msgid "Submission Type"
msgstr "Inlämningstyp"

#: frontend/src/components/Assignment.vue:13
#: frontend/src/components/Assignment.vue:16
msgid "Submission by"
msgstr "Inlämning av"

#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseSubmission.vue:353
msgid "Submission saved!"
msgstr "Inlämning sparad!"

#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseSubmissions.vue:254
msgid "Submissions deleted successfully"
msgstr "Inlämningar raderade"

#: frontend/src/components/Modals/AssessmentModal.vue:9
#: frontend/src/components/Modals/BatchCourseModal.vue:9
#: frontend/src/components/Modals/EvaluationModal.vue:9
#: frontend/src/components/Quiz.vue:242 lms/templates/assignment.html:9
#: lms/templates/livecode/extension_footer.html:25
#: lms/templates/quiz/quiz.html:128 lms/templates/reviews.html:163
#: lms/www/new-sign-up.html:32
msgid "Submit"
msgstr "Godkänn"

#: frontend/src/components/BatchFeedback.vue:35
msgid "Submit Feedback"
msgstr "Godkänn  Återkoppling"

#: frontend/src/pages/PersonaForm.vue:43
msgid "Submit and Continue"
msgstr "Godkänn och Fortsätt"

#: frontend/src/components/Modals/JobApplicationModal.vue:23
msgid "Submit your resume to proceed with your application for this position. Upon submission, it will be shared with the job poster."
msgstr "Lämna in ditt CV för att gå vidare med din ansökan till denna tjänst. När du lämnat in ansökan kommer den att delas med jobbannons."

#: lms/templates/livecode/extension_footer.html:85
#: lms/templates/livecode/extension_footer.html:115
msgid "Submitted {0}"
msgstr "Inskickad {0}"

#. Label of the summary (Small Text) field in DocType 'LMS Certificate
#. Evaluation'
#: frontend/src/components/Modals/Event.vue:97
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
msgid "Summary"
msgstr "Översikt"

#. Option for the 'Day' (Select) field in DocType 'Evaluator Schedule'
#. Option for the 'Day' (Select) field in DocType 'LMS Certificate Request'
#: lms/lms/doctype/evaluator_schedule/evaluator_schedule.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
msgid "Sunday"
msgstr "Söndag"

#: lms/lms/api.py:1075
msgid "Suspicious pattern found in {0}: {1}"
msgstr "Misstänkt mönster hittat i {0}: {1}"

#. Name of a role
#: lms/job/doctype/job_opportunity/job_opportunity.json
#: lms/job/doctype/job_settings/job_settings.json
#: lms/job/doctype/lms_job_application/lms_job_application.json
#: lms/lms/doctype/cohort/cohort.json
#: lms/lms/doctype/cohort_join_request/cohort_join_request.json
#: lms/lms/doctype/cohort_mentor/cohort_mentor.json
#: lms/lms/doctype/cohort_staff/cohort_staff.json
#: lms/lms/doctype/cohort_subgroup/cohort_subgroup.json
#: lms/lms/doctype/course_chapter/course_chapter.json
#: lms/lms/doctype/course_evaluator/course_evaluator.json
#: lms/lms/doctype/course_lesson/course_lesson.json
#: lms/lms/doctype/exercise_latest_submission/exercise_latest_submission.json
#: lms/lms/doctype/exercise_submission/exercise_submission.json
#: lms/lms/doctype/function/function.json
#: lms/lms/doctype/industry/industry.json
#: lms/lms/doctype/invite_request/invite_request.json
#: lms/lms/doctype/lms_assignment/lms_assignment.json
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
#: lms/lms/doctype/lms_badge/lms_badge.json
#: lms/lms/doctype/lms_badge_assignment/lms_badge_assignment.json
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_batch_enrollment/lms_batch_enrollment.json
#: lms/lms/doctype/lms_batch_feedback/lms_batch_feedback.json
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
#: lms/lms/doctype/lms_category/lms_category.json
#: lms/lms/doctype/lms_certificate/lms_certificate.json
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
#: lms/lms/doctype/lms_course/lms_course.json
#: lms/lms/doctype/lms_course_interest/lms_course_interest.json
#: lms/lms/doctype/lms_course_mentor_mapping/lms_course_mentor_mapping.json
#: lms/lms/doctype/lms_course_progress/lms_course_progress.json
#: lms/lms/doctype/lms_course_review/lms_course_review.json
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
#: lms/lms/doctype/lms_exercise/lms_exercise.json
#: lms/lms/doctype/lms_live_class/lms_live_class.json
#: lms/lms/doctype/lms_live_class_participant/lms_live_class_participant.json
#: lms/lms/doctype/lms_mentor_request/lms_mentor_request.json
#: lms/lms/doctype/lms_payment/lms_payment.json
#: lms/lms/doctype/lms_program/lms_program.json
#: lms/lms/doctype/lms_programming_exercise/lms_programming_exercise.json
#: lms/lms/doctype/lms_programming_exercise_submission/lms_programming_exercise_submission.json
#: lms/lms/doctype/lms_question/lms_question.json
#: lms/lms/doctype/lms_quiz/lms_quiz.json
#: lms/lms/doctype/lms_quiz_submission/lms_quiz_submission.json
#: lms/lms/doctype/lms_settings/lms_settings.json
#: lms/lms/doctype/lms_source/lms_source.json
#: lms/lms/doctype/lms_timetable_template/lms_timetable_template.json
#: lms/lms/doctype/lms_video_watch_duration/lms_video_watch_duration.json
#: lms/lms/doctype/lms_zoom_settings/lms_zoom_settings.json
#: lms/lms/doctype/user_skill/user_skill.json
#: lms/lms/doctype/zoom_settings/zoom_settings.json
msgid "System Manager"
msgstr "System Ansvarig"

#. Label of the tags (Data) field in DocType 'LMS Course'
#: frontend/src/pages/CourseForm.vue:51
#: lms/lms/doctype/lms_course/lms_course.json
msgid "Tags"
msgstr "Taggar"

#: lms/templates/emails/community_course_membership.html:18
#: lms/templates/emails/mentor_request_creation_email.html:8
#: lms/templates/emails/mentor_request_status_update_email.html:7
msgid "Team School"
msgstr "Team Skola"

#. Option for the 'Collaboration Preference' (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Team Work"
msgstr "Teamarbete"

#. Label of the template (Link) field in DocType 'Cohort Web Page'
#. Label of the template (Link) field in DocType 'LMS Certificate'
#: frontend/src/components/Modals/BulkCertificates.vue:43
#: frontend/src/components/Modals/Event.vue:112
#: lms/lms/doctype/cohort_web_page/cohort_web_page.json
#: lms/lms/doctype/lms_certificate/lms_certificate.json
msgid "Template"
msgstr "Mall"

#: lms/lms/user.py:40
msgid "Temporarily Disabled"
msgstr "Tillfälligt Inaktiverad"

#: lms/lms/utils.py:440
msgid "Terms of Use"
msgstr "Villkor"

#. Label of the test_cases (Table) field in DocType 'LMS Programming Exercise'
#. Label of the test_cases (Table) field in DocType 'LMS Programming Exercise
#. Submission'
#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseForm.vue:29
#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseSubmission.vue:83
#: lms/lms/doctype/lms_programming_exercise/lms_programming_exercise.json
#: lms/lms/doctype/lms_programming_exercise_submission/lms_programming_exercise_submission.json
msgid "Test Cases"
msgstr "Testfall"

#: frontend/src/pages/QuizForm.vue:23
msgid "Test Quiz"
msgstr "Test Frågesport"

#. Label of the test_results (Small Text) field in DocType 'Exercise Latest
#. Submission'
#. Label of the test_results (Small Text) field in DocType 'Exercise
#. Submission'
#: lms/lms/doctype/exercise_latest_submission/exercise_latest_submission.json
#: lms/lms/doctype/exercise_submission/exercise_submission.json
msgid "Test Results"
msgstr "Test Resultat"

#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseForm.vue:82
msgid "Test this Exercise"
msgstr "Testa denna Övning"

#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseSubmission.vue:92
msgid "Test {0}"
msgstr "Testa {0}"

#. Label of the tests (Code) field in DocType 'LMS Exercise'
#: lms/lms/doctype/lms_exercise/lms_exercise.json
msgid "Tests"
msgstr "Tester"

#. Option for the 'Type' (Select) field in DocType 'LMS Assignment'
#. Option for the 'Type' (Select) field in DocType 'LMS Assignment Submission'
#: lms/lms/doctype/lms_assignment/lms_assignment.json
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
msgid "Text"
msgstr "Text"

#: frontend/src/components/BatchFeedback.vue:6
msgid "Thank you for providing your feedback."
msgstr "Tack för återkoppling."

#: lms/templates/emails/lms_course_interest.html:17
#: lms/templates/emails/lms_invite_request_approved.html:15
#: lms/templates/emails/mentor_request_creation_email.html:7
#: lms/templates/emails/mentor_request_status_update_email.html:6
msgid "Thanks and Regards"
msgstr "Tack och Hälsningar"

#: lms/lms/utils.py:1975
msgid "The batch is full. Please contact the Administrator."
msgstr "Gruppen är full. Kontakta administratör."

#: lms/templates/emails/batch_start_reminder.html:6
msgid "The batch you have enrolled for is starting tomorrow. Please be prepared and be on time for the session."
msgstr "Gruppen du har anmält dig till börjar i morgon. Var förberedd och kom i tid till sessionen."

#: lms/templates/emails/lms_course_interest.html:5
msgid "The course {0} is now available on {1}."
msgstr "Kurs {0} är nu tillgänglig på {1}."

#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.py:53
msgid "The evaluator of this course is unavailable from {0} to {1}. Please select a date after {1}"
msgstr "Utvärderare av denna kurs är inte tillgänglig från {0} till {1}. Välj ett datum efter {1}"

#: lms/templates/quiz/quiz.html:24
msgid "The quiz has a time limit. For each question you will be given {0} seconds."
msgstr "Frågesport är tidsbegränsad. För varje fråga får du {0} sekunder."

#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.py:71
msgid "The slot is already booked by another participant."
msgstr "Tiden är redan bokad av en annan deltagare."

#: lms/patches/create_mentor_request_email_templates.py:40
msgid "The status of your application has changed."
msgstr "Status för din ansökan har förändrats."

#: frontend/src/components/CreateOutline.vue:12
msgid "There are no chapters in this course. Create and manage chapters from here."
msgstr "Det finns inga kapitel i denna kurs. Skapa och hantera kapitel härifrån."

#: lms/lms/doctype/lms_batch/lms_batch.py:107
msgid "There are no seats available in this batch."
msgstr "Det finns inga platser tillgängliga i denna grupp."

#: frontend/src/components/BatchStudents.vue:155
msgid "There are no students in this batch."
msgstr "Det finns inga studenter i denna grupp."

#: frontend/src/pages/AssignmentSubmissionList.vue:70
msgid "There are no submissions for this assignment."
msgstr "Det finns inga inlämningar för denna uppgift."

#: frontend/src/components/EmptyState.vue:11
msgid "There are no {0} currently. Keep an eye out, fresh learning experiences are on the way!"
msgstr "Det finns inga {0} för närvarande. Håll utkik, nya inlärningsupplevelser är på väg!"

#: lms/templates/course_list.html:14
msgid "There are no {0} on this site."
msgstr "Det finns ingen {0} på denna webbplats."

#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.py:44
msgid "There has been an update on your submission for assignment {0}"
msgstr "Det har skett uppdatering av din inlämning för uppgift {0}"

#: lms/lms/doctype/lms_quiz_submission/lms_quiz_submission.py:59
msgid "There has been an update on your submission. You have got a score of {0} for the quiz {1}"
msgstr "Det har skett uppdatering av din inlämning. Du har fått resultat av {0} för frågesport {1}"

#. Description of the 'section_break_ubxi' (Section Break) field in DocType
#. 'LMS Batch'
#: lms/lms/doctype/lms_batch/lms_batch.json
msgid "These customisations will work on the main batch page."
msgstr "Dessa anpassningar kommer att fungera på huvudgrupp sida."

#: frontend/src/pages/Badge.vue:14
msgid "This badge has been awarded to {0} on {1}."
msgstr "Detta emblem är tilldelad {0} {1}."

#. Label of the expire (Check) field in DocType 'Certification'
#: lms/lms/doctype/certification/certification.json
msgid "This certificate does no expire"
msgstr "Detta certifikat upphör inte att gälla"

#: frontend/src/components/LiveClass.vue:83
msgid "This class has ended"
msgstr "Denna klass har avslutats"

#: frontend/src/components/CourseCardOverlay.vue:126
msgid "This course has:"
msgstr "Denna kurs har:"

#: lms/lms/utils.py:1818
msgid "This course is free."
msgstr "Denna kurs är gratis."

#. Description of the 'Meta Description' (Small Text) field in DocType 'LMS
#. Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "This description will be shown on lists and pages without meta description"
msgstr "Denna beskrivning kommer att visas på listor och sidor utan metabeskrivning"

#. Description of the 'Meta Image' (Attach Image) field in DocType 'LMS
#. Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "This image will be shown on lists and pages that don't have an image by default"
msgstr "Denna bild kommer att visas på listor och sidor som inte har en bild som standard"

#: frontend/src/pages/Lesson.vue:30
msgid "This lesson is locked"
msgstr "Denna lektion är låst"

#: frontend/src/pages/Lesson.vue:35
msgid "This lesson is not available for preview. Please enroll in the course to access it."
msgstr "Denna lektion är inte tillgänglig för förhandsgranskning. Registrera dig för kurs för att få tillgång till den."

#: lms/lms/widgets/NoPreviewModal.html:16
msgid "This lesson is not available for preview. Please join the course to access it."
msgstr "Denna lektion är inte tillgänglig för förhandsgranskning. Gå med i kurs för att få tillgång till den."

#: frontend/src/components/Quiz.vue:11 lms/templates/quiz/quiz.html:6
msgid "This quiz consists of {0} questions."
msgstr "Denna frågesport består av {0} frågor."

#: frontend/src/components/AppSidebar.vue:75
#: frontend/src/components/AppSidebar.vue:115
msgid "This site is being updated. You will not be able to make any changes. Full access will be restored shortly."
msgstr "Denna webbplats håller på att uppdateras. Du kommer inte att kunna göra några ändringar. Full åtkomst kommer att återställas inom kort."

#: frontend/src/components/VideoBlock.vue:5
msgid "This video contains {0} {1}:"
msgstr "Denna video innehåller {0} {1}:"

#. Option for the 'Day' (Select) field in DocType 'Evaluator Schedule'
#. Option for the 'Day' (Select) field in DocType 'LMS Certificate Request'
#: lms/lms/doctype/evaluator_schedule/evaluator_schedule.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
msgid "Thursday"
msgstr "Torsdag"

#. Label of the time (Time) field in DocType 'LMS Live Class'
#: frontend/src/components/Modals/Event.vue:48
#: frontend/src/components/Modals/LiveClassModal.vue:52
#: frontend/src/components/Quiz.vue:58
#: lms/lms/doctype/lms_live_class/lms_live_class.json
msgid "Time"
msgstr "Tid"

#. Label of the time (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Time Preference"
msgstr "Tidspreferens"

#: frontend/src/components/VideoBlock.vue:140
msgid "Time for a Quiz"
msgstr "Dags för Frågesport"

#: frontend/src/components/Modals/QuizInVideo.vue:13
msgid "Time in Video"
msgstr "Tid i Video"

#: frontend/src/components/Modals/QuizInVideo.vue:220
msgid "Time in Video (minutes)"
msgstr "Tid i Video (minuter)"

#: frontend/src/components/Modals/QuizInVideo.vue:173
msgid "Time in video exceeds the total duration of the video."
msgstr "Tid i video överskrider total tid för video."

#: frontend/src/components/Modals/LiveClassModal.vue:44
msgid "Time must be in 24 hour format (HH:mm). Example 11:30 or 22:00"
msgstr "Tid måste vara i 24 timmars format (HH:mm). Exempel 11:30 eller 22:00"

#. Label of the schedule_tab (Tab Break) field in DocType 'LMS Batch'
#. Label of the timetable (Table) field in DocType 'LMS Batch'
#. Label of the timetable (Table) field in DocType 'LMS Timetable Template'
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_timetable_template/lms_timetable_template.json
msgid "Timetable"
msgstr "Tidtabell"

#. Label of the timetable_legends (Table) field in DocType 'LMS Batch'
#. Label of the timetable_legends (Table) field in DocType 'LMS Timetable
#. Template'
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_timetable_template/lms_timetable_template.json
msgid "Timetable Legends"
msgstr "Tidtabell Förklaring"

#. Label of the timetable_template (Link) field in DocType 'LMS Batch'
#: lms/lms/doctype/lms_batch/lms_batch.json
msgid "Timetable Template"
msgstr "Tidtabell Mall"

#. Label of the timezone (Data) field in DocType 'LMS Batch'
#. Label of the timezone (Data) field in DocType 'LMS Certificate Request'
#. Label of the timezone (Data) field in DocType 'LMS Live Class'
#: frontend/src/components/Modals/LiveClassModal.vue:59
#: frontend/src/pages/BatchForm.vue:107
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
#: lms/lms/doctype/lms_live_class/lms_live_class.json
msgid "Timezone"
msgstr "Tidszon"

#: lms/templates/emails/batch_confirmation.html:21
#: lms/templates/emails/batch_start_reminder.html:16
#: lms/templates/emails/live_class_reminder.html:16
msgid "Timings:"
msgstr "Tidpunkter:"

#. Label of the title (Data) field in DocType 'Cohort'
#. Label of the title (Data) field in DocType 'Cohort Subgroup'
#. Label of the title (Data) field in DocType 'Cohort Web Page'
#. Label of the title (Data) field in DocType 'Course Chapter'
#. Label of the title (Data) field in DocType 'Course Lesson'
#. Label of the title (Data) field in DocType 'LMS Assignment'
#. Label of the title (Data) field in DocType 'LMS Badge'
#. Label of the title (Data) field in DocType 'LMS Batch'
#. Label of the title (Data) field in DocType 'LMS Batch Old'
#. Label of the title (Data) field in DocType 'LMS Course'
#. Label of the title (Data) field in DocType 'LMS Exercise'
#. Label of the title (Data) field in DocType 'LMS Live Class'
#. Label of the title (Data) field in DocType 'LMS Program'
#. Label of the title (Data) field in DocType 'LMS Programming Exercise'
#. Label of the title (Data) field in DocType 'LMS Quiz'
#. Label of the title (Data) field in DocType 'LMS Sidebar Item'
#. Label of the title (Data) field in DocType 'LMS Timetable Template'
#. Label of the title (Data) field in DocType 'Work Experience'
#: frontend/src/components/Modals/AssignmentForm.vue:20
#: frontend/src/components/Modals/DiscussionModal.vue:18
#: frontend/src/components/Modals/LiveClassModal.vue:23
#: frontend/src/pages/Assignments.vue:162 frontend/src/pages/BatchForm.vue:20
#: frontend/src/pages/CourseForm.vue:30 frontend/src/pages/JobForm.vue:20
#: frontend/src/pages/ProgramForm.vue:11
#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseForm.vue:17
#: frontend/src/pages/Programs.vue:101 frontend/src/pages/QuizForm.vue:56
#: frontend/src/pages/Quizzes.vue:115 frontend/src/pages/Quizzes.vue:229
#: lms/lms/doctype/cohort/cohort.json
#: lms/lms/doctype/cohort_subgroup/cohort_subgroup.json
#: lms/lms/doctype/cohort_web_page/cohort_web_page.json
#: lms/lms/doctype/course_chapter/course_chapter.json
#: lms/lms/doctype/course_lesson/course_lesson.json
#: lms/lms/doctype/lms_assignment/lms_assignment.json
#: lms/lms/doctype/lms_badge/lms_badge.json
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
#: lms/lms/doctype/lms_course/lms_course.json
#: lms/lms/doctype/lms_exercise/lms_exercise.json
#: lms/lms/doctype/lms_live_class/lms_live_class.json
#: lms/lms/doctype/lms_program/lms_program.json
#: lms/lms/doctype/lms_programming_exercise/lms_programming_exercise.json
#: lms/lms/doctype/lms_quiz/lms_quiz.json
#: lms/lms/doctype/lms_sidebar_item/lms_sidebar_item.json
#: lms/lms/doctype/lms_timetable_template/lms_timetable_template.json
#: lms/lms/doctype/work_experience/work_experience.json
msgid "Title"
msgstr "Benämning"

#: frontend/src/components/Modals/ChapterModal.vue:172
msgid "Title is required"
msgstr "Benämning erfordras"

#. Label of the unavailable_to (Date) field in DocType 'Course Evaluator'
#: frontend/src/pages/ProfileEvaluator.vue:112
#: lms/lms/doctype/course_evaluator/course_evaluator.json
msgid "To"
msgstr "Till"

#. Label of the to_date (Date) field in DocType 'Work Experience'
#: lms/lms/doctype/work_experience/work_experience.json
msgid "To Date"
msgstr "Till Datum"

#: lms/lms/utils.py:1829
msgid "To join this batch, please contact the Administrator."
msgstr "För att gå med i denna grupp, kontakta Administratör."

#: lms/lms/user.py:41
msgid "Too many users signed up recently, so the registration is disabled. Please try back in an hour"
msgstr "Alltför många Användare registrerade sig nyligen, så registrering är inaktiverad. Försök igen om en timme"

#: frontend/src/pages/Billing.vue:53
msgid "Total"
msgstr "Totalt"

#. Label of the total_marks (Int) field in DocType 'LMS Quiz'
#: frontend/src/pages/QuizForm.vue:73 frontend/src/pages/Quizzes.vue:235
#: lms/lms/doctype/lms_quiz/lms_quiz.json
msgid "Total Marks"
msgstr "Totalt antal markeringar"

#: lms/lms/web_template/lms_statistics/lms_statistics.html:14
#: lms/templates/statistics.html:12
msgid "Total Signups"
msgstr "Totalt antal registreringar"

#: frontend/src/components/Modals/FeedbackModal.vue:11
msgid "Training Feedback"
msgstr "Utbildning Återkoppling"

#. Option for the 'Location Preference' (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Travel"
msgstr "Resa"

#: frontend/src/components/Quiz.vue:284 lms/templates/quiz/quiz.html:131
msgid "Try Again"
msgstr "Försök igen"

#. Option for the 'Day' (Select) field in DocType 'Evaluator Schedule'
#. Option for the 'Day' (Select) field in DocType 'LMS Certificate Request'
#: lms/lms/doctype/evaluator_schedule/evaluator_schedule.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
msgid "Tuesday"
msgstr "Tisdag"

#: frontend/src/pages/ProfileAbout.vue:86
msgid "Twitter"
msgstr "Twitter"

#. Label of the type (Select) field in DocType 'Job Opportunity'
#. Label of the type (Select) field in DocType 'LMS Assignment'
#. Label of the type (Select) field in DocType 'LMS Assignment Submission'
#. Label of the type (Select) field in DocType 'LMS Question'
#. Label of the type (Select) field in DocType 'LMS Quiz Question'
#: frontend/src/components/Modals/AssessmentModal.vue:22
#: frontend/src/components/Modals/Question.vue:44
#: frontend/src/pages/Assignments.vue:40 frontend/src/pages/Assignments.vue:167
#: frontend/src/pages/JobForm.vue:25 frontend/src/pages/Jobs.vue:65
#: frontend/src/pages/ProgrammingExercises/ProgrammingExercises.vue:53
#: lms/job/doctype/job_opportunity/job_opportunity.json
#: lms/lms/doctype/lms_assignment/lms_assignment.json
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
#: lms/lms/doctype/lms_question/lms_question.json
#: lms/lms/doctype/lms_quiz_question/lms_quiz_question.json
#: lms/templates/assessments.html:14
msgid "Type"
msgstr "Typ"

#: frontend/src/utils/markdownParser.js:11
msgid "Type '/' for commands or select text to format"
msgstr "Skriv \"/\" för kommandon eller markera text till format"

#: frontend/src/components/Quiz.vue:646
msgid "Type your answer"
msgstr "Skriv ditt svar"

#. Option for the 'Grade Type' (Select) field in DocType 'Education Detail'
#: lms/lms/doctype/education_detail/education_detail.json
msgid "UK Grading  (e.g. 1st, 2:2)"
msgstr "UK Betyg (t.ex. 1:a, 2:2)"

#. Option for the 'Type' (Select) field in DocType 'LMS Assignment'
#. Option for the 'Type' (Select) field in DocType 'LMS Assignment Submission'
#: lms/lms/doctype/lms_assignment/lms_assignment.json
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
msgid "URL"
msgstr "URL"

#. Label of the uuid (Data) field in DocType 'LMS Live Class'
#: lms/lms/doctype/lms_live_class/lms_live_class.json
msgid "UUID"
msgstr "UUID"

#. Label of the unavailability_section (Section Break) field in DocType 'Course
#. Evaluator'
#: lms/lms/doctype/course_evaluator/course_evaluator.json
msgid "Unavailability"
msgstr "Otillgänglighet"

#: frontend/src/pages/ProfileEvaluator.vue:259
msgid "Unavailability updated successfully"
msgstr "Otillgänglighet uppdaterad"

#: lms/lms/doctype/course_evaluator/course_evaluator.py:29
msgid "Unavailable From Date cannot be greater than Unavailable To Date"
msgstr "Otillgänglig Från datum kan inte vara senare än Otillgänglig till datum"

#. Option for the 'Status' (Select) field in DocType 'LMS Course'
#: lms/lms/doctype/lms_course/lms_course.json
msgid "Under Review"
msgstr "Under Recension"

#. Option for the 'Visibility' (Select) field in DocType 'LMS Batch Old'
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
msgid "Unlisted"
msgstr "Ej Listad"

#: frontend/src/pages/Batches.vue:284 frontend/src/pages/Courses.vue:322
msgid "Unpublished"
msgstr "Opublicerad"

#: frontend/src/components/Modals/EditCoverImage.vue:60
#: frontend/src/components/UnsplashImageBrowser.vue:54
msgid "Unsplash"
msgstr "Unsplash"

#. Label of the unsplash_access_key (Data) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Unsplash Access Key"
msgstr "Unsplash Access Nyckel"

#. Option for the 'Role Preference' (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Unstructured Role"
msgstr "Ostrukturerad Roll"

#. Option for the 'Status' (Select) field in DocType 'Cohort'
#. Option for the 'Status' (Select) field in DocType 'LMS Certificate Request'
#. Label of the upcoming (Check) field in DocType 'LMS Course'
#: frontend/src/pages/Batches.vue:282 frontend/src/pages/CourseForm.vue:162
#: frontend/src/pages/Courses.vue:313 lms/lms/doctype/cohort/cohort.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
#: lms/lms/doctype/lms_course/lms_course.json
msgid "Upcoming"
msgstr "Kommande"

#: frontend/src/pages/Batch.vue:187
msgid "Upcoming Batches"
msgstr "Kommande grupper"

#: frontend/src/components/UpcomingEvaluations.vue:5
#: lms/templates/upcoming_evals.html:3
msgid "Upcoming Evaluations"
msgstr "Kommande utvärderingar"

#: frontend/src/components/Settings/BrandSettings.vue:24
#: frontend/src/components/Settings/PaymentSettings.vue:27
#: frontend/src/components/Settings/SettingDetails.vue:23
msgid "Update"
msgstr "Uppdatera"

#: lms/templates/emails/community_course_membership.html:11
msgid "Update Password"
msgstr "Uppdatera lösenord"

#: frontend/src/pages/BatchForm.vue:217 frontend/src/pages/CourseForm.vue:108
msgid "Upload"
msgstr "Ladda upp"

#: frontend/src/components/Assignment.vue:81
msgid "Upload File"
msgstr "Ladda upp fil"

#: frontend/src/components/Assignment.vue:80
msgid "Uploading {0}%"
msgstr "Ladda Upp {0}%"

#: frontend/src/components/Modals/EmailTemplateModal.vue:38
msgid "Use HTML"
msgstr "Använd HTML"

#. Label of the user (Link) field in DocType 'LMS Job Application'
#. Label of the email (Link) field in DocType 'Cohort Staff'
#. Label of the user (Link) field in DocType 'LMS Course Interest'
#: lms/job/doctype/lms_job_application/lms_job_application.json
#: lms/lms/doctype/cohort_staff/cohort_staff.json
#: lms/lms/doctype/lms_course_interest/lms_course_interest.json
msgid "User"
msgstr "Användare"

#. Label of the user_category (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json lms/templates/signup-form.html:17
msgid "User Category"
msgstr "Användarkategori"

#. Label of the user_field (Select) field in DocType 'LMS Badge'
#: lms/lms/doctype/lms_badge/lms_badge.json
msgid "User Field"
msgstr "Användare Fält"

#. Label of the user_image (Attach Image) field in DocType 'Course Evaluator'
#: lms/lms/doctype/course_evaluator/course_evaluator.json
msgid "User Image"
msgstr "Användare Bild"

#. Option for the 'Type' (Select) field in DocType 'LMS Question'
#. Option for the 'Type' (Select) field in DocType 'LMS Quiz Question'
#: lms/lms/doctype/lms_question/lms_question.json
#: lms/lms/doctype/lms_quiz_question/lms_quiz_question.json
msgid "User Input"
msgstr "Användarinmatning"

#. Name of a DocType
#: lms/lms/doctype/user_skill/user_skill.json
msgid "User Skill"
msgstr "Användarkompetens"

#: lms/job/doctype/job_opportunity/job_opportunity.py:40
msgid "User {0} has reported the job post {1}"
msgstr "Användare {0} har rapporterat jobb post {1}"

#. Label of the username (Data) field in DocType 'Course Evaluator'
#. Label of the username (Data) field in DocType 'Invite Request'
#: lms/lms/doctype/course_evaluator/course_evaluator.json
#: lms/lms/doctype/invite_request/invite_request.json
msgid "Username"
msgstr "Användarnamn"

#. Label of a shortcut in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "Users"
msgstr "Användare"

#. Label of the answer (Small Text) field in DocType 'LMS Quiz Result'
#: lms/lms/doctype/lms_quiz_result/lms_quiz_result.json
msgid "Users Response"
msgstr "Användarens svar"

#: lms/templates/signup-form.html:83
msgid "Valid email and name required"
msgstr "Giltig e-post och namn erfordras"

#. Label of the value (Rating) field in DocType 'LMS Batch Feedback'
#: lms/lms/doctype/lms_batch_feedback/lms_batch_feedback.json
msgid "Value"
msgstr "Värde"

#. Option for the 'Event' (Select) field in DocType 'LMS Badge'
#: lms/lms/doctype/lms_badge/lms_badge.json
msgid "Value Change"
msgstr "Värde Förändring"

#. Label of the video_link (Data) field in DocType 'LMS Course'
#: lms/lms/doctype/lms_course/lms_course.json
msgid "Video Embed Link"
msgstr "Videoinbäddning Länk"

#: frontend/src/pages/Lesson.vue:19
msgid "Video Statistics"
msgstr "Videostatistik"

#: frontend/src/components/Modals/VideoStatistics.vue:6
msgid "Video Statistics for {0}"
msgstr "Videostatistik för {0}"

#: frontend/src/pages/Notifications.vue:39
msgid "View"
msgstr "Visa"

#: frontend/src/components/CertificationLinks.vue:10
#: frontend/src/components/Modals/Event.vue:67
msgid "View Certificate"
msgstr "Visa Certifikat"

#: frontend/src/components/BatchFeedback.vue:56
msgid "View all feedback"
msgstr "Visa alla återkopplingar"

#. Label of the visibility (Select) field in DocType 'LMS Batch Old'
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
msgid "Visibility"
msgstr "Synlighet"

#: frontend/src/components/BatchOverlay.vue:69
msgid "Visit Batch"
msgstr "Besök Omgång"

#: frontend/src/pages/JobDetail.vue:41
msgid "Visit Website"
msgstr "Besök Webbplats"

#: lms/templates/emails/batch_confirmation.html:25
msgid "Visit the following link to view your "
msgstr "Besök följande länk för att se din "

#: lms/templates/emails/batch_start_reminder.html:23
#: lms/templates/emails/live_class_reminder.html:20
msgid "Visit your batch"
msgstr "Besök din grupp"

#. Label of the internship (Table) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Volunteering or Internship"
msgstr "Volontärarbete eller Praktik"

#. Label of the watch_time (Data) field in DocType 'LMS Video Watch Duration'
#: frontend/src/components/Modals/VideoStatistics.vue:25
#: lms/lms/doctype/lms_video_watch_duration/lms_video_watch_duration.json
msgid "Watch Time"
msgstr "Visningstid"

#: lms/templates/emails/batch_confirmation.html:6
msgid "We are pleased to inform you that you have been enrolled in our upcoming batch. Congratulations!"
msgstr "Vi är glada att informera dig om att du har blivit antagen i vår kommande grupp. Grattis!"

#: lms/templates/emails/payment_reminder.html:7
msgid "We have a limited number of seats, and they won't be available for long!"
msgstr "Vi har ett begränsat antal platser, och de kommer inte att finnas kvar länge till!"

#: lms/templates/emails/payment_reminder.html:4
msgid "We noticed that you started enrolling in the"
msgstr "Vi märkte att du började registrera dig i"

#. Label of the web_page (Link) field in DocType 'LMS Sidebar Item'
#: frontend/src/components/Modals/PageModal.vue:23
#: lms/lms/doctype/lms_sidebar_item/lms_sidebar_item.json
msgid "Web Page"
msgstr "Webbsida"

#: frontend/src/components/Modals/PageModal.vue:80
msgid "Web page added to sidebar"
msgstr "Webbsida tillagd i sidofält"

#. Option for the 'Day' (Select) field in DocType 'Evaluator Schedule'
#. Option for the 'Day' (Select) field in DocType 'LMS Certificate Request'
#: lms/lms/doctype/evaluator_schedule/evaluator_schedule.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
msgid "Wednesday"
msgstr "Onsdag"

#: lms/lms/doctype/invite_request/invite_request.py:40
#: lms/templates/emails/lms_invite_request_approved.html:4
msgid "Welcome to {0}!"
msgstr "Välkommen till {0}!"

#: frontend/src/pages/PersonaForm.vue:32
msgid "What best describes your role?"
msgstr "Vad beskriver bäst din roll?"

#: frontend/src/components/LessonHelp.vue:6
msgid "What does include in preview mean?"
msgstr "Vad betyder inkludera i förhandsvisning?"

#: frontend/src/pages/PersonaForm.vue:21
msgid "What is your use case for Frappe Learning?"
msgstr "Vad är ditt användningsfall för Frappe Learning?"

#: lms/templates/courses_under_review.html:15
msgid "When a course gets submitted for review, it will be listed here."
msgstr "När kurs lämnas in för granskning kommer den att listas här."

#: frontend/src/pages/Billing.vue:106
msgid "Where did you hear about us?"
msgstr "Var har du hört talas om oss?"

#: lms/templates/emails/certification.html:10
msgid "With this certification, you can now showcase your updated skills and share your achievement with your colleagues and on LinkedIn. To access your certificate, please click on the link provided below. Make sure you are logged in to the portal."
msgstr "Med denna certifiering kan du nu visa upp dina uppdaterade färdigheter och dela dina prestationer med dina kollegor och på LinkedIn. För att få tillgång till ditt certifikat, klicka på länk nedan. Se till att du är inloggad på Portal."

#. Option for the 'Status' (Select) field in DocType 'LMS Mentor Request'
#: lms/lms/doctype/lms_mentor_request/lms_mentor_request.json
msgid "Withdrawn"
msgstr "Återkallad"

#. Label of the work_environment (Section Break) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Work Environment"
msgstr "Arbetsmiljö"

#. Label of the work_experience (Table) field in DocType 'User'
#. Name of a DocType
#: lms/fixtures/custom_field.json
#: lms/lms/doctype/work_experience/work_experience.json
msgid "Work Experience"
msgstr "Arbetsliv Erfarenhet"

#. Label of the work_experience_details (Section Break) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Work Experience Details"
msgstr "Arbetsliv Erfarenhet Detaljer"

#: frontend/src/components/CourseReviews.vue:8
#: frontend/src/components/Modals/ReviewModal.vue:5
#: lms/templates/reviews.html:117
msgid "Write a Review"
msgstr "Skriv en Recension"

#: lms/templates/reviews.html:31 lms/templates/reviews.html:103
#: lms/templates/reviews_cta.html:3 lms/templates/reviews_cta.html:7
msgid "Write a review"
msgstr "Skriv en recension"

#: frontend/src/components/Assignment.vue:123
msgid "Write your answer here"
msgstr "Skriv ditt svar här"

#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.py:95
msgid "You already have an evaluation on {0} at {1} for the course {2}."
msgstr "Du har redan utvärdering {0} kl. {1} för kurs {2}."

#: frontend/src/pages/CourseCertification.vue:14
msgid "You are already certified for this course. Click on the card below to open your certificate."
msgstr "Du är redan certifierad för denna kurs. Klicka på kort nedan för att öppna ditt certifikat."

#: lms/lms/api.py:234
msgid "You are already enrolled for this batch."
msgstr "Du är redan inskriven för denna grupp."

#: lms/lms/api.py:226
msgid "You are already enrolled for this course."
msgstr "Du är redan inskriven på denna kurs."

#: frontend/src/pages/Batch.vue:169
msgid "You are not a member of this batch. Please checkout our upcoming batches."
msgstr "Du är inte i denna omgång. Kolla in våra kommande omgångar."

#: lms/lms/doctype/lms_batch_old/lms_batch_old.py:20
msgid "You are not a mentor of the course {0}"
msgstr "Du är inte mentor för kurs {0}"

#: frontend/src/pages/SCORMChapter.vue:22
msgid "You are not enrolled in this course. Please enroll to access this lesson."
msgstr "Du är inte inskriven i denna kurs. Anmäl dig för att få tillgång till denna lektion."

#: lms/templates/emails/lms_course_interest.html:13
#: lms/templates/emails/lms_invite_request_approved.html:11
msgid "You can also copy-paste following link in your browser"
msgstr "Du kan också kopiera och klistra in följande länk i din webbläsare"

#: lms/templates/quiz/quiz.html:18
msgid "You can attempt this quiz only {0} {1}"
msgstr "Du kan bara försöka med denna frågesport {0} {1}"

#: frontend/src/components/Quiz.vue:37
msgid "You can attempt this quiz {0}."
msgstr "Du kan prova detta frågesport {0}."

#: lms/templates/emails/job_application.html:6
msgid "You can find their resume attached to this email."
msgstr "Du kan hitta deras CV bifogat till detta e-post meddelande."

#: frontend/src/pages/ProfileEvaluator.vue:14
msgid "You cannot change the availability when the site is being updated."
msgstr "Du kan inte ändra tillgänglighet när webbplats uppdateras."

#: frontend/src/pages/ProfileRoles.vue:12
msgid "You cannot change the roles in read-only mode."
msgstr "Du kan inte ändra rollerna i skrivskyddat läge."

#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.py:115
msgid "You cannot schedule evaluations after {0}."
msgstr "Du kan inte schemalägga utvärderingar efter {0}."

#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.py:104
msgid "You cannot schedule evaluations for past slots."
msgstr "Du kan inte schemalägga utvärderingar för förflutna tider."

#: frontend/src/components/NoPermission.vue:11
msgid "You do not have permission to access this page."
msgstr "Du har inte behörighet att komma åt denna sida."

#: lms/templates/notifications.html:27
msgid "You don't have any notifications."
msgstr "Du har inga aviseringar."

#: lms/templates/quiz/quiz.js:137
msgid "You got"
msgstr "Du har"

#: frontend/src/components/Quiz.vue:265
#, python-format
msgid "You got {0}% correct answers with a score of {1} out of {2}"
msgstr "Du fick {0}% rätta svar med resultat på {1} av {2}"

#: lms/templates/emails/live_class_reminder.html:6
msgid "You have a live class scheduled tomorrow. Please be prepared and be on time for the session."
msgstr "Du har en liveklass schemalagd i morgon. Var förberedd och kom i tid till sessionen."

#: lms/job/doctype/lms_job_application/lms_job_application.py:22
msgid "You have already applied for this job."
msgstr "Du har redan sökt detta jobb."

#: frontend/src/components/Quiz.vue:96 lms/templates/quiz/quiz.html:43
msgid "You have already exceeded the maximum number of attempts allowed for this quiz."
msgstr "Du har redan överskridit maximal antalet försök som tillåts för denna frågesport."

#: lms/lms/api.py:258
msgid "You have already purchased the certificate for this course."
msgstr "Du har redan köpt certifikat för denna kurs."

#: lms/lms/doctype/lms_course_review/lms_course_review.py:17
msgid "You have already reviewed this course"
msgstr "Du har redan granskat denna kurs"

#: frontend/src/pages/JobDetail.vue:57
msgid "You have applied"
msgstr "Du har ansökt"

#: frontend/src/components/BatchOverlay.vue:159
msgid "You have been enrolled in this batch"
msgstr "Du har blivit registrerad i denna grupp"

#: frontend/src/components/CourseCardOverlay.vue:229
msgid "You have been enrolled in this course"
msgstr "Du har blivit registrerad på denna kurs"

#: lms/lms/doctype/lms_quiz_submission/lms_quiz_submission.py:30
msgid "You have exceeded the maximum number of attempts ({0}) for this quiz"
msgstr "Du har överskridit det maximala antalet försök ({0}) för denna frågesport"

#: lms/lms/doctype/lms_quiz_submission/lms_quiz_submission.py:56
msgid "You have got a score of {0} for the quiz {1}"
msgstr "Du har fått resultat av {0} för frågesport {1}"

#: lms/lms/widgets/NoPreviewModal.html:12
msgid "You have opted to be notified for this course. You will receive an email when the course becomes available."
msgstr "Du har valt att bli meddelad om denna kurs. Du kommer att få ett e-post meddelande när kursen blir tillgänglig."

#: frontend/src/components/CourseCardOverlay.vue:217
msgid "You need to login first to enroll for this course"
msgstr "Du måste logga in först för att registrera dig till denna kurs"

#: frontend/src/components/Quiz.vue:7
msgid "You will have to complete the quiz to continue the video"
msgstr "Slutför frågesport för att fortsätta video"

#: frontend/src/components/Quiz.vue:30 lms/templates/quiz/quiz.html:11
#, python-format
msgid "You will have to get {0}% correct answers in order to pass the quiz."
msgstr "Du måste få {0}% korrekta svar för att klara frågesport."

#: lms/templates/emails/mentor_request_creation_email.html:4
msgid "You've applied to become a mentor for this course. Your request is currently under review."
msgstr "Du har ansökt om att bli mentor för denna kurs. Din begäran är för närvarande under granskning."

#: frontend/src/components/Assignment.vue:58
msgid "You've successfully submitted the assignment."
msgstr "Du har lyckats lämna in uppgift."

#. Label of the youtube (Data) field in DocType 'Course Lesson'
#: lms/lms/doctype/course_lesson/course_lesson.json
msgid "YouTube Video URL"
msgstr "YouTube Video URL"

#. Description of the 'YouTube Video URL' (Data) field in DocType 'Course
#. Lesson'
#: lms/lms/doctype/course_lesson/course_lesson.json
msgid "YouTube Video will appear at the top of the lesson."
msgstr "YouTube Video visas överst i lektion."

#: lms/www/new-sign-up.html:56
msgid "Your Account has been successfully created!"
msgstr "Ditt konto är skapad!"

#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseSubmission.vue:119
msgid "Your Output"
msgstr "Utdata"

#: lms/lms/doctype/lms_batch/lms_batch.py:362
msgid "Your batch {0} is starting tomorrow"
msgstr "Din grupp {0} börjar imorgon"

#: frontend/src/pages/ProfileEvaluator.vue:134
msgid "Your calendar is set."
msgstr "Din kalender är konfigurerad."

#: lms/lms/doctype/lms_live_class/lms_live_class.py:90
msgid "Your class on {0} is today"
msgstr "Din lektion {0} är idag"

#: frontend/src/components/Modals/EmailTemplateModal.vue:35
msgid "Your enrollment in {{ batch_name }} is confirmed"
msgstr "Din inskrivning till {{ batch_name }} är bekräftad"

#: lms/lms/notification/certificate_request_reminder/certificate_request_reminder.html:3
#: lms/templates/emails/certificate_request_notification.html:3
msgid "Your evaluation for the course {0} has been scheduled on {1} at {2} {3}."
msgstr "Din utvärdering av kurs {0} är schemalagd {1} kl. {2} {3}."

#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.py:125
msgid "Your evaluation slot has been booked"
msgstr "Din utvärdering plats är bokad"

#: lms/templates/emails/certificate_request_notification.html:5
msgid "Your evaluator is {0}"
msgstr "Din utvärderare är {0}"

#: lms/templates/emails/mentor_request_status_update_email.html:4
msgid "Your request to join us as a mentor for the course"
msgstr "Din begäran om att bli mentor för kurs"

#: lms/templates/quiz/quiz.js:140
msgid "Your score is"
msgstr "Ditt resultat är"

#: frontend/src/components/Quiz.vue:258
msgid "Your submission has been successfully saved. The instructor will review and grade it shortly, and you'll be notified of your final result."
msgstr "Din inlämning är sparad. Lärare kommer att granska och betygsätta den inom kort och du kommer att få meddelande om ditt slutresultat."

#: frontend/src/pages/Lesson.vue:8
msgid "Zen Mode"
msgstr "Zen Läge"

#. Label of the zoom_account (Link) field in DocType 'LMS Batch'
#. Label of the zoom_account (Link) field in DocType 'LMS Live Class'
#: frontend/src/pages/BatchForm.vue:164
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_live_class/lms_live_class.json
msgid "Zoom Account"
msgstr "Zoom Konto"

#: frontend/src/components/Modals/ZoomAccountModal.vue:158
msgid "Zoom Account created successfully"
msgstr "Zooma konto skapad"

#: frontend/src/components/Modals/ZoomAccountModal.vue:195
msgid "Zoom Account updated successfully"
msgstr "Zoom konto uppdaterad"

#. Name of a DocType
#: lms/lms/doctype/zoom_settings/zoom_settings.json
msgid "Zoom Settings"
msgstr "Zoom Inställningar"

#: frontend/src/components/StudentHeatmap.vue:6
msgid "activities"
msgstr "aktiviteter"

#: frontend/src/components/StudentHeatmap.vue:6
msgid "activity"
msgstr "aktivitet"

#: frontend/src/components/JobCard.vue:26 frontend/src/pages/JobDetail.vue:108
msgid "applicant"
msgstr "sökande"

#: frontend/src/components/JobCard.vue:26 frontend/src/pages/JobDetail.vue:108
msgid "applicants"
msgstr "sökande"

#: frontend/src/components/VideoBlock.vue:15
msgid "at {0} minutes"
msgstr "om {0} minuter"

#: lms/templates/emails/payment_reminder.html:4
msgid "but didn’t complete your payment"
msgstr "men slutförde inte din betalning"

#: lms/templates/emails/mentor_request_creation_email.html:5
msgid "cancel your application"
msgstr "avbryt din ansökan"

#: frontend/src/pages/CertifiedParticipants.vue:79
msgid "certificate"
msgstr "certifikat"

#: frontend/src/pages/CertifiedParticipants.vue:78
msgid "certificates"
msgstr "certifikat"

#: frontend/src/pages/CertifiedParticipants.vue:18
msgid "certified members"
msgstr "certifierade medlemmar"

#: frontend/src/pages/Lesson.vue:98 frontend/src/pages/Lesson.vue:234
msgid "completed"
msgstr "klar"

#: lms/templates/quiz/quiz.js:137
msgid "correct answers"
msgstr "korrekta svar"

#: lms/templates/emails/mentor_request_status_update_email.html:4
msgid "has been"
msgstr "har varit"

#: frontend/src/components/StudentHeatmap.vue:8
msgid "in the last"
msgstr "i sista"

#: lms/templates/signup-form.html:12
msgid "<EMAIL>"
msgstr "användare@bolag"

#: frontend/src/pages/Programs.vue:31
msgid "member"
msgstr "medlem"

#: frontend/src/pages/Programs.vue:31
msgid "members"
msgstr "medlemmar"

#: frontend/src/components/Modals/LiveClassAttendance.vue:57
msgid "minutes"
msgstr "minuter"

#: lms/templates/quiz/quiz.html:106
msgid "of"
msgstr "av "

#: lms/templates/quiz/quiz.js:141
msgid "out of"
msgstr "av"

#: frontend/src/pages/QuizForm.vue:344
msgid "question_detail"
msgstr "fråga_detalj"

#: lms/templates/reviews.html:25
msgid "ratings"
msgstr "Bedömningar"

#: frontend/src/components/Settings/Categories.vue:19
msgid "saving..."
msgstr "sparar..."

#: lms/templates/reviews.html:43
msgid "stars"
msgstr "stjärnor"

#: frontend/src/components/BatchFeedback.vue:12
msgid "to view your feedback."
msgstr "för att se din återkoppling."

#: frontend/src/components/StudentHeatmap.vue:10
msgid "weeks"
msgstr "veckor"

#: lms/templates/emails/mentor_request_creation_email.html:5
msgid "you can"
msgstr "du kan"

#: frontend/src/pages/Assignments.vue:26
msgid "{0} Assignments"
msgstr "{0} Uppgifter"

#: frontend/src/pages/ProgrammingExercises/ProgrammingExercises.vue:39
msgid "{0} Exercises"
msgstr "{0} Övningar"

#: frontend/src/components/Modals/CourseProgressSummary.vue:14
msgid "{0} Members"
msgstr "{0} Medlemmar"

#: frontend/src/pages/Jobs.vue:32
msgid "{0} Open Jobs"
msgstr "{0} Lediga Jobb"

#: frontend/src/pages/Quizzes.vue:18
msgid "{0} Quizzes"
msgstr "{0} Frågesporter"

#: lms/lms/api.py:865 lms/lms/api.py:873
msgid "{0} Settings not found"
msgstr "{0} Inställningar hittades inte"

#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseSubmissions.vue:12
msgid "{0} Submissions"
msgstr "{0} Inlämningar"

#: lms/templates/emails/job_application.html:2
msgid "{0} has applied for the job position {1}"
msgstr "{0} har sökt tjänst {1}"

#: lms/templates/emails/job_report.html:4
msgid "{0} has reported a job post for the following reason."
msgstr "{0} har anmält en ledig tjänst av följande skäl."

#: lms/templates/emails/assignment_submission.html:2
msgid "{0} has submitted the assignment {1}"
msgstr "{0} har lämnat in uppgift {1}"

#: lms/lms/doctype/lms_enrollment/lms_enrollment.py:57
msgid "{0} is already a Student of {1} course through {2} batch"
msgstr "{0} är redan student på {1} kurs genom {2} grupp"

#: lms/lms/doctype/lms_course_mentor_mapping/lms_course_mentor_mapping.py:16
msgid "{0} is already a mentor for course {1}"
msgstr "{0} är redan mentor för kurs {1}"

#: lms/lms/doctype/lms_enrollment/lms_enrollment.py:30
msgid "{0} is already a {1} of the course {2}"
msgstr "{0} är redan {1} av kurs {2}"

#: lms/lms/doctype/lms_certificate/lms_certificate.py:91
msgid "{0} is already certified for the batch {1}"
msgstr "{0} är redan certifierad för grupp {1}"

#: lms/lms/doctype/lms_certificate/lms_certificate.py:72
msgid "{0} is already certified for the course {1}"
msgstr "{0} är redan certifierad för kurs {1}"

#: lms/lms/notification/certificate_request_reminder/certificate_request_reminder.html:5
msgid "{0} is your evaluator"
msgstr "{0} är din utvärderare"

#: lms/lms/utils.py:686
msgid "{0} mentioned you in a comment"
msgstr "{0} nämnde dig i en kommentar"

#: lms/templates/emails/mention_template.html:2
msgid "{0} mentioned you in a comment in your batch."
msgstr "{0} nämnde dig i en kommentar i din grupp."

#: lms/lms/utils.py:639 lms/lms/utils.py:645
msgid "{0} mentioned you in a comment in {1}"
msgstr "{0} hänvisade dig i kommentar i {1}"

#: lms/lms/utils.py:462
msgid "{0}k"
msgstr "{0}k"

#. Count format of shortcut in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "{} Active"
msgstr "{} Aktiva"

#. Count format of shortcut in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "{} Completed"
msgstr "{} Klara"

#. Count format of shortcut in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "{} Enrolled"
msgstr "{} Inskriven"

#. Count format of shortcut in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "{} Granted"
msgstr "{} Beviljad"

#. Count format of shortcut in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "{} Passed"
msgstr "{} Godkänd"

#. Count format of shortcut in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "{} Published"
msgstr "{} Publicerad"

