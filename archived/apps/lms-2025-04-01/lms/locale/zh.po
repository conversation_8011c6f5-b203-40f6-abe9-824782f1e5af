msgid ""
msgstr ""
"Project-Id-Version: frappe\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2025-07-04 16:04+0000\n"
"PO-Revision-Date: 2025-07-07 19:40\n"
"Last-Translator: <EMAIL>\n"
"Language-Team: Chinese Simplified\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.16.0\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"X-Crowdin-Project: frappe\n"
"X-Crowdin-Project-ID: 639578\n"
"X-Crowdin-Language: zh-CN\n"
"X-Crowdin-File: /[frappe.lms] develop/lms/locale/main.pot\n"
"X-Crowdin-File-ID: 90\n"
"Language: zh_CN\n"

#: lms/templates/emails/assignment_submission.html:5
msgid " Please evaluate and grade it."
msgstr "请进行评估并评分。"

#: frontend/src/pages/Programs.vue:39
#, python-format
msgid "% completed"
msgstr "完成百分比"

#. Paragraph text in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "<a href=\"/app/lms-settings/LMS%20Settings\">LMS Settings</a>"
msgstr "<a href=\"/app/lms-settings/LMS%20Settings\">LMS 设置</a>"

#. Paragraph text in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "<a href=\"/app/web-page/new-web-page-1\">Setup a Home Page</a>"
msgstr "<a href=\"/app/web-page/new-web-page-1\">设置主页</a>"

#. Paragraph text in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "<a href=\"/lms/courses\">Visit LMS Portal</a>"
msgstr "<a href=\"/lms/courses\">访问 LMS 门户</a>"

#. Paragraph text in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "<a href=\"/lms/courses/new/edit\">Create a Course</a>"
msgstr "<a href=\"/lms/courses/new/edit\">创建一个课程</a>"

#. Paragraph text in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "<a href=\"https://docs.frappe.io/learning\">Documentation</a>"
msgstr "<a href=\"https://docs.frappe.io/learning\">文档</a>"

#: frontend/src/components/Modals/EmailTemplateModal.vue:50
msgid "<p>Dear {{ member_name }},</p>\\n\\n<p>You have been enrolled in our upcoming batch {{ batch_name }}.</p>\\n\\n<p>Thanks,</p>\\n<p>Frappe Learning</p>"
msgstr ""

#. Header text in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "<span class=\"h4\"><b>Get Started</b></span>"
msgstr "<span class=\"h4\"><b>开始于</b></span>"

#. Header text in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "<span class=\"h4\"><b>Master</b></span>"
msgstr "<span class=\"h4\"><b>大师</b></span>"

#. Header text in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "<span style=\"font-size: 18px;\"><b>Statistics</b></span>"
msgstr "<span style=\"font-size: 18px;\"><b>统计数据</b></span>"

#: lms/lms/doctype/lms_course/lms_course.py:63
msgid "A course cannot have both paid certificate and certificate of completion."
msgstr "课程不可同时设置付费证书与结业证书。"

#: frontend/src/pages/CourseForm.vue:82
msgid "A one line introduction to the course that appears on the course card"
msgstr "课程卡片显示的课程简介（单行）"

#: frontend/src/pages/ProfileAbout.vue:4
msgid "About"
msgstr "关于"

#: frontend/src/pages/Batch.vue:101
msgid "About this batch"
msgstr "本批次说明"

#. Label of the verify_terms (Check) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Acceptance for Terms and/or Policies"
msgstr "条款及政策接受声明"

#. Option for the 'Status' (Select) field in DocType 'Cohort Join Request'
#: lms/lms/doctype/cohort_join_request/cohort_join_request.json
msgid "Accepted"
msgstr "已接受"

#: frontend/src/components/Settings/ZoomSettings.vue:174
msgid "Account"
msgstr "会计科目"

#. Label of the account_id (Data) field in DocType 'LMS Zoom Settings'
#. Label of the account_id (Data) field in DocType 'Zoom Settings'
#: frontend/src/components/Modals/ZoomAccountModal.vue:55
#: lms/lms/doctype/lms_zoom_settings/lms_zoom_settings.json
#: lms/lms/doctype/zoom_settings/zoom_settings.json
msgid "Account ID"
msgstr "账户ID"

#. Label of the account_name (Data) field in DocType 'LMS Zoom Settings'
#: frontend/src/components/Modals/ZoomAccountModal.vue:30
#: lms/lms/doctype/lms_zoom_settings/lms_zoom_settings.json
msgid "Account Name"
msgstr "账户名称"

#: frontend/src/pages/ProfileAbout.vue:17
msgid "Achievements"
msgstr "成就"

#. Option for the 'Status' (Select) field in DocType 'LMS Batch Old'
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
msgid "Active"
msgstr "激活"

#: frontend/src/pages/Statistics.vue:16
msgid "Active Members"
msgstr "活跃成员"

#: frontend/src/components/Assessments.vue:11
#: frontend/src/components/BatchCourses.vue:11
#: frontend/src/components/BatchStudents.vue:73
#: frontend/src/components/LiveClass.vue:21
#: frontend/src/components/Modals/QuizInVideo.vue:29
#: frontend/src/components/Settings/Categories.vue:43
#: frontend/src/components/Settings/Evaluators.vue:93
#: frontend/src/components/Settings/Members.vue:91
#: frontend/src/pages/ProgramForm.vue:30 frontend/src/pages/ProgramForm.vue:92
#: frontend/src/pages/ProgramForm.vue:137
msgid "Add"
msgstr "添加"

#: frontend/src/components/CourseOutline.vue:18
#: frontend/src/components/CreateOutline.vue:18
#: frontend/src/components/Modals/ChapterModal.vue:5
msgid "Add Chapter"
msgstr "添加章节"

#: frontend/src/components/Settings/Evaluators.vue:91
msgid "Add Evaluator"
msgstr ""

#: frontend/src/components/CourseOutline.vue:133
msgid "Add Lesson"
msgstr "添加课时"

#: frontend/src/components/VideoBlock.vue:121
msgid "Add Quiz to Video"
msgstr ""

#: frontend/src/components/Controls/ChildTable.vue:69
msgid "Add Row"
msgstr "添加行"

#: frontend/src/pages/ProfileEvaluator.vue:89
msgid "Add Slot"
msgstr "添加时段"

#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseForm.vue:35
msgid "Add Test Case"
msgstr ""

#: lms/templates/onboarding_header.html:26
msgid "Add a Chapter"
msgstr "新增章节"

#: lms/templates/onboarding_header.html:33
msgid "Add a Lesson"
msgstr "新增课时"

#: frontend/src/components/Modals/StudentModal.vue:5
msgid "Add a Student"
msgstr "添加学员"

#: frontend/src/components/AppSidebar.vue:568
msgid "Add a chapter"
msgstr "添加章节"

#: frontend/src/components/Modals/BatchCourseModal.vue:5
msgid "Add a course"
msgstr "创建课程"

#: frontend/src/pages/CourseForm.vue:67
msgid "Add a keyword and then press enter"
msgstr "输入关键词后按回车键添加"

#: frontend/src/components/AppSidebar.vue:569
msgid "Add a lesson"
msgstr "添加课时"

#: frontend/src/components/Settings/Members.vue:88
msgid "Add a new member"
msgstr ""

#: frontend/src/components/Modals/Question.vue:166
#: frontend/src/pages/QuizForm.vue:200
msgid "Add a new question"
msgstr "新增试题"

#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseModal.vue:5
msgid "Add a programming exercise to your lesson"
msgstr ""

#: frontend/src/components/AssessmentPlugin.vue:7
msgid "Add a quiz to your lesson"
msgstr "为本课时添加测验"

#: frontend/src/components/Modals/AssessmentModal.vue:5
msgid "Add an assessment"
msgstr "添加考核"

#: frontend/src/components/AssessmentPlugin.vue:8
msgid "Add an assignment to your lesson"
msgstr "为本课时添加作业"

#: lms/lms/doctype/lms_question/lms_question.py:66
msgid "Add at least one possible answer for this question: {0}"
msgstr "请为该问题添加至少一个备选答案：{0}"

#: frontend/src/components/AppSidebar.vue:532
msgid "Add courses to your batch"
msgstr "为班级添加课程"

#: frontend/src/components/Modals/QuizInVideo.vue:5
msgid "Add quiz to this video"
msgstr ""

#: frontend/src/components/AppSidebar.vue:511
msgid "Add students to your batch"
msgstr "为班级添加学员"

#: frontend/src/components/Modals/PageModal.vue:6
msgid "Add web page to sidebar"
msgstr "添加网页至侧边栏"

#: frontend/src/components/Assignment.vue:68
msgid "Add your assignment as {0}"
msgstr "以{0}格式添加作业"

#: frontend/src/components/AppSidebar.vue:444
msgid "Add your first chapter"
msgstr "添加首个章节"

#: frontend/src/components/AppSidebar.vue:460
msgid "Add your first lesson"
msgstr "添加首节课时"

#. Label of the address (Link) field in DocType 'LMS Payment'
#: frontend/src/pages/Billing.vue:64
#: lms/lms/doctype/lms_payment/lms_payment.json
msgid "Address"
msgstr "地址"

#: frontend/src/pages/Billing.vue:74
msgid "Address Line 1"
msgstr "地址行1"

#: frontend/src/pages/Billing.vue:78
msgid "Address Line 2"
msgstr "地址行2"

#. Option for the 'Role' (Select) field in DocType 'Cohort Staff'
#. Option for the 'Required Role' (Select) field in DocType 'Cohort Web Page'
#. Option for the 'Role' (Select) field in DocType 'LMS Enrollment'
#: lms/lms/doctype/cohort_staff/cohort_staff.json
#: lms/lms/doctype/cohort_web_page/cohort_web_page.json
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
msgid "Admin"
msgstr "管理员"

#. Name of a role
#: frontend/src/pages/Batches.vue:273 lms/lms/doctype/lms_badge/lms_badge.json
msgid "All"
msgstr "全部"

#: frontend/src/pages/Batches.vue:26
msgid "All Batches"
msgstr "所有批次"

#: frontend/src/pages/Courses.vue:26 lms/lms/widgets/BreadCrumb.html:3
msgid "All Courses"
msgstr "所有课程"

#: lms/templates/quiz/quiz.html:141
msgid "All Submissions"
msgstr "所有提交记录"

#: lms/lms/doctype/lms_quiz/lms_quiz.py:44
msgid "All questions should have the same marks if the limit is set."
msgstr "若设置分数限制，所有试题分值必须统一。"

#. Label of the allow_guest_access (Check) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Allow Guest Access"
msgstr "允许访客访问"

#. Label of the allow_posting (Check) field in DocType 'Job Settings'
#: lms/job/doctype/job_settings/job_settings.json
msgid "Allow Job Posting From Website"
msgstr "允许通过网站发布职位"

#. Label of the allow_self_enrollment (Check) field in DocType 'LMS Batch'
#: lms/lms/doctype/lms_batch/lms_batch.json
msgid "Allow Self Enrollment"
msgstr "允许自助注册"

#. Label of the allow_future (Check) field in DocType 'LMS Batch'
#: lms/lms/doctype/lms_batch/lms_batch.json
msgid "Allow accessing future dates"
msgstr "允许访问未来日期"

#: frontend/src/pages/BatchForm.vue:57
msgid "Allow self enrollment"
msgstr "允许自助注册"

#: lms/lms/user.py:34
msgid "Already Registered"
msgstr "已注册"

#. Label of the amount (Currency) field in DocType 'LMS Batch'
#. Label of the course_price (Currency) field in DocType 'LMS Course'
#. Label of the amount (Currency) field in DocType 'LMS Payment'
#: frontend/src/pages/BatchForm.vue:265 frontend/src/pages/CourseForm.vue:245
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_course/lms_course.json
#: lms/lms/doctype/lms_payment/lms_payment.json
msgid "Amount"
msgstr "金额"

#. Label of the amount_usd (Currency) field in DocType 'LMS Batch'
#. Label of the amount_usd (Currency) field in DocType 'LMS Course'
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_course/lms_course.json
msgid "Amount (USD)"
msgstr "金额（美元）"

#: lms/lms/doctype/lms_batch/lms_batch.py:70
msgid "Amount and currency are required for paid batches."
msgstr "付费批次必须填写金额与币种。"

#: lms/lms/doctype/lms_course/lms_course.py:74
msgid "Amount and currency are required for paid certificates."
msgstr "付费证书必须填写金额与币种。"

#: lms/lms/doctype/lms_course/lms_course.py:71
msgid "Amount and currency are required for paid courses."
msgstr "付费课程必须填写金额与币种。"

#. Label of the amount_with_gst (Currency) field in DocType 'LMS Payment'
#: lms/lms/doctype/lms_payment/lms_payment.json
msgid "Amount with GST"
msgstr "含商品及服务税金额"

#: frontend/src/components/Modals/AnnouncementModal.vue:33
msgid "Announcement"
msgstr "公告"

#: frontend/src/components/Modals/AnnouncementModal.vue:101
msgid "Announcement has been sent successfully"
msgstr "公告发送成功"

#: frontend/src/components/Modals/AnnouncementModal.vue:96
msgid "Announcement is required"
msgstr "必须填写公告内容。"

#. Label of the answer (Text Editor) field in DocType 'LMS Assignment'
#. Label of the answer (Text Editor) field in DocType 'LMS Assignment
#. Submission'
#. Label of the answer (Code) field in DocType 'LMS Exercise'
#: frontend/src/pages/QuizSubmission.vue:60
#: lms/lms/doctype/lms_assignment/lms_assignment.json
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
#: lms/lms/doctype/lms_exercise/lms_exercise.json
msgid "Answer"
msgstr "答案"

#: frontend/src/pages/CourseForm.vue:112 frontend/src/pages/CourseForm.vue:131
msgid "Appears on the course card in the course list"
msgstr "显示在课程列表的课程卡片上"

#: frontend/src/pages/BatchForm.vue:240
msgid "Appears when the batch URL is shared on any online platform"
msgstr "在分享批次链接至网络平台时显示"

#: frontend/src/pages/BatchForm.vue:221
msgid "Appears when the batch URL is shared on socials"
msgstr "当班级URL在社交平台分享时显示"

#: frontend/src/pages/JobDetail.vue:51
msgid "Apply"
msgstr "应用"

#. Label of the apply_gst (Check) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Apply GST for India"
msgstr "应用印度商品及服务税"

#. Label of the apply_rounding (Check) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Apply Rounding on Equivalent"
msgstr "应用等价舍入"

#: frontend/src/components/Modals/JobApplicationModal.vue:6
msgid "Apply for this job"
msgstr "申请该职位"

#. Option for the 'Status' (Select) field in DocType 'Invite Request'
#. Option for the 'Status' (Select) field in DocType 'LMS Course'
#. Option for the 'Status' (Select) field in DocType 'LMS Mentor Request'
#: lms/lms/doctype/invite_request/invite_request.json
#: lms/lms/doctype/lms_course/lms_course.json
#: lms/lms/doctype/lms_mentor_request/lms_mentor_request.json
msgid "Approved"
msgstr "已批准"

#: frontend/src/components/Apps.vue:13
msgid "Apps"
msgstr "应用"

#: frontend/src/pages/Batches.vue:283
msgid "Archived"
msgstr "已归档"

#: frontend/src/components/UpcomingEvaluations.vue:172
msgid "Are you sure you want to cancel this evaluation? This action cannot be undone."
msgstr "确认取消本次评估？此操作不可撤销。"

#: frontend/src/components/UserDropdown.vue:175
msgid "Are you sure you want to login to your Frappe Cloud dashboard?"
msgstr "确定要登录Frappe Cloud控制面板？"

#. Label of the assessment_tab (Tab Break) field in DocType 'LMS Batch'
#. Label of the assessment (Table) field in DocType 'LMS Batch'
#: frontend/src/components/Modals/AssessmentModal.vue:27
#: frontend/src/components/Modals/BatchStudentProgress.vue:41
#: lms/lms/doctype/lms_batch/lms_batch.json lms/templates/assessments.html:11
msgid "Assessment"
msgstr "考核"

#. Label of the assessment_name (Dynamic Link) field in DocType 'LMS
#. Assessment'
#: lms/lms/doctype/lms_assessment/lms_assessment.json
msgid "Assessment Name"
msgstr "考核名称"

#. Label of the assessment_type (Link) field in DocType 'LMS Assessment'
#: lms/lms/doctype/lms_assessment/lms_assessment.json
msgid "Assessment Type"
msgstr "考核类型"

#: frontend/src/components/Modals/AssessmentModal.vue:91
msgid "Assessment added successfully"
msgstr "考核添加成功"

#: lms/lms/doctype/lms_batch/lms_batch.py:80
msgid "Assessment {0} has already been added to this batch."
msgstr "考核{0}已添加至本批次。"

#. Label of the show_assessments (Check) field in DocType 'LMS Settings'
#: frontend/src/components/AppSidebar.vue:581
#: frontend/src/components/Assessments.vue:5
#: frontend/src/components/BatchStudents.vue:32
#: lms/lms/doctype/lms_settings/lms_settings.json
#: lms/templates/assessments.html:3
msgid "Assessments"
msgstr "考核"

#: lms/lms/doctype/lms_badge/lms_badge.js:50
msgid "Assign"
msgstr "分配"

#. Label of the section_break_16 (Section Break) field in DocType 'Course
#. Lesson'
#. Label of the assignment (Link) field in DocType 'LMS Assignment Submission'
#: frontend/src/components/Assessments.vue:245
#: frontend/src/pages/AssignmentSubmissionList.vue:12
#: frontend/src/utils/assignment.js:24
#: lms/lms/doctype/course_lesson/course_lesson.json
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
#: lms/templates/assignment.html:3
msgid "Assignment"
msgstr "作业"

#. Label of the assignment_attachment (Attach) field in DocType 'LMS Assignment
#. Submission'
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
msgid "Assignment Attachment"
msgstr "作业附件"

#: frontend/src/pages/AssignmentSubmissionList.vue:222
msgid "Assignment Submissions"
msgstr "作业提交记录"

#. Label of the assignment_title (Data) field in DocType 'LMS Assignment
#. Submission'
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
msgid "Assignment Title"
msgstr "作业标题"

#: frontend/src/components/Modals/AssignmentForm.vue:125
msgid "Assignment created successfully"
msgstr "作业创建成功"

#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.py:24
msgid "Assignment for Lesson {0} by {1} already exists."
msgstr "学员{1}的课时{0}作业已存在。"

#: frontend/src/components/Assignment.vue:356
msgid "Assignment submitted successfully"
msgstr "作业提交成功。"

#: frontend/src/components/Modals/AssignmentForm.vue:138
msgid "Assignment updated successfully"
msgstr "作业更新成功"

#. Description of the 'Question' (Small Text) field in DocType 'Course Lesson'
#: lms/lms/doctype/course_lesson/course_lesson.json
msgid "Assignment will appear at the bottom of the lesson."
msgstr "作业将显示在课时末尾。"

#: frontend/src/components/AppSidebar.vue:585
#: frontend/src/pages/Assignments.vue:208 lms/www/lms.py:273
msgid "Assignments"
msgstr "作业"

#: lms/lms/doctype/lms_question/lms_question.py:43
msgid "At least one option must be correct for this question."
msgstr "本题至少需设置一个正确选项。"

#: lms/lms/doctype/lms_programming_exercise/lms_programming_exercise.py:15
msgid "At least one test case is required for the programming exercise."
msgstr ""

#: frontend/src/components/Modals/LiveClassAttendance.vue:5
msgid "Attendance for Class - {0}"
msgstr ""

#: frontend/src/components/Modals/LiveClassAttendance.vue:24
msgid "Attended for"
msgstr ""

#. Label of the attendees (Int) field in DocType 'LMS Live Class'
#: lms/lms/doctype/lms_live_class/lms_live_class.json
msgid "Attendees"
msgstr "参会人员"

#. Label of the attire (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Attire Preference"
msgstr "着装偏好"

#: frontend/src/pages/ProfileEvaluator.vue:137
msgid "Authorize Google Calendar Access"
msgstr "授权Google日历访问"

#. Option for the 'Event' (Select) field in DocType 'LMS Badge'
#: lms/lms/doctype/lms_badge/lms_badge.json
msgid "Auto Assign"
msgstr "自动分配"

#. Label of the auto_recording (Select) field in DocType 'LMS Live Class'
#: frontend/src/components/Modals/LiveClassModal.vue:73
#: lms/lms/doctype/lms_live_class/lms_live_class.json
msgid "Auto Recording"
msgstr "自动录制"

#: frontend/src/pages/ProfileEvaluator.vue:224
msgid "Availability updated successfully"
msgstr "可用性更新成功。"

#: frontend/src/components/BatchFeedback.vue:43
msgid "Average Feedback Received"
msgstr ""

#: frontend/src/components/Modals/CourseProgressSummary.vue:96
msgid "Average Progress %"
msgstr ""

#: frontend/src/components/CourseCard.vue:55
#: frontend/src/pages/CourseDetail.vue:20
msgid "Average Rating"
msgstr "平均评分"

#: frontend/src/components/Modals/VideoStatistics.vue:65
msgid "Average Watch Time (seconds)"
msgstr ""

#: frontend/src/pages/Lesson.vue:151
msgid "Back to Course"
msgstr "返回课程"

#. Label of the badge (Link) field in DocType 'LMS Badge Assignment'
#: lms/lms/doctype/lms_badge_assignment/lms_badge_assignment.json
msgid "Badge"
msgstr "徽章"

#. Label of the badge_description (Small Text) field in DocType 'LMS Badge
#. Assignment'
#: lms/lms/doctype/lms_badge_assignment/lms_badge_assignment.json
msgid "Badge Description"
msgstr "徽章描述"

#. Label of the badge_image (Attach) field in DocType 'LMS Badge Assignment'
#: lms/lms/doctype/lms_badge_assignment/lms_badge_assignment.json
msgid "Badge Image"
msgstr "徽章图片"

#. Label of the batch (Link) field in DocType 'LMS Batch Enrollment'
#. Label of the batch (Link) field in DocType 'LMS Batch Feedback'
#. Label of the batch_name (Link) field in DocType 'LMS Certificate'
#. Label of the batch_name (Link) field in DocType 'LMS Certificate Request'
#. Label of the batch_name (Link) field in DocType 'LMS Live Class'
#: frontend/src/components/Modals/Event.vue:32
#: lms/lms/doctype/lms_batch_enrollment/lms_batch_enrollment.json
#: lms/lms/doctype/lms_batch_feedback/lms_batch_feedback.json
#: lms/lms/doctype/lms_certificate/lms_certificate.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
#: lms/lms/doctype/lms_live_class/lms_live_class.json
msgid "Batch"
msgstr "批次"

#. Label of the batch_confirmation_template (Link) field in DocType 'LMS
#. Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Batch Confirmation Template"
msgstr "批次确认模板"

#. Name of a DocType
#: lms/lms/doctype/batch_course/batch_course.json
msgid "Batch Course"
msgstr "批次课程"

#. Label of the section_break_5 (Section Break) field in DocType 'LMS Batch
#. Old'
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
msgid "Batch Description"
msgstr "批次说明"

#. Label of the batch_details (Text Editor) field in DocType 'LMS Batch'
#: frontend/src/pages/BatchForm.vue:126
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/templates/emails/batch_confirmation.html:26
msgid "Batch Details"
msgstr "批次详情"

#. Label of the batch_details_raw (HTML Editor) field in DocType 'LMS Batch'
#: lms/lms/doctype/lms_batch/lms_batch.json
msgid "Batch Details Raw"
msgstr "批次原始数据"

#: frontend/src/components/Modals/EmailTemplateModal.vue:28
msgid "Batch Enrollment Confirmation"
msgstr ""

#. Name of a role
#: lms/lms/doctype/course_evaluator/course_evaluator.json
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_category/lms_category.json
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
#: lms/lms/doctype/lms_programming_exercise/lms_programming_exercise.json
#: lms/lms/doctype/lms_programming_exercise_submission/lms_programming_exercise_submission.json
#: lms/lms/doctype/lms_zoom_settings/lms_zoom_settings.json
msgid "Batch Evaluator"
msgstr "批次评估人"

#. Label of the batch_name (Link) field in DocType 'LMS Certificate Evaluation'
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
msgid "Batch Name"
msgstr "批次名称"

#. Label of the batch_old (Link) field in DocType 'Exercise Latest Submission'
#. Label of the batch_old (Link) field in DocType 'Exercise Submission'
#. Label of the batch_old (Link) field in DocType 'LMS Enrollment'
#: lms/lms/doctype/exercise_latest_submission/exercise_latest_submission.json
#: lms/lms/doctype/exercise_submission/exercise_submission.json
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
msgid "Batch Old"
msgstr "旧批次"

#. Label of the section_break_7 (Section Break) field in DocType 'LMS Batch
#. Old'
#. Label of the section_break_szgq (Section Break) field in DocType 'LMS
#. Settings'
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Batch Settings"
msgstr "批次设置"

#: lms/templates/emails/batch_confirmation.html:11
msgid "Batch Start Date:"
msgstr "批次开始日期："

#: frontend/src/components/BatchStudents.vue:40
msgid "Batch Summary"
msgstr ""

#. Label of the batch_title (Data) field in DocType 'LMS Certificate'
#. Label of the batch_title (Data) field in DocType 'LMS Certificate Request'
#: lms/lms/doctype/lms_certificate/lms_certificate.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
msgid "Batch Title"
msgstr "批次标题"

#: lms/lms/doctype/lms_batch/lms_batch.py:41
msgid "Batch end date cannot be before the batch start date"
msgstr "批次结束日期不可早于开始日期"

#: lms/lms/api.py:245
msgid "Batch has already started."
msgstr "班级已开始。"

#: lms/lms/api.py:240
msgid "Batch is sold out."
msgstr "该批次已满额。"

#: lms/lms/doctype/lms_batch/lms_batch.py:46
msgid "Batch start time cannot be greater than or equal to end time."
msgstr "班级开始时间不可大于或等于结束时间。"

#: lms/templates/emails/batch_start_reminder.html:10
msgid "Batch:"
msgstr "批次："

#. Label of the batches (Check) field in DocType 'LMS Settings'
#: frontend/src/pages/Batches.vue:299 frontend/src/pages/Batches.vue:306
#: lms/lms/doctype/lms_settings/lms_settings.json lms/www/lms.py:122
msgid "Batches"
msgstr "批次"

#. Label of the begin_date (Date) field in DocType 'Cohort'
#: lms/lms/doctype/cohort/cohort.json
msgid "Begin Date"
msgstr "起始日期"

#: lms/templates/emails/batch_confirmation.html:33
#: lms/templates/emails/batch_start_reminder.html:31
#: lms/templates/emails/certification.html:20
#: lms/templates/emails/live_class_reminder.html:28
msgid "Best Regards"
msgstr "此致敬礼"

#. Label of the billing_details_section (Section Break) field in DocType 'LMS
#. Payment'
#: frontend/src/pages/Billing.vue:8 frontend/src/pages/Billing.vue:357
#: lms/lms/doctype/lms_payment/lms_payment.json
msgid "Billing Details"
msgstr "账单明细"

#. Label of the billing_name (Data) field in DocType 'LMS Payment'
#: frontend/src/pages/Billing.vue:70
#: lms/lms/doctype/lms_payment/lms_payment.json
msgid "Billing Name"
msgstr "账单名称"

#: frontend/src/components/Modals/EditProfile.vue:75
msgid "Bio"
msgstr "个人简介"

#. Label of the body (Markdown Editor) field in DocType 'Course Lesson'
#: lms/lms/doctype/course_lesson/course_lesson.json
msgid "Body"
msgstr "正文"

#. Option for the 'Collaboration Preference' (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Both Individual and Team Work"
msgstr "个人与团队协作"

#. Label of the branch (Data) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Branch"
msgstr "分支机构"

#. Option for the 'User Category' (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json lms/templates/signup-form.html:23
msgid "Business Owner"
msgstr "业务负责人"

#: frontend/src/components/CourseCardOverlay.vue:54
msgid "Buy this course"
msgstr "购买本课程"

#: lms/templates/emails/lms_message.html:11
msgid "By"
msgstr "由"

#. Option for the 'Grade Type' (Select) field in DocType 'Education Detail'
#: lms/lms/doctype/education_detail/education_detail.json
msgid "CGPA/4"
msgstr "累计平均绩点/4分制"

#: frontend/src/components/UpcomingEvaluations.vue:57
#: frontend/src/components/UpcomingEvaluations.vue:177
msgid "Cancel"
msgstr "取消"

#: frontend/src/components/UpcomingEvaluations.vue:171
msgid "Cancel this evaluation?"
msgstr "取消本次评估？"

#. Option for the 'Status' (Select) field in DocType 'Cohort'
#. Option for the 'Stage' (Select) field in DocType 'LMS Batch Old'
#. Option for the 'Status' (Select) field in DocType 'LMS Certificate Request'
#: lms/lms/doctype/cohort/cohort.json
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
msgid "Cancelled"
msgstr "已取消"

#. Label of the carrer_preference_details (Section Break) field in DocType
#. 'User'
#: lms/fixtures/custom_field.json
msgid "Career Preference Details"
msgstr "职业偏好详情"

#. Option for the 'Attire Preference' (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Casual Wear"
msgstr "便装"

#. Label of the category (Link) field in DocType 'LMS Batch'
#. Label of the category (Data) field in DocType 'LMS Category'
#. Label of the category (Link) field in DocType 'LMS Course'
#: frontend/src/pages/BatchForm.vue:192 frontend/src/pages/Batches.vue:55
#: frontend/src/pages/CertifiedParticipants.vue:35
#: frontend/src/pages/CourseForm.vue:36 frontend/src/pages/Courses.vue:51
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_category/lms_category.json
#: lms/lms/doctype/lms_course/lms_course.json lms/templates/signup-form.html:22
msgid "Category"
msgstr "类别"

#: frontend/src/components/Settings/Categories.vue:39
msgid "Category Name"
msgstr "类别名称"

#: frontend/src/components/Settings/Categories.vue:133
msgid "Category added successfully"
msgstr ""

#: frontend/src/components/Settings/Categories.vue:193
msgid "Category deleted successfully"
msgstr ""

#: frontend/src/components/Settings/Categories.vue:173
msgid "Category updated successfully"
msgstr ""

#. Label of the certificate (Link) field in DocType 'LMS Enrollment'
#. Label of a shortcut in the LMS Workspace
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
#: lms/lms/workspace/lms/lms.json
msgid "Certificate"
msgstr "证书"

#. Label of the certification_template (Link) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Certificate Email Template"
msgstr "证书邮件模板"

#: lms/templates/emails/certification.html:13
msgid "Certificate Link"
msgstr "证书链接"

#: frontend/src/components/CourseCardOverlay.vue:156
msgid "Certificate of Completion"
msgstr "结业证书"

#: frontend/src/components/Modals/Event.vue:317
msgid "Certificate saved successfully"
msgstr "证书保存成功"

#: frontend/src/pages/ProfileCertificates.vue:4
msgid "Certificates"
msgstr "证书"

#: frontend/src/components/Modals/BulkCertificates.vue:120
msgid "Certificates generated successfully"
msgstr "证书生成成功"

#. Label of the certification (Table) field in DocType 'User'
#. Name of a DocType
#. Label of the certification (Check) field in DocType 'LMS Batch'
#. Label of the certification_section (Section Break) field in DocType 'LMS
#. Enrollment'
#. Label of a Card Break in the LMS Workspace
#. Label of a Link in the LMS Workspace
#: frontend/src/components/AppSidebar.vue:589
#: frontend/src/components/CourseCard.vue:115
#: frontend/src/components/Modals/Event.vue:381
#: frontend/src/pages/BatchForm.vue:62 frontend/src/pages/Batches.vue:38
#: frontend/src/pages/CourseCertification.vue:10
#: frontend/src/pages/CourseCertification.vue:135
#: frontend/src/pages/Courses.vue:34 lms/fixtures/custom_field.json
#: lms/lms/doctype/certification/certification.json
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
#: lms/lms/workspace/lms/lms.json
msgid "Certification"
msgstr "认证"

#. Label of the certification_details (Section Break) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Certification Details"
msgstr "认证详情"

#. Label of the certification_name (Data) field in DocType 'Certification'
#: lms/lms/doctype/certification/certification.json
msgid "Certification Name"
msgstr "认证名称"

#: frontend/src/components/BatchStudents.vue:17
msgid "Certified"
msgstr "已认证"

#. Label of the certified_members (Check) field in DocType 'LMS Settings'
#: frontend/src/pages/CertifiedParticipants.vue:182
#: frontend/src/pages/CertifiedParticipants.vue:189
#: frontend/src/pages/Statistics.vue:40
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Certified Members"
msgstr "认证成员"

#. Label of the certified_participants (Check) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json lms/www/lms.py:302
msgid "Certified Participants"
msgstr "认证参与者"

#: lms/templates/assignment.html:13
msgid "Change"
msgstr "变更"

#: frontend/src/components/Assignment.vue:342
msgid "Changes saved successfully"
msgstr "变更保存成功"

#. Label of the chapter (Link) field in DocType 'Chapter Reference'
#. Label of the chapter (Link) field in DocType 'LMS Course Progress'
#. Label of the chapter (Link) field in DocType 'LMS Video Watch Duration'
#. Label of a Link in the LMS Workspace
#: lms/lms/doctype/chapter_reference/chapter_reference.json
#: lms/lms/doctype/lms_course_progress/lms_course_progress.json
#: lms/lms/doctype/lms_video_watch_duration/lms_video_watch_duration.json
#: lms/lms/workspace/lms/lms.json
msgid "Chapter"
msgstr "章节"

#. Name of a DocType
#: lms/lms/doctype/chapter_reference/chapter_reference.json
msgid "Chapter Reference"
msgstr "章节引用"

#: frontend/src/components/Modals/ChapterModal.vue:154
msgid "Chapter added successfully"
msgstr "章节添加成功"

#: frontend/src/components/CourseOutline.vue:299
msgid "Chapter deleted successfully"
msgstr "章节删除成功。"

#: frontend/src/components/Modals/ChapterModal.vue:196
msgid "Chapter updated successfully"
msgstr "章节更新成功"

#. Label of the chapters (Table) field in DocType 'LMS Course'
#: lms/lms/doctype/lms_course/lms_course.json
msgid "Chapters"
msgstr "章节"

#: frontend/src/components/Quiz.vue:229 lms/templates/quiz/quiz.html:120
msgid "Check"
msgstr "检查"

#: frontend/src/pages/ProgrammingExercises/ProgrammingExercises.vue:16
msgid "Check All Submissions"
msgstr ""

#: lms/templates/emails/mention_template.html:10
msgid "Check Discussion"
msgstr "查看讨论"

#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseForm.vue:97
msgid "Check Submission"
msgstr ""

#: frontend/src/components/Modals/AssignmentForm.vue:55
#: frontend/src/pages/QuizForm.vue:39
msgid "Check Submissions"
msgstr "查看提交记录"

#: lms/templates/certificates_section.html:24
msgid "Check out the {0} to know more about certification."
msgstr "查看{0}了解更多认证信息。"

#: frontend/src/components/NoPermission.vue:19
msgid "Checkout Courses"
msgstr "选课中心"

#. Option for the 'Type' (Select) field in DocType 'LMS Question'
#. Option for the 'Type' (Select) field in DocType 'LMS Quiz Question'
#: lms/lms/doctype/lms_question/lms_question.json
#: lms/lms/doctype/lms_quiz_question/lms_quiz_question.json
msgid "Choices"
msgstr "选项"

#: frontend/src/components/Quiz.vue:644 lms/templates/quiz/quiz.html:53
msgid "Choose all answers that apply"
msgstr "选择所有适用答案"

#: frontend/src/components/Modals/Question.vue:19
msgid "Choose an existing question"
msgstr "选择现有题目"

#: frontend/src/components/Controls/IconPicker.vue:27
msgid "Choose an icon"
msgstr "选择图标"

#: frontend/src/components/Quiz.vue:645 lms/templates/quiz/quiz.html:53
msgid "Choose one answer"
msgstr "选择唯一答案"

#. Label of the city (Data) field in DocType 'User'
#. Label of the location (Data) field in DocType 'Job Opportunity'
#: frontend/src/pages/Billing.vue:81 frontend/src/pages/JobForm.vue:34
#: lms/fixtures/custom_field.json
#: lms/job/doctype/job_opportunity/job_opportunity.json
msgid "City"
msgstr "城市"

#: lms/templates/emails/live_class_reminder.html:10
msgid "Class:"
msgstr "班级："

#: frontend/src/components/Controls/Link.vue:50
msgid "Clear"
msgstr "清除"

#. Option for the 'Role Preference' (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Clearly Defined Role"
msgstr "明确界定的角色"

#: frontend/src/components/BatchFeedback.vue:10
msgid "Click here"
msgstr "点击此处"

#. Label of the client_id (Data) field in DocType 'LMS Zoom Settings'
#. Label of the client_id (Data) field in DocType 'Zoom Settings'
#: frontend/src/components/Modals/ZoomAccountModal.vue:36
#: lms/lms/doctype/lms_zoom_settings/lms_zoom_settings.json
#: lms/lms/doctype/zoom_settings/zoom_settings.json
msgid "Client ID"
msgstr "客户端ID"

#. Label of the client_secret (Password) field in DocType 'LMS Zoom Settings'
#. Label of the client_secret (Password) field in DocType 'Zoom Settings'
#: frontend/src/components/Modals/ZoomAccountModal.vue:49
#: lms/lms/doctype/lms_zoom_settings/lms_zoom_settings.json
#: lms/lms/doctype/zoom_settings/zoom_settings.json
msgid "Client Secret"
msgstr "客户端密钥"

#: frontend/src/components/Settings/Categories.vue:27
msgid "Close"
msgstr "关闭"

#. Option for the 'Status' (Select) field in DocType 'Job Opportunity'
#. Option for the 'Membership' (Select) field in DocType 'LMS Batch Old'
#: lms/job/doctype/job_opportunity/job_opportunity.json
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
msgid "Closed"
msgstr "已关闭"

#. Option for the 'Auto Recording' (Select) field in DocType 'LMS Live Class'
#: lms/lms/doctype/lms_live_class/lms_live_class.json
msgid "Cloud"
msgstr "云"

#. Label of the code (Code) field in DocType 'LMS Exercise'
#. Label of the code (Code) field in DocType 'LMS Programming Exercise
#. Submission'
#: lms/lms/doctype/lms_exercise/lms_exercise.json
#: lms/lms/doctype/lms_programming_exercise_submission/lms_programming_exercise_submission.json
msgid "Code"
msgstr "代码"

#. Name of a DocType
#. Label of the cohort (Link) field in DocType 'Cohort Join Request'
#. Label of the cohort (Link) field in DocType 'Cohort Mentor'
#. Label of the cohort (Link) field in DocType 'Cohort Staff'
#. Label of the cohort (Link) field in DocType 'Cohort Subgroup'
#. Option for the 'Scope' (Select) field in DocType 'Cohort Web Page'
#. Label of the cohort (Link) field in DocType 'LMS Enrollment'
#: lms/lms/doctype/cohort/cohort.json
#: lms/lms/doctype/cohort_join_request/cohort_join_request.json
#: lms/lms/doctype/cohort_mentor/cohort_mentor.json
#: lms/lms/doctype/cohort_staff/cohort_staff.json
#: lms/lms/doctype/cohort_subgroup/cohort_subgroup.json
#: lms/lms/doctype/cohort_web_page/cohort_web_page.json
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
msgid "Cohort"
msgstr "学习群组"

#. Name of a DocType
#: lms/lms/doctype/cohort_join_request/cohort_join_request.json
msgid "Cohort Join Request"
msgstr "加入群组请求"

#. Name of a DocType
#: lms/lms/doctype/cohort_mentor/cohort_mentor.json
msgid "Cohort Mentor"
msgstr "群组导师"

#. Name of a DocType
#: lms/lms/doctype/cohort_staff/cohort_staff.json
msgid "Cohort Staff"
msgstr "群组工作人员"

#. Name of a DocType
#: lms/lms/doctype/cohort_subgroup/cohort_subgroup.json
msgid "Cohort Subgroup"
msgstr "群组子分组"

#. Name of a DocType
#: lms/lms/doctype/cohort_web_page/cohort_web_page.json
msgid "Cohort Web Page"
msgstr "群组网页"

#. Label of the collaboration (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Collaboration Preference"
msgstr "协作偏好"

#: frontend/src/components/AppSidebar.vue:142
msgid "Collapse"
msgstr "折叠"

#. Label of the college (Data) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "College Name"
msgstr "院校名称"

#. Label of the color (Color) field in DocType 'LMS Timetable Legend'
#: lms/lms/doctype/lms_timetable_legend/lms_timetable_legend.json
msgid "Color"
msgstr "颜色"

#: frontend/src/pages/BatchForm.vue:293 frontend/src/pages/CourseForm.vue:283
msgid "Comma separated keywords for SEO"
msgstr ""

#. Label of the comments (Small Text) field in DocType 'Exercise Latest
#. Submission'
#. Label of the comments (Small Text) field in DocType 'Exercise Submission'
#. Label of the comments (Text Editor) field in DocType 'LMS Assignment
#. Submission'
#. Label of the comments (Small Text) field in DocType 'LMS Mentor Request'
#: frontend/src/components/Assignment.vue:164
#: lms/lms/doctype/exercise_latest_submission/exercise_latest_submission.json
#: lms/lms/doctype/exercise_submission/exercise_submission.json
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
#: lms/lms/doctype/lms_mentor_request/lms_mentor_request.json
msgid "Comments"
msgstr "评语"

#: frontend/src/components/Assignment.vue:142
msgid "Comments by Evaluator"
msgstr "评估人评语"

#. Description of the 'Meta Keywords' (Small Text) field in DocType 'LMS
#. Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Common keywords that will be used for all pages"
msgstr "全站通用关键词"

#. Label of the company (Data) field in DocType 'LMS Job Application'
#. Label of the company (Data) field in DocType 'Work Experience'
#: lms/job/doctype/lms_job_application/lms_job_application.json
#: lms/lms/doctype/work_experience/work_experience.json
msgid "Company"
msgstr "公司"

#. Label of the section_break_6 (Section Break) field in DocType 'Job
#. Opportunity'
#: frontend/src/pages/JobForm.vue:56
#: lms/job/doctype/job_opportunity/job_opportunity.json
msgid "Company Details"
msgstr "公司详情"

#. Label of the company_email_address (Data) field in DocType 'Job Opportunity'
#: frontend/src/pages/JobForm.vue:75
#: lms/job/doctype/job_opportunity/job_opportunity.json
msgid "Company Email Address"
msgstr "公司邮箱"

#. Label of the company_logo (Attach Image) field in DocType 'Job Opportunity'
#: frontend/src/pages/JobForm.vue:80
#: lms/job/doctype/job_opportunity/job_opportunity.json
msgid "Company Logo"
msgstr "公司标志"

#. Label of the company_name (Data) field in DocType 'Job Opportunity'
#: frontend/src/pages/JobForm.vue:62
#: lms/job/doctype/job_opportunity/job_opportunity.json
msgid "Company Name"
msgstr "公司名称"

#. Label of the company_type (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Company Type"
msgstr "公司类型"

#. Label of the company_website (Data) field in DocType 'Job Opportunity'
#: frontend/src/pages/JobForm.vue:68
#: lms/job/doctype/job_opportunity/job_opportunity.json
msgid "Company Website"
msgstr "公司网站"

#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseSubmission.vue:69
msgid "Compiler Message"
msgstr ""

#. Option for the 'Status' (Select) field in DocType 'LMS Course Progress'
#: frontend/src/components/Modals/BatchStudentProgress.vue:24
#: lms/lms/doctype/lms_course_progress/lms_course_progress.json
#: lms/lms/widgets/CourseCard.html:75 lms/templates/reviews.html:48
msgid "Complete"
msgstr "完成"

#: lms/templates/emails/lms_invite_request_approved.html:7
msgid "Complete Sign Up"
msgstr "完成注册"

#: lms/templates/emails/payment_reminder.html:15
msgid "Complete Your Enrollment"
msgstr "完成注册"

#: lms/lms/doctype/lms_payment/lms_payment.py:73
msgid "Complete Your Enrollment - Don't miss out!"
msgstr "完成注册——切勿错过！"

#: frontend/src/components/VideoBlock.vue:144
msgid "Complete the upcoming quiz to continue watching the video. The quiz will open in {0} {1}."
msgstr ""

#. Option for the 'Status' (Select) field in DocType 'Cohort'
#. Option for the 'Stage' (Select) field in DocType 'LMS Batch Old'
#. Option for the 'Status' (Select) field in DocType 'LMS Certificate Request'
#: lms/lms/doctype/cohort/cohort.json
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
#: lms/lms/widgets/CourseCard.html:78
msgid "Completed"
msgstr "已完成"

#. Label of the enable_certification (Check) field in DocType 'LMS Course'
#: frontend/src/pages/CourseForm.vue:232
#: lms/lms/doctype/lms_course/lms_course.json
msgid "Completion Certificate"
msgstr "结业证书"

#. Label of the condition (Code) field in DocType 'LMS Badge'
#: lms/lms/doctype/lms_badge/lms_badge.json
msgid "Condition"
msgstr "条件"

#: lms/lms/doctype/lms_badge/lms_badge.py:16
msgid "Condition must be in valid JSON format."
msgstr "条件必须符合有效的JSON格式"

#: lms/lms/doctype/lms_badge/lms_badge.py:21
msgid "Condition must be valid python code."
msgstr "条件必须为有效的Python代码"

#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.js:7
msgid "Conduct Evaluation"
msgstr "执行评估"

#: frontend/src/pages/BatchForm.vue:141
msgid "Configurations"
msgstr "系统配置"

#: frontend/src/components/UserDropdown.vue:180
msgid "Confirm"
msgstr "确认"

#. Label of the confirmation_email_sent (Check) field in DocType 'LMS Batch
#. Enrollment'
#: lms/lms/doctype/lms_batch_enrollment/lms_batch_enrollment.json
msgid "Confirmation Email Sent"
msgstr "确认邮件已发送"

#. Label of the confirmation_email_template (Link) field in DocType 'LMS Batch'
#: lms/lms/doctype/lms_batch/lms_batch.json
msgid "Confirmation Email Template"
msgstr "确认邮件模板"

#: lms/lms/doctype/lms_certificate/lms_certificate.py:29
msgid "Congratulations on getting certified!"
msgstr "祝贺您获得认证！"

#: frontend/src/components/CourseCardOverlay.vue:63
#: frontend/src/pages/Lesson.vue:53
msgid "Contact the Administrator to enroll for this course."
msgstr "请联系管理员报名本课程"

#. Label of the content (Text) field in DocType 'Course Lesson'
#. Label of the content (Rating) field in DocType 'LMS Batch Feedback'
#: frontend/src/components/Modals/EmailTemplateModal.vue:44
#: frontend/src/components/Modals/EmailTemplateModal.vue:57
#: frontend/src/pages/LessonForm.vue:62
#: lms/lms/doctype/course_lesson/course_lesson.json
#: lms/lms/doctype/lms_batch_feedback/lms_batch_feedback.json
msgid "Content"
msgstr "内容"

#: frontend/src/components/CourseCardOverlay.vue:33
msgid "Continue Learning"
msgstr "继续学习"

#. Option for the 'Type' (Select) field in DocType 'Job Opportunity'
#: frontend/src/pages/Jobs.vue:178
#: lms/job/doctype/job_opportunity/job_opportunity.json
msgid "Contract"
msgstr "合同"

#: lms/lms/utils.py:442
msgid "Cookie Policy"
msgstr "Cookie政策"

#. Option for the 'Company Type' (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Corporate Organization"
msgstr "企业机构"

#. Option for the 'Status' (Select) field in DocType 'Exercise Latest
#. Submission'
#. Option for the 'Status' (Select) field in DocType 'Exercise Submission'
#: frontend/src/components/Quiz.vue:189
#: lms/lms/doctype/exercise_latest_submission/exercise_latest_submission.json
#: lms/lms/doctype/exercise_submission/exercise_submission.json
msgid "Correct"
msgstr "正确"

#: frontend/src/components/Modals/Question.vue:79
msgid "Correct Answer"
msgstr "正确答案"

#. Label of the country (Link) field in DocType 'User'
#. Label of the country (Link) field in DocType 'Job Opportunity'
#. Label of the country (Link) field in DocType 'Payment Country'
#: frontend/src/pages/Billing.vue:92 frontend/src/pages/JobForm.vue:40
#: frontend/src/pages/Jobs.vue:57 lms/fixtures/custom_field.json
#: lms/job/doctype/job_opportunity/job_opportunity.json
#: lms/lms/doctype/payment_country/payment_country.json
msgid "Country"
msgstr "国家"

#. Label of the course (Link) field in DocType 'Batch Course'
#. Label of the course (Link) field in DocType 'Cohort'
#. Label of the course (Link) field in DocType 'Cohort Mentor'
#. Label of the course (Link) field in DocType 'Cohort Staff'
#. Label of the course (Link) field in DocType 'Cohort Subgroup'
#. Label of the course (Link) field in DocType 'Course Chapter'
#. Label of the course (Link) field in DocType 'Course Lesson'
#. Label of the course (Link) field in DocType 'Exercise Latest Submission'
#. Label of the course (Link) field in DocType 'Exercise Submission'
#. Label of the course (Link) field in DocType 'LMS Assignment Submission'
#. Label of the course (Link) field in DocType 'LMS Batch Old'
#. Label of the course (Link) field in DocType 'LMS Certificate'
#. Label of the course (Link) field in DocType 'LMS Certificate Evaluation'
#. Label of the course (Link) field in DocType 'LMS Certificate Request'
#. Label of the course (Link) field in DocType 'LMS Course Interest'
#. Label of the course (Link) field in DocType 'LMS Course Mentor Mapping'
#. Label of the course (Link) field in DocType 'LMS Course Progress'
#. Label of the course (Link) field in DocType 'LMS Course Review'
#. Label of the course (Link) field in DocType 'LMS Enrollment'
#. Label of the course (Link) field in DocType 'LMS Exercise'
#. Label of the course (Link) field in DocType 'LMS Mentor Request'
#. Label of the course (Link) field in DocType 'LMS Program Course'
#. Label of the course (Link) field in DocType 'LMS Quiz'
#. Label of the course (Link) field in DocType 'LMS Quiz Submission'
#. Label of the course (Link) field in DocType 'LMS Video Watch Duration'
#. Label of the course (Link) field in DocType 'Related Courses'
#. Label of a Link in the LMS Workspace
#. Label of a shortcut in the LMS Workspace
#: frontend/src/components/Modals/BatchCourseModal.vue:20
#: frontend/src/components/Modals/BulkCertificates.vue:38
#: frontend/src/components/Modals/EvaluationModal.vue:20
#: frontend/src/components/Modals/Event.vue:24
#: lms/lms/doctype/batch_course/batch_course.json
#: lms/lms/doctype/cohort/cohort.json
#: lms/lms/doctype/cohort_mentor/cohort_mentor.json
#: lms/lms/doctype/cohort_staff/cohort_staff.json
#: lms/lms/doctype/cohort_subgroup/cohort_subgroup.json
#: lms/lms/doctype/course_chapter/course_chapter.json
#: lms/lms/doctype/course_lesson/course_lesson.json
#: lms/lms/doctype/exercise_latest_submission/exercise_latest_submission.json
#: lms/lms/doctype/exercise_submission/exercise_submission.json
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
#: lms/lms/doctype/lms_certificate/lms_certificate.json
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
#: lms/lms/doctype/lms_course_interest/lms_course_interest.json
#: lms/lms/doctype/lms_course_mentor_mapping/lms_course_mentor_mapping.json
#: lms/lms/doctype/lms_course_progress/lms_course_progress.json
#: lms/lms/doctype/lms_course_review/lms_course_review.json
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
#: lms/lms/doctype/lms_exercise/lms_exercise.json
#: lms/lms/doctype/lms_mentor_request/lms_mentor_request.json
#: lms/lms/doctype/lms_program_course/lms_program_course.json
#: lms/lms/doctype/lms_quiz/lms_quiz.json
#: lms/lms/doctype/lms_quiz_submission/lms_quiz_submission.json
#: lms/lms/doctype/lms_video_watch_duration/lms_video_watch_duration.json
#: lms/lms/doctype/related_courses/related_courses.json
#: lms/lms/report/course_progress_summary/course_progress_summary.js:9
#: lms/lms/report/course_progress_summary/course_progress_summary.py:51
#: lms/lms/workspace/lms/lms.json
msgid "Course"
msgstr "课程"

#. Name of a DocType
#. Label of the chapter (Link) field in DocType 'Course Lesson'
#: lms/lms/doctype/course_chapter/course_chapter.json
#: lms/lms/doctype/course_lesson/course_lesson.json
msgid "Course Chapter"
msgstr "课程章节"

#. Label of a shortcut in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "Course Completed"
msgstr "课程完成"

#: frontend/src/pages/Statistics.vue:31
msgid "Course Completions"
msgstr "课程完成情况"

#. Name of a role
#: frontend/src/pages/ProfileRoles.vue:26
#: lms/lms/doctype/course_chapter/course_chapter.json
#: lms/lms/doctype/course_lesson/course_lesson.json
#: lms/lms/doctype/lms_category/lms_category.json
#: lms/lms/doctype/lms_course/lms_course.json
#: lms/lms/doctype/lms_program/lms_program.json
#: lms/lms/doctype/lms_programming_exercise/lms_programming_exercise.json
#: lms/lms/doctype/lms_programming_exercise_submission/lms_programming_exercise_submission.json
#: lms/lms/doctype/lms_question/lms_question.json
#: lms/lms/doctype/lms_quiz/lms_quiz.json
#: lms/lms/doctype/lms_video_watch_duration/lms_video_watch_duration.json
msgid "Course Creator"
msgstr "课程创建者"

#. Label of a Card Break in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "Course Data"
msgstr "课程数据"

#: frontend/src/pages/CourseForm.vue:181
msgid "Course Description"
msgstr "课程描述"

#: frontend/src/pages/Statistics.vue:22
msgid "Course Enrollments"
msgstr "课程注册记录"

#. Name of a DocType
#: lms/lms/doctype/course_evaluator/course_evaluator.json
msgid "Course Evaluator"
msgstr "课程评估人"

#: frontend/src/pages/CourseForm.vue:90
msgid "Course Image"
msgstr "课程图片"

#. Name of a DocType
#: lms/lms/doctype/course_instructor/course_instructor.json
msgid "Course Instructor"
msgstr "课程讲师"

#. Name of a DocType
#: lms/lms/doctype/course_lesson/course_lesson.json
msgid "Course Lesson"
msgstr "课程课时"

#: lms/www/lms.py:87
msgid "Course List"
msgstr "课程列表"

#: lms/lms/report/course_progress_summary/course_progress_summary.py:58
msgid "Course Name"
msgstr "课程名称"

#: frontend/src/pages/CourseDetail.vue:78 frontend/src/pages/CourseForm.vue:293
msgid "Course Outline"
msgstr "课程大纲"

#. Name of a report
#: frontend/src/components/Modals/CourseProgressSummary.vue:5
#: lms/lms/report/course_progress_summary/course_progress_summary.json
msgid "Course Progress Summary"
msgstr "课程进度概览"

#. Label of the section_break_7 (Section Break) field in DocType 'LMS Course'
#: lms/lms/doctype/lms_course/lms_course.json
msgid "Course Settings"
msgstr "课程设置"

#. Label of a Card Break in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "Course Stats"
msgstr "课程统计"

#. Label of the title (Data) field in DocType 'Batch Course'
#. Label of the course_title (Data) field in DocType 'Course Chapter'
#. Label of the course_title (Data) field in DocType 'LMS Certificate'
#. Label of the course_title (Data) field in DocType 'LMS Certificate Request'
#. Label of the course_title (Data) field in DocType 'LMS Program Course'
#: lms/lms/doctype/batch_course/batch_course.json
#: lms/lms/doctype/course_chapter/course_chapter.json
#: lms/lms/doctype/lms_certificate/lms_certificate.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
#: lms/lms/doctype/lms_program_course/lms_program_course.json
msgid "Course Title"
msgstr "课程标题"

#: frontend/src/pages/ProgramForm.vue:234
msgid "Course added to program"
msgstr "课程已添加至项目"

#: frontend/src/pages/CourseForm.vue:523
msgid "Course created successfully"
msgstr "课程创建成功。"

#: frontend/src/pages/CourseForm.vue:560
msgid "Course deleted successfully"
msgstr "课程删除成功"

#: frontend/src/pages/ProgramForm.vue:303
msgid "Course moved successfully"
msgstr "课程移动成功"

#: frontend/src/pages/CourseForm.vue:543
msgid "Course updated successfully"
msgstr "课程更新成功。"

#: lms/lms/doctype/lms_batch/lms_batch.py:54
#: lms/lms/doctype/lms_program/lms_program.py:19
msgid "Course {0} has already been added to this batch."
msgstr "课程{0}已添加至本批次"

#. Label of the courses (Table) field in DocType 'LMS Batch'
#. Label of the show_courses (Check) field in DocType 'LMS Settings'
#. Label of the courses (Check) field in DocType 'LMS Settings'
#: frontend/src/components/BatchCourses.vue:5
#: frontend/src/components/BatchOverlay.vue:37
#: frontend/src/components/BatchStudents.vue:25
#: frontend/src/components/Modals/BatchStudentProgress.vue:91
#: frontend/src/pages/BatchDetail.vue:44
#: frontend/src/pages/CourseCertification.vue:127
#: frontend/src/pages/Courses.vue:331 frontend/src/pages/Courses.vue:338
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Courses"
msgstr "课程"

#: lms/lms/web_template/lms_statistics/lms_statistics.html:14
#: lms/templates/statistics.html:28
msgid "Courses Completed"
msgstr "已完成课程"

#: frontend/src/components/BatchCourses.vue:154
msgid "Courses deleted successfully"
msgstr "课程删除成功"

#. Label of the cover_image (Attach Image) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Cover Image"
msgstr "封面图片"

#: frontend/src/components/Modals/ChapterModal.vue:9
#: frontend/src/pages/Programs.vue:93
msgid "Create"
msgstr "创建"

#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.js:7
msgid "Create Certificate"
msgstr "创建证书"

#: frontend/src/components/Controls/Link.vue:38
#: frontend/src/components/Controls/MultiSelect.vue:66
msgid "Create New"
msgstr "新建"

#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseForm.vue:7
msgid "Create Programming Exercise"
msgstr ""

#: lms/templates/onboarding_header.html:19
msgid "Create a Course"
msgstr "创建课程"

#: frontend/src/components/Modals/LiveClassModal.vue:5
msgid "Create a Live Class"
msgstr "创建直播课程"

#: frontend/src/pages/Quizzes.vue:101
msgid "Create a Quiz"
msgstr ""

#: frontend/src/components/AppSidebar.vue:576
msgid "Create a batch"
msgstr "创建班级"

#: frontend/src/components/AppSidebar.vue:567
msgid "Create a course"
msgstr "创建课程"

#: frontend/src/components/AppSidebar.vue:577
msgid "Create a live class"
msgstr "创建直播课程"

#: frontend/src/components/Modals/AssignmentForm.vue:13
msgid "Create an Assignment"
msgstr "创建作业"

#: frontend/src/components/AppSidebar.vue:501
msgid "Create your first batch"
msgstr "创建首个班级"

#: frontend/src/components/AppSidebar.vue:432
msgid "Create your first course"
msgstr "创建首门课程"

#: frontend/src/components/AppSidebar.vue:479
msgid "Create your first quiz"
msgstr "创建首项测验"

#: frontend/src/pages/Assignments.vue:173 frontend/src/pages/Courses.vue:321
msgid "Created"
msgstr "已创建"

#: frontend/src/components/AppSidebar.vue:573
msgid "Creating a batch"
msgstr "正在创建班级"

#: frontend/src/components/AppSidebar.vue:564
msgid "Creating a course"
msgstr "正在创建课程"

#. Label of the currency (Link) field in DocType 'LMS Batch'
#. Label of the currency (Link) field in DocType 'LMS Course'
#. Label of the currency (Link) field in DocType 'LMS Payment'
#: frontend/src/pages/BatchForm.vue:272 frontend/src/pages/CourseForm.vue:262
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_course/lms_course.json
#: lms/lms/doctype/lms_payment/lms_payment.json
msgid "Currency"
msgstr "币种"

#. Label of the current_lesson (Link) field in DocType 'LMS Enrollment'
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
msgid "Current Lesson"
msgstr "当前课时"

#: frontend/src/components/AppSidebar.vue:595
msgid "Custom Certificate Templates"
msgstr "自定义证书模板"

#. Label of the custom_component (Code) field in DocType 'LMS Batch'
#: lms/lms/doctype/lms_batch/lms_batch.json
msgid "Custom HTML"
msgstr "自定义HTML"

#. Label of the custom_script (Code) field in DocType 'LMS Batch'
#: lms/lms/doctype/lms_batch/lms_batch.json
msgid "Custom Script (JavaScript)"
msgstr "自定义脚本（JavaScript）"

#. Label of the custom_signup_content (HTML Editor) field in DocType 'LMS
#. Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Custom Signup Content"
msgstr "自定义注册内容"

#. Label of the customisations_tab (Tab Break) field in DocType 'LMS Batch'
#: lms/lms/doctype/lms_batch/lms_batch.json
msgid "Customisations"
msgstr "自定义配置"

#. Label of the show_dashboard (Check) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Dashboard"
msgstr "仪表盘"

#. Label of the date (Date) field in DocType 'LMS Batch Timetable'
#. Label of the date (Date) field in DocType 'LMS Certificate Evaluation'
#. Label of the date (Date) field in DocType 'LMS Certificate Request'
#. Label of the date (Date) field in DocType 'LMS Live Class'
#. Label of the date (Date) field in DocType 'Scheduled Flow'
#: frontend/src/components/Modals/EvaluationModal.vue:26
#: frontend/src/components/Modals/Event.vue:40
#: frontend/src/components/Modals/LiveClassModal.vue:29
#: lms/lms/doctype/lms_batch_timetable/lms_batch_timetable.json
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
#: lms/lms/doctype/lms_live_class/lms_live_class.json
#: lms/lms/doctype/scheduled_flow/scheduled_flow.json
#: lms/templates/quiz/quiz.html:149
msgid "Date"
msgstr "日期"

#: frontend/src/pages/BatchForm.vue:69
msgid "Date and Time"
msgstr "日期与时间"

#: lms/templates/emails/live_class_reminder.html:13
msgid "Date:"
msgstr "日期："

#. Label of the day (Select) field in DocType 'Evaluator Schedule'
#. Label of the day (Int) field in DocType 'LMS Batch Timetable'
#. Label of the day (Select) field in DocType 'LMS Certificate Request'
#: frontend/src/pages/ProfileEvaluator.vue:26
#: lms/lms/doctype/evaluator_schedule/evaluator_schedule.json
#: lms/lms/doctype/lms_batch_timetable/lms_batch_timetable.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
msgid "Day"
msgstr "天"

#: lms/templates/emails/mentor_request_creation_email.html:2
#: lms/templates/emails/mentor_request_status_update_email.html:2
msgid "Dear"
msgstr "尊敬的"

#: lms/templates/emails/batch_confirmation.html:2
#: lms/templates/emails/batch_start_reminder.html:2
#: lms/templates/emails/certification.html:2
#: lms/templates/emails/live_class_reminder.html:2
msgid "Dear "
msgstr "尊敬的"

#: frontend/src/components/Modals/EmailTemplateModal.vue:66
msgid "Dear {{ member_name }},\\n\\nYou have been enrolled in our upcoming batch {{ batch_name }}.\\n\\nThanks,\\nFrappe Learning"
msgstr ""

#. Label of the default_currency (Link) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Default Currency"
msgstr "默认币种"

#. Label of the degree_type (Data) field in DocType 'Education Detail'
#: lms/lms/doctype/education_detail/education_detail.json
msgid "Degree Type"
msgstr "学位类型"

#: frontend/src/components/Controls/ChildTable.vue:56
#: frontend/src/components/CourseOutline.vue:253
#: frontend/src/components/CourseOutline.vue:311
#: frontend/src/pages/CourseForm.vue:573
#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseForm.vue:67
msgid "Delete"
msgstr "删除"

#: frontend/src/components/CourseOutline.vue:56
msgid "Delete Chapter"
msgstr "删除章节"

#: frontend/src/pages/CourseForm.vue:567
msgid "Delete Course"
msgstr "删除课程"

#: frontend/src/components/CourseOutline.vue:305
msgid "Delete this chapter?"
msgstr "确认删除此章节？"

#: frontend/src/components/CourseOutline.vue:247
msgid "Delete this lesson?"
msgstr "确认删除此课时？"

#: frontend/src/pages/CourseForm.vue:568
msgid "Deleting the course will also delete all its chapters and lessons. Are you sure you want to delete this course?"
msgstr "删除本课程将同时删除其所有章节和课时。此操作不可逆。确认继续吗？"

#: frontend/src/components/CourseOutline.vue:306
msgid "Deleting this chapter will also delete all its lessons and permanently remove it from the course. This action cannot be undone. Are you sure you want to continue?"
msgstr "删除本章节将同时删除其所有课时并永久移除。此操作不可撤销。确认继续吗？"

#: frontend/src/components/CourseOutline.vue:248
msgid "Deleting this lesson will permanently remove it from the course. This action cannot be undone. Are you sure you want to continue?"
msgstr "删除本课时将永久移除。此操作不可撤销。确认继续吗？"

#. Label of the description (Text Editor) field in DocType 'Job Opportunity'
#. Label of the description (Small Text) field in DocType 'Certification'
#. Label of the description (Markdown Editor) field in DocType 'Cohort'
#. Label of the description (Markdown Editor) field in DocType 'Cohort
#. Subgroup'
#. Label of the description (Small Text) field in DocType 'LMS Badge'
#. Label of the description (Small Text) field in DocType 'LMS Batch'
#. Label of the description (Markdown Editor) field in DocType 'LMS Batch Old'
#. Label of the description (Text Editor) field in DocType 'LMS Course'
#. Label of the description (Small Text) field in DocType 'LMS Exercise'
#. Label of the description (Text) field in DocType 'LMS Live Class'
#. Label of the description (Small Text) field in DocType 'Work Experience'
#: frontend/src/components/Modals/LiveClassModal.vue:80
#: frontend/src/pages/JobForm.vue:125
#: lms/job/doctype/job_opportunity/job_opportunity.json
#: lms/lms/doctype/certification/certification.json
#: lms/lms/doctype/cohort/cohort.json
#: lms/lms/doctype/cohort_subgroup/cohort_subgroup.json
#: lms/lms/doctype/lms_badge/lms_badge.json
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
#: lms/lms/doctype/lms_course/lms_course.json
#: lms/lms/doctype/lms_exercise/lms_exercise.json
#: lms/lms/doctype/lms_live_class/lms_live_class.json
#: lms/lms/doctype/work_experience/work_experience.json
msgid "Description"
msgstr "描述"

#: frontend/src/components/Apps.vue:51
msgid "Desk"
msgstr "工作台"

#: frontend/src/components/Modals/DiscussionModal.vue:22
#: frontend/src/pages/BatchForm.vue:14 frontend/src/pages/CourseForm.vue:25
#: frontend/src/pages/QuizForm.vue:50
msgid "Details"
msgstr "详情"

#: frontend/src/pages/CourseForm.vue:172
msgid "Disable Self Enrollment"
msgstr "禁用自助注册"

#. Label of the disable_self_learning (Check) field in DocType 'LMS Course'
#: lms/lms/doctype/lms_course/lms_course.json
msgid "Disable Self Learning"
msgstr "禁用自主学习"

#. Label of the disable_signup (Check) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Disable Signup"
msgstr "禁用注册功能"

#. Label of the disabled (Check) field in DocType 'Job Opportunity'
#: frontend/src/components/Settings/ZoomSettings.vue:56
#: lms/job/doctype/job_opportunity/job_opportunity.json
msgid "Disabled"
msgstr "已禁用"

#: frontend/src/components/DiscussionReplies.vue:57
#: lms/lms/widgets/NoPreviewModal.html:25 lms/templates/reviews.html:159
msgid "Discard"
msgstr "放弃"

#. Label of the show_discussions (Check) field in DocType 'LMS Settings'
#: frontend/src/pages/Batch.vue:88
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Discussions"
msgstr "讨论区"

#. Option for the 'File Type' (Select) field in DocType 'Course Lesson'
#. Option for the 'Type' (Select) field in DocType 'LMS Assignment'
#. Option for the 'Type' (Select) field in DocType 'LMS Assignment Submission'
#: lms/lms/doctype/course_lesson/course_lesson.json
#: lms/lms/doctype/lms_assignment/lms_assignment.json
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
msgid "Document"
msgstr "文档"

#: lms/templates/emails/payment_reminder.html:11
msgid "Don’t miss this opportunity to enhance your skills. Click below to complete your enrollment"
msgstr "切勿错过提升技能的机会。点击下方完成注册"

#. Label of the dream_companies (Data) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Dream Companies"
msgstr "目标企业"

#: lms/lms/doctype/lms_question/lms_question.py:33
msgid "Duplicate options found for this question."
msgstr "发现重复选项"

#. Label of the duration (Data) field in DocType 'Cohort'
#. Label of the duration (Data) field in DocType 'LMS Batch Timetable'
#. Label of the duration (Int) field in DocType 'LMS Live Class'
#. Label of the duration (Int) field in DocType 'LMS Live Class Participant'
#: frontend/src/components/Modals/LiveClassModal.vue:36
#: lms/lms/doctype/cohort/cohort.json
#: lms/lms/doctype/lms_batch_timetable/lms_batch_timetable.json
#: lms/lms/doctype/lms_live_class/lms_live_class.json
#: lms/lms/doctype/lms_live_class_participant/lms_live_class_participant.json
msgid "Duration"
msgstr "时长"

#. Label of the duration (Data) field in DocType 'LMS Quiz'
#: frontend/src/pages/QuizForm.vue:67 lms/lms/doctype/lms_quiz/lms_quiz.json
msgid "Duration (in minutes)"
msgstr "时长（分钟）"

#: frontend/src/components/Modals/LiveClassModal.vue:32
msgid "Duration of the live class in minutes"
msgstr "直播课程时长（单位：分钟）"

#. Label of the email (Link) field in DocType 'Cohort Join Request'
#: lms/lms/doctype/cohort_join_request/cohort_join_request.json
msgid "E-Mail"
msgstr "电子邮件"

#. Label of the email (Link) field in DocType 'Cohort Mentor'
#: lms/lms/doctype/cohort_mentor/cohort_mentor.json
msgid "E-mail"
msgstr "电子邮件"

#: frontend/src/components/BatchOverlay.vue:116
#: frontend/src/components/CourseCardOverlay.vue:116
#: frontend/src/components/Modals/ChapterModal.vue:9
#: frontend/src/pages/JobDetail.vue:34 frontend/src/pages/Lesson.vue:130
#: frontend/src/pages/Profile.vue:36 frontend/src/pages/Programs.vue:53
msgid "Edit"
msgstr "编辑"

#: frontend/src/components/Modals/AssignmentForm.vue:14
msgid "Edit Assignment"
msgstr "编辑作业"

#: frontend/src/components/CourseOutline.vue:49
#: frontend/src/components/Modals/ChapterModal.vue:5
msgid "Edit Chapter"
msgstr "编辑章节"

#: frontend/src/components/Modals/EmailTemplateModal.vue:8
msgid "Edit Email Template"
msgstr ""

#: frontend/src/pages/Profile.vue:72
msgid "Edit Profile"
msgstr "编辑个人资料"

#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseForm.vue:8
msgid "Edit Programming Exercise"
msgstr ""

#: frontend/src/components/Modals/ZoomAccountModal.vue:6
msgid "Edit Zoom Account"
msgstr ""

#: frontend/src/pages/QuizForm.vue:199
msgid "Edit the question"
msgstr "编辑试题"

#. Label of the education (Table) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Education"
msgstr "教育背景"

#. Name of a DocType
#: lms/lms/doctype/education_detail/education_detail.json
msgid "Education Detail"
msgstr "教育详情"

#. Label of the education_details (Section Break) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Education Details"
msgstr "教育背景详情"

#: frontend/src/components/Settings/Evaluators.vue:105
#: frontend/src/components/Settings/Members.vue:103
#: lms/templates/signup-form.html:10
msgid "Email"
msgstr "电子邮件"

#: frontend/src/components/Modals/Event.vue:16
msgid "Email ID"
msgstr "邮箱账号"

#. Label of the email_sent (Check) field in DocType 'LMS Course Interest'
#: lms/lms/doctype/lms_course_interest/lms_course_interest.json
msgid "Email Sent"
msgstr "邮件已发送"

#: frontend/src/pages/BatchForm.vue:154
msgid "Email Template"
msgstr "邮件模板"

#: frontend/src/components/Modals/EmailTemplateModal.vue:117
msgid "Email Template created successfully"
msgstr ""

#: frontend/src/components/Modals/EmailTemplateModal.vue:146
msgid "Email Template updated successfully"
msgstr ""

#. Label of the email_templates_tab (Tab Break) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Email Templates"
msgstr "邮件模板"

#: frontend/src/components/Settings/EmailTemplates.vue:128
#: frontend/src/components/Settings/ZoomSettings.vue:161
msgid "Email Templates deleted successfully"
msgstr ""

#. Label of the show_emails (Check) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Emails"
msgstr "邮件"

#. Option for the 'User Category' (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json lms/templates/signup-form.html:25
msgid "Employee"
msgstr "员工"

#. Label of the enable (Check) field in DocType 'Zoom Settings'
#: lms/lms/doctype/zoom_settings/zoom_settings.json
msgid "Enable"
msgstr "启用"

#: lms/lms/doctype/lms_settings/lms_settings.py:21
msgid "Enable Google API in Google Settings to send calendar invites for evaluations."
msgstr "在Google设置中启用API以发送评估日历邀请"

#. Label of the enable_learning_paths (Check) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Enable Learning Paths"
msgstr "启用学习路径"

#. Label of the enable_negative_marking (Check) field in DocType 'LMS Quiz'
#: frontend/src/pages/QuizForm.vue:117 lms/lms/doctype/lms_quiz/lms_quiz.json
msgid "Enable Negative Marking"
msgstr ""

#: frontend/src/components/Modals/ChapterModal.vue:24
msgid "Enable this only if you want to upload a SCORM package as a chapter."
msgstr "仅在上传SCORM包作为章节时启用"

#. Label of the enabled (Check) field in DocType 'LMS Badge'
#. Label of the enabled (Check) field in DocType 'LMS Zoom Settings'
#: frontend/src/components/Modals/ZoomAccountModal.vue:23
#: frontend/src/components/Settings/ZoomSettings.vue:53
#: lms/lms/doctype/lms_badge/lms_badge.json
#: lms/lms/doctype/lms_zoom_settings/lms_zoom_settings.json
msgid "Enabled"
msgstr "已启用"

#: frontend/src/components/Modals/BulkCertificates.vue:53
msgid "Enabling this will publish the certificate on the certified participants page."
msgstr "启用后证书将发布至认证参与者页面"

#. Label of the end_date (Date) field in DocType 'Cohort'
#. Label of the end_date (Date) field in DocType 'LMS Batch'
#: frontend/src/pages/BatchForm.vue:82 lms/lms/doctype/cohort/cohort.json
#: lms/lms/doctype/lms_batch/lms_batch.json
msgid "End Date"
msgstr "结束日期"

#. Label of the end_date (Date) field in DocType 'Education Detail'
#: lms/lms/doctype/education_detail/education_detail.json
msgid "End Date (or expected)"
msgstr "结束日期（或预期）"

#. Label of the end_time (Time) field in DocType 'Evaluator Schedule'
#. Label of the end_time (Time) field in DocType 'LMS Batch'
#. Label of the end_time (Time) field in DocType 'LMS Batch Old'
#. Label of the end_time (Time) field in DocType 'LMS Batch Timetable'
#. Label of the end_time (Time) field in DocType 'LMS Certificate Evaluation'
#. Label of the end_time (Time) field in DocType 'LMS Certificate Request'
#. Label of the end_time (Time) field in DocType 'Scheduled Flow'
#: frontend/src/pages/BatchForm.vue:98
#: frontend/src/pages/ProfileEvaluator.vue:32
#: lms/lms/doctype/evaluator_schedule/evaluator_schedule.json
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
#: lms/lms/doctype/lms_batch_timetable/lms_batch_timetable.json
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
#: lms/lms/doctype/scheduled_flow/scheduled_flow.json
msgid "End Time"
msgstr "结束时间"

#: frontend/src/components/LiveClass.vue:89
msgid "Ended"
msgstr ""

#: frontend/src/components/BatchOverlay.vue:103
msgid "Enroll Now"
msgstr "立即报名"

#: frontend/src/pages/Batches.vue:286 frontend/src/pages/Courses.vue:324
msgid "Enrolled"
msgstr "已注册"

#: frontend/src/components/CourseCard.vue:46
#: frontend/src/components/CourseCardOverlay.vue:138
#: frontend/src/pages/CourseDetail.vue:33
msgid "Enrolled Students"
msgstr "注册学员"

#: lms/lms/doctype/lms_batch_enrollment/lms_batch_enrollment.py:93
msgid "Enrollment Confirmation for {0}"
msgstr "{0}的注册确认"

#: lms/lms/web_template/lms_statistics/lms_statistics.html:14
#: lms/templates/statistics.html:20
msgid "Enrollment Count"
msgstr "注册人数"

#: lms/lms/utils.py:1943
msgid "Enrollment Failed"
msgstr "注册失败"

#. Label of the enrollments (Int) field in DocType 'LMS Course'
#. Label of a chart in the LMS Workspace
#. Label of a shortcut in the LMS Workspace
#: lms/lms/doctype/lms_course/lms_course.json lms/lms/workspace/lms/lms.json
msgid "Enrollments"
msgstr "注册记录"

#: lms/lms/doctype/lms_settings/lms_settings.py:26
msgid "Enter Client Id and Client Secret in Google Settings to send calendar invites for evaluations."
msgstr "在Google设置中填写客户端ID和密钥以发送评估日历邀请"

#: frontend/src/components/Assignment.vue:113
msgid "Enter a URL"
msgstr "输入URL"

#: lms/templates/quiz/quiz.html:53
msgid "Enter the correct answer"
msgstr "输入正确答案"

#: frontend/src/components/Modals/ZoomAccountModal.vue:163
msgid "Error creating Zoom Account"
msgstr ""

#: frontend/src/components/Modals/EmailTemplateModal.vue:122
msgid "Error creating email template"
msgstr ""

#: lms/lms/doctype/lms_batch/lms_batch.py:204
msgid "Error creating live class. Please try again. {0}"
msgstr "创建直播课程失败，请重试。错误：{0}"

#: frontend/src/pages/Quizzes.vue:212
msgid "Error creating quiz: {0}"
msgstr ""

#: frontend/src/components/Settings/EmailTemplates.vue:133
#: frontend/src/components/Settings/ZoomSettings.vue:166
msgid "Error deleting email templates"
msgstr ""

#: frontend/src/components/Modals/ZoomAccountModal.vue:200
msgid "Error updating Zoom Account"
msgstr ""

#: frontend/src/components/Modals/EmailTemplateModal.vue:151
msgid "Error updating email template"
msgstr ""

#. Label of a Link in the LMS Workspace
#. Label of a shortcut in the LMS Workspace
#: frontend/src/components/Modals/Event.vue:374 lms/lms/workspace/lms/lms.json
msgid "Evaluation"
msgstr "评估"

#. Label of the section_break_6 (Section Break) field in DocType 'LMS
#. Certificate Evaluation'
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
msgid "Evaluation Details"
msgstr "评估详情"

#. Label of the evaluation_end_date (Date) field in DocType 'LMS Batch'
#: frontend/src/pages/BatchForm.vue:115
#: lms/lms/doctype/lms_batch/lms_batch.json
msgid "Evaluation End Date"
msgstr "评估结束日期"

#. Label of a Link in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "Evaluation Request"
msgstr "评估请求"

#: lms/lms/doctype/lms_batch/lms_batch.py:87
msgid "Evaluation end date cannot be less than the batch end date."
msgstr "评估结束日期不得早于批次结束日期"

#: frontend/src/components/Modals/Event.vue:256
msgid "Evaluation saved successfully"
msgstr "评估保存成功"

#. Label of the evaluator (Link) field in DocType 'Batch Course'
#. Label of the evaluator (Link) field in DocType 'Course Evaluator'
#. Label of the evaluator (Link) field in DocType 'LMS Assignment Submission'
#. Label of the evaluator (Link) field in DocType 'LMS Certificate'
#. Label of the evaluator (Link) field in DocType 'LMS Certificate Evaluation'
#. Label of the evaluator (Link) field in DocType 'LMS Certificate Request'
#. Label of the evaluator (Link) field in DocType 'LMS Course'
#: frontend/src/components/Modals/BatchCourseModal.vue:37
#: frontend/src/components/Modals/BulkCertificates.vue:22
#: frontend/src/pages/CourseForm.vue:251 frontend/src/pages/ProfileRoles.vue:32
#: lms/lms/doctype/batch_course/batch_course.json
#: lms/lms/doctype/course_evaluator/course_evaluator.json
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
#: lms/lms/doctype/lms_certificate/lms_certificate.json
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
#: lms/lms/doctype/lms_course/lms_course.json
#: lms/templates/upcoming_evals.html:33
msgid "Evaluator"
msgstr "评估人"

#. Label of the evaluator_name (Data) field in DocType 'LMS Certificate'
#. Label of the evaluator_name (Data) field in DocType 'LMS Certificate
#. Evaluation'
#. Label of the evaluator_name (Data) field in DocType 'LMS Certificate
#. Request'
#: lms/lms/doctype/lms_certificate/lms_certificate.json
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
msgid "Evaluator Name"
msgstr "评估人姓名"

#. Name of a DocType
#: lms/lms/doctype/evaluator_schedule/evaluator_schedule.json
msgid "Evaluator Schedule"
msgstr "评估人日程"

#: frontend/src/components/Settings/Evaluators.vue:163
msgid "Evaluator added successfully"
msgstr ""

#: frontend/src/components/Settings/Evaluators.vue:196
msgid "Evaluator deleted successfully"
msgstr ""

#: lms/lms/api.py:1419
msgid "Evaluator does not exist."
msgstr ""

#: lms/lms/doctype/lms_course/lms_course.py:67
msgid "Evaluator is required for paid certificates."
msgstr "付费证书需指定评估人"

#. Label of the event (Select) field in DocType 'LMS Badge'
#. Label of the event (Link) field in DocType 'LMS Live Class'
#: lms/lms/doctype/lms_badge/lms_badge.json
#: lms/lms/doctype/lms_live_class/lms_live_class.json
msgid "Event"
msgstr "事件"

#: frontend/src/pages/BatchForm.vue:109
msgid "Example: IST (+5:30)"
msgstr "示例：IST（+5:30）"

#. Label of the exercise (Link) field in DocType 'Exercise Latest Submission'
#. Label of the exercise (Link) field in DocType 'Exercise Submission'
#. Label of the exercise (Link) field in DocType 'LMS Programming Exercise
#. Submission'
#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseSubmissions.vue:274
#: lms/lms/doctype/exercise_latest_submission/exercise_latest_submission.json
#: lms/lms/doctype/exercise_submission/exercise_submission.json
#: lms/lms/doctype/lms_programming_exercise_submission/lms_programming_exercise_submission.json
msgid "Exercise"
msgstr "练习"

#. Name of a DocType
#: lms/lms/doctype/exercise_latest_submission/exercise_latest_submission.json
msgid "Exercise Latest Submission"
msgstr "最新练习提交"

#. Name of a DocType
#: lms/lms/doctype/exercise_submission/exercise_submission.json
msgid "Exercise Submission"
msgstr "练习提交"

#. Label of the exercise_title (Data) field in DocType 'Exercise Latest
#. Submission'
#. Label of the exercise_title (Data) field in DocType 'Exercise Submission'
#. Label of the exercise_title (Data) field in DocType 'LMS Programming
#. Exercise Submission'
#: lms/lms/doctype/exercise_latest_submission/exercise_latest_submission.json
#: lms/lms/doctype/exercise_submission/exercise_submission.json
#: lms/lms/doctype/lms_programming_exercise_submission/lms_programming_exercise_submission.json
msgid "Exercise Title"
msgstr "练习标题"

#: frontend/src/components/AppSidebar.vue:142
msgid "Expand"
msgstr "展开"

#. Label of the expected_output (Data) field in DocType 'LMS Test Case'
#. Label of the expected_output (Data) field in DocType 'LMS Test Case
#. Submission'
#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseSubmission.vue:127
#: lms/lms/doctype/lms_test_case/lms_test_case.json
#: lms/lms/doctype/lms_test_case_submission/lms_test_case_submission.json
msgid "Expected Output"
msgstr ""

#. Label of the expiration_date (Data) field in DocType 'Certification'
#: lms/lms/doctype/certification/certification.json
msgid "Expiration Date"
msgstr "失效日期"

#. Label of the expiry_date (Date) field in DocType 'LMS Certificate'
#: frontend/src/components/Modals/BulkCertificates.vue:33
#: frontend/src/components/Modals/Event.vue:126
#: lms/lms/doctype/lms_certificate/lms_certificate.json
msgid "Expiry Date"
msgstr "到期日"

#. Label of the explanation_1 (Small Text) field in DocType 'LMS Question'
#. Label of the explanation_3 (Small Text) field in DocType 'LMS Question'
#. Label of the explanation_4 (Small Text) field in DocType 'LMS Question'
#: frontend/src/components/Modals/Question.vue:75
#: lms/lms/doctype/lms_question/lms_question.json
msgid "Explanation"
msgstr "解析"

#. Label of the explanation_2 (Small Text) field in DocType 'LMS Question'
#: lms/lms/doctype/lms_question/lms_question.json
msgid "Explanation "
msgstr "解析"

#: lms/lms/web_template/course_cards/course_cards.html:15
#: lms/lms/web_template/recently_published_courses/recently_published_courses.html:16
msgid "Explore More"
msgstr "查看更多"

#. Option for the 'Status' (Select) field in DocType 'LMS Assignment
#. Submission'
#. Option for the 'Status' (Select) field in DocType 'LMS Certificate
#. Evaluation'
#: frontend/src/components/Modals/Event.vue:366
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
msgid "Fail"
msgstr "未通过"

#. Option for the 'Status' (Select) field in DocType 'LMS Programming Exercise
#. Submission'
#. Option for the 'Status' (Select) field in DocType 'LMS Test Case Submission'
#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseSubmissions.vue:37
#: lms/lms/doctype/lms_programming_exercise_submission/lms_programming_exercise_submission.json
#: lms/lms/doctype/lms_test_case_submission/lms_test_case_submission.json
msgid "Failed"
msgstr "失败"

#: lms/lms/doctype/lms_live_class/lms_live_class.py:139
msgid "Failed to fetch attendance data from Zoom for class {0}: {1}"
msgstr ""

#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseSubmission.vue:358
msgid "Failed to submit. Please try again. {0}"
msgstr ""

#: frontend/src/utils/index.js:668
msgid "Failed to update meta tags {0}"
msgstr ""

#. Label of the featured (Check) field in DocType 'LMS Course'
#: frontend/src/components/CourseCard.vue:20
#: frontend/src/pages/CourseForm.vue:167
#: lms/lms/doctype/lms_course/lms_course.json
msgid "Featured"
msgstr "精选"

#. Label of the feedback (Small Text) field in DocType 'LMS Batch Feedback'
#: frontend/src/components/BatchFeedback.vue:30
#: frontend/src/pages/Batch.vue:146
#: lms/lms/doctype/lms_batch_feedback/lms_batch_feedback.json
msgid "Feedback"
msgstr "反馈"

#: frontend/src/components/Assignment.vue:64
msgid "Feel free to make edits to your submission if needed."
msgstr "可根据需要修改提交内容"

#. Label of the field_to_check (Select) field in DocType 'LMS Badge'
#: lms/lms/doctype/lms_badge/lms_badge.json
msgid "Field To Check"
msgstr "待检查字段"

#. Label of the major (Data) field in DocType 'Education Detail'
#: lms/lms/doctype/education_detail/education_detail.json
msgid "Field of Major/Study"
msgstr "专业/研究领域"

#. Label of the file_type (Select) field in DocType 'Course Lesson'
#: lms/lms/doctype/course_lesson/course_lesson.json
msgid "File Type"
msgstr "文件类型"

#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseSubmissions.vue:23
msgid "Filter by Exercise"
msgstr ""

#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseSubmissions.vue:28
msgid "Filter by Member"
msgstr ""

#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseSubmissions.vue:39
msgid "Filter by Status"
msgstr ""

#: frontend/src/components/Modals/EditProfile.vue:59
#: frontend/src/components/Settings/Members.vue:110
msgid "First Name"
msgstr "名字"

#. Option for the 'Time Preference' (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Fixed 9-5"
msgstr "固定9-5工作制"

#. Option for the 'Time Preference' (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Flexible Time"
msgstr "弹性时间"

#. Option for the 'Attire Preference' (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Formal Wear"
msgstr "正装"

#: lms/lms/widgets/CourseCard.html:114
msgid "Free"
msgstr "免费"

#. Option for the 'Type' (Select) field in DocType 'Job Opportunity'
#: frontend/src/pages/Jobs.vue:179
#: lms/job/doctype/job_opportunity/job_opportunity.json
msgid "Freelance"
msgstr "自由职业"

#. Option for the 'User Category' (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json lms/templates/signup-form.html:27
msgid "Freelancer/Just looking"
msgstr "自由职业者/观望中"

#. Option for the 'Grade Type' (Select) field in DocType 'Education Detail'
#: lms/lms/doctype/education_detail/education_detail.json
msgid "French (e.g. Distinction)"
msgstr "法国评分（如优秀）"

#. Option for the 'Day' (Select) field in DocType 'Evaluator Schedule'
#. Option for the 'Day' (Select) field in DocType 'LMS Certificate Request'
#: lms/lms/doctype/evaluator_schedule/evaluator_schedule.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
msgid "Friday"
msgstr "星期五"

#. Label of the unavailable_from (Date) field in DocType 'Course Evaluator'
#: frontend/src/pages/ProfileEvaluator.vue:99
#: lms/lms/doctype/course_evaluator/course_evaluator.json
msgid "From"
msgstr "自"

#. Label of the from_date (Date) field in DocType 'Work Experience'
#: lms/lms/doctype/work_experience/work_experience.json
msgid "From Date"
msgstr "起始日期"

#. Label of the full_name (Data) field in DocType 'Course Evaluator'
#. Label of the full_name (Data) field in DocType 'Invite Request'
#. Label of the full_name (Data) field in DocType 'LMS Program Member'
#: lms/lms/doctype/course_evaluator/course_evaluator.json
#: lms/lms/doctype/invite_request/invite_request.json
#: lms/lms/doctype/lms_program_member/lms_program_member.json
#: lms/templates/signup-form.html:5
msgid "Full Name"
msgstr "全名"

#. Option for the 'Type' (Select) field in DocType 'Job Opportunity'
#: frontend/src/pages/Jobs.vue:176
#: lms/job/doctype/job_opportunity/job_opportunity.json
msgid "Full Time"
msgstr "全职"

#. Name of a DocType
#. Label of the function (Data) field in DocType 'Function'
#. Label of the function (Link) field in DocType 'Preferred Function'
#: lms/lms/doctype/function/function.json
#: lms/lms/doctype/preferred_function/preferred_function.json
msgid "Function"
msgstr "职能"

#: frontend/src/pages/Billing.vue:43
msgid "GST Amount"
msgstr "商品及服务税额"

#: frontend/src/pages/Billing.vue:110
msgid "GST Number"
msgstr "商品及服务税号"

#. Label of the gstin (Data) field in DocType 'LMS Payment'
#: lms/lms/doctype/lms_payment/lms_payment.json
msgid "GSTIN"
msgstr "商品及服务税识别号"

#. Label of the general_tab (Tab Break) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "General"
msgstr "常规"

#: frontend/src/components/Modals/BulkCertificates.vue:5
#: frontend/src/pages/Batch.vue:12
msgid "Generate Certificates"
msgstr "生成证书"

#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.js:15
msgid "Generate Google Meet Link"
msgstr "生成Google Meet链接"

#: frontend/src/components/CourseCardOverlay.vue:89
msgid "Get Certificate"
msgstr "获取证书"

#: frontend/src/components/CertificationLinks.vue:34
#: frontend/src/components/CertificationLinks.vue:50
#: frontend/src/pages/CertifiedParticipants.vue:11
msgid "Get Certified"
msgstr "获取认证"

#: lms/templates/onboarding_header.html:8
msgid "Get Started"
msgstr "开始使用"

#. Label of the github (Data) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Github ID"
msgstr "Github账号"

#. Label of the google_meet_link (Data) field in DocType 'LMS Certificate
#. Request'
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
msgid "Google Meet Link"
msgstr "Google Meet链接"

#. Label of the grade (Data) field in DocType 'Education Detail'
#: frontend/src/components/Assignment.vue:158
#: lms/lms/doctype/education_detail/education_detail.json
msgid "Grade"
msgstr "评分"

#. Label of the grade_assignment (Check) field in DocType 'LMS Assignment'
#: lms/lms/doctype/lms_assignment/lms_assignment.json
msgid "Grade Assignment"
msgstr "作业评分"

#. Label of the grade_type (Select) field in DocType 'Education Detail'
#: lms/lms/doctype/education_detail/education_detail.json
msgid "Grade Type"
msgstr "评分类型"

#: frontend/src/components/Assignment.vue:153
msgid "Grading"
msgstr "评分"

#. Label of the grant_only_once (Check) field in DocType 'LMS Badge'
#: lms/lms/doctype/lms_badge/lms_badge.json
msgid "Grant only once"
msgstr "仅授予一次"

#: lms/templates/signup-form.html:56
msgid "Have an account? Login"
msgstr "已有账号？登录"

#. Label of the headline (Data) field in DocType 'User'
#: frontend/src/components/Modals/EditProfile.vue:69
#: lms/fixtures/custom_field.json
msgid "Headline"
msgstr "标题"

#: lms/lms/widgets/HelloWorld.html:13
msgid "Hello"
msgstr "您好"

#: frontend/src/components/AppSidebar.vue:128
msgid "Help"
msgstr "帮助"

#: lms/templates/courses_created.html:15
msgid "Help others learn something new by creating a course."
msgstr "通过创建课程帮助他人学习新知"

#: frontend/src/components/BatchFeedback.vue:15
msgid "Help us improve by providing your feedback."
msgstr ""

#: lms/templates/reviews.html:101
msgid "Help us improve our course material."
msgstr "帮助我们改进课程材料"

#: frontend/src/pages/PersonaForm.vue:16
msgid "Help us understand your needs"
msgstr "请说明您的需求"

#: lms/lms/notification/certificate_request_reminder/certificate_request_reminder.html:1
#: lms/templates/emails/certificate_request_notification.html:1
msgid "Hey {0}"
msgstr "您好，{0}"

#: lms/templates/emails/job_report.html:3
msgid "Hey,"
msgstr "您好，"

#: lms/templates/emails/payment_reminder.html:2
msgid "Hi"
msgstr "您好"

#: lms/templates/emails/lms_course_interest.html:3
msgid "Hi {0},"
msgstr "您好，{0}："

#: lms/templates/emails/lms_invite_request_approved.html:3
msgid "Hi,"
msgstr "您好："

#. Label of the hide_private (Check) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Hide my Private Information from others"
msgstr "隐藏我的个人信息"

#. Label of the hints (Small Text) field in DocType 'LMS Exercise'
#: lms/lms/doctype/lms_exercise/lms_exercise.json
msgid "Hints"
msgstr "提示"

#. Label of the host (Link) field in DocType 'LMS Live Class'
#: lms/lms/doctype/lms_live_class/lms_live_class.json
msgid "Host"
msgstr "主持人"

#. Label of the current (Check) field in DocType 'Work Experience'
#: lms/lms/doctype/work_experience/work_experience.json
msgid "I am currently working here"
msgstr "我目前在此任职"

#: lms/templates/emails/certification.html:6
msgid "I am delighted to inform you that you have successfully earned your certification for the {0} course. Congratulations!"
msgstr "很高兴通知您已成功获得{0}课程认证。祝贺您！"

#. Label of the looking_for_job (Check) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "I am looking for a job"
msgstr "我正在求职"

#: frontend/src/pages/ProfileEvaluator.vue:94
msgid "I am unavailable"
msgstr "我暂不可用"

#: frontend/src/pages/QuizForm.vue:338
msgid "ID"
msgstr "ID"

#. Label of the icon (Data) field in DocType 'LMS Sidebar Item'
#: frontend/src/components/Modals/PageModal.vue:28
#: lms/lms/doctype/lms_sidebar_item/lms_sidebar_item.json
msgid "Icon"
msgstr "图标"

#. Label of the user_category (Check) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Identify User Category"
msgstr "识别用户类别"

#: frontend/src/components/LessonHelp.vue:11
msgid "If Include in Preview is enabled for a lesson then the lesson will also be accessible to non logged in users."
msgstr "启用'包含在预览中'后，课时将对未登录用户开放"

#: frontend/src/components/Quiz.vue:46
msgid "If you answer incorrectly, {0} {1} will be deducted from your score for each incorrect answer."
msgstr ""

#: lms/templates/emails/mentor_request_creation_email.html:5
msgid "If you are not any more interested to mentor the course"
msgstr "若您不再希望担任课程导师"

#: frontend/src/components/Quiz.vue:23
msgid "If you fail to do so, the quiz will be automatically submitted when the timer ends."
msgstr "若未完成，计时结束后测验将自动提交"

#: lms/templates/emails/payment_reminder.html:19
msgid "If you have any questions or need assistance, feel free to reach out to our support team."
msgstr "如有疑问或需协助，请联系支持团队"

#: lms/templates/emails/batch_confirmation.html:29
#: lms/templates/emails/batch_start_reminder.html:27
#: lms/templates/emails/live_class_reminder.html:24
msgid "If you have any questions or require assistance, feel free to contact us."
msgstr "如需帮助，请随时联系我们"

#. Description of the 'Amount (USD)' (Currency) field in DocType 'LMS Batch'
#. Description of the 'Amount (USD)' (Currency) field in DocType 'LMS Course'
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_course/lms_course.json
msgid "If you set an amount here, then the USD equivalent setting will not get applied."
msgstr "此处设置金额后将不再应用美元等值换算"

#: lms/lms/doctype/lms_quiz/lms_quiz.py:66
msgid "If you want open ended questions then make sure each question in the quiz is of open ended type."
msgstr "若需开放性问题，请确保测验中所有题目均为开放型"

#. Option for the 'File Type' (Select) field in DocType 'Course Lesson'
#. Label of the image (Code) field in DocType 'Exercise Latest Submission'
#. Label of the image (Code) field in DocType 'Exercise Submission'
#. Option for the 'Type' (Select) field in DocType 'LMS Assignment'
#. Option for the 'Type' (Select) field in DocType 'LMS Assignment Submission'
#. Label of the image (Attach Image) field in DocType 'LMS Badge'
#. Label of the image (Code) field in DocType 'LMS Exercise'
#: lms/lms/doctype/course_lesson/course_lesson.json
#: lms/lms/doctype/exercise_latest_submission/exercise_latest_submission.json
#: lms/lms/doctype/exercise_submission/exercise_submission.json
#: lms/lms/doctype/lms_assignment/lms_assignment.json
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
#: lms/lms/doctype/lms_badge/lms_badge.json
#: lms/lms/doctype/lms_exercise/lms_exercise.json
msgid "Image"
msgstr "图片"

#: frontend/src/components/Modals/EditCoverImage.vue:58
#: frontend/src/components/UnsplashImageBrowser.vue:52
msgid "Image search powered by"
msgstr "图片搜索由{0}提供"

#: lms/lms/doctype/lms_quiz/lms_quiz.py:232
msgid "Image: Corrupted Data Stream"
msgstr "图片：数据流损坏"

#. Option for the 'Stage' (Select) field in DocType 'LMS Batch Old'
#. Option for the 'Status' (Select) field in DocType 'LMS Certificate
#. Evaluation'
#. Option for the 'Status' (Select) field in DocType 'LMS Course'
#: frontend/src/components/Modals/Event.vue:358
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
#: lms/lms/doctype/lms_course/lms_course.json
msgid "In Progress"
msgstr "进行中"

#. Option for the 'Status' (Select) field in DocType 'LMS Batch Old'
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
msgid "Inactive"
msgstr "未激活"

#. Label of the include_in_preview (Check) field in DocType 'Course Lesson'
#: lms/lms/doctype/course_lesson/course_lesson.json
msgid "Include In Preview"
msgstr "包含在预览中"

#. Option for the 'Status' (Select) field in DocType 'LMS Course Progress'
#: lms/lms/doctype/lms_course_progress/lms_course_progress.json
msgid "Incomplete"
msgstr "未完成"

#. Option for the 'Status' (Select) field in DocType 'Exercise Latest
#. Submission'
#. Option for the 'Status' (Select) field in DocType 'Exercise Submission'
#: frontend/src/components/Quiz.vue:194
#: lms/lms/doctype/exercise_latest_submission/exercise_latest_submission.json
#: lms/lms/doctype/exercise_submission/exercise_submission.json
msgid "Incorrect"
msgstr "错误"

#. Label of the index_ (Int) field in DocType 'LMS Exercise'
#: lms/lms/doctype/lms_exercise/lms_exercise.json
msgid "Index"
msgstr "索引"

#. Label of the index_label (Data) field in DocType 'LMS Exercise'
#: lms/lms/doctype/lms_exercise/lms_exercise.json
msgid "Index Label"
msgstr "索引标签"

#. Option for the 'Collaboration Preference' (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Individual Work"
msgstr "个人作业"

#. Name of a DocType
#. Label of the industry (Data) field in DocType 'Industry'
#. Label of the industry (Link) field in DocType 'Preferred Industry'
#: lms/lms/doctype/industry/industry.json
#: lms/lms/doctype/preferred_industry/preferred_industry.json
msgid "Industry"
msgstr "行业"

#. Label of the input (Data) field in DocType 'LMS Test Case'
#. Label of the input (Data) field in DocType 'LMS Test Case Submission'
#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseSubmission.vue:113
#: lms/lms/doctype/lms_test_case/lms_test_case.json
#: lms/lms/doctype/lms_test_case_submission/lms_test_case_submission.json
msgid "Input"
msgstr ""

#. Label of the institution_name (Data) field in DocType 'Education Detail'
#: lms/lms/doctype/education_detail/education_detail.json
msgid "Institution Name"
msgstr "机构名称"

#. Label of the instructor (Link) field in DocType 'Cohort'
#. Label of the instructor (Link) field in DocType 'Course Instructor'
#: lms/lms/doctype/cohort/cohort.json
#: lms/lms/doctype/course_instructor/course_instructor.json
msgid "Instructor"
msgstr "讲师"

#. Label of the instructor_content (Text) field in DocType 'Course Lesson'
#: lms/lms/doctype/course_lesson/course_lesson.json
msgid "Instructor Content"
msgstr "讲师内容"

#. Label of the instructor_notes (Markdown Editor) field in DocType 'Course
#. Lesson'
#: frontend/src/pages/Lesson.vue:184 frontend/src/pages/LessonForm.vue:42
#: lms/lms/doctype/course_lesson/course_lesson.json
msgid "Instructor Notes"
msgstr "讲师备注"

#. Label of the instructors (Table MultiSelect) field in DocType 'LMS Batch'
#. Label of the instructors (Rating) field in DocType 'LMS Batch Feedback'
#. Label of the instructors (Table MultiSelect) field in DocType 'LMS Course'
#: frontend/src/pages/BatchForm.vue:27 frontend/src/pages/CourseForm.vue:44
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_batch_feedback/lms_batch_feedback.json
#: lms/lms/doctype/lms_course/lms_course.json
msgid "Instructors"
msgstr "讲师"

#: lms/templates/assignment.html:17
msgid "Instructors Comments"
msgstr "讲师评语"

#. Label of a Link in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "Interest"
msgstr "兴趣"

#: frontend/src/components/AppSidebar.vue:556
#: frontend/src/components/AppSidebar.vue:559
msgid "Introduction"
msgstr "简介"

#: lms/lms/doctype/invite_request/invite_request.py:83
msgid "Invalid Invite Code."
msgstr "无效邀请码"

#: lms/lms/doctype/course_lesson/course_lesson.py:20
msgid "Invalid Quiz ID"
msgstr "无效的测验ID"

#: lms/lms/doctype/course_lesson/course_lesson.py:34
msgid "Invalid Quiz ID in content"
msgstr "内容中存在无效测验ID"

#. Label of the invite_code (Data) field in DocType 'Cohort Subgroup'
#. Label of the invite_code (Data) field in DocType 'Invite Request'
#: lms/lms/doctype/cohort_subgroup/cohort_subgroup.json
#: lms/lms/doctype/invite_request/invite_request.json
msgid "Invite Code"
msgstr "邀请码"

#. Label of the invite_email (Data) field in DocType 'Invite Request'
#: lms/lms/doctype/invite_request/invite_request.json
msgid "Invite Email"
msgstr "邀请邮件"

#. Option for the 'Membership' (Select) field in DocType 'LMS Batch Old'
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
msgid "Invite Only"
msgstr "仅限邀请"

#. Name of a DocType
#: lms/lms/doctype/invite_request/invite_request.json
msgid "Invite Request"
msgstr "邀请请求"

#: frontend/src/components/AppSidebar.vue:490
msgid "Invite your team and students"
msgstr "邀请团队成员及学员"

#. Label of the is_correct (Check) field in DocType 'LMS Option'
#. Label of the is_correct_1 (Check) field in DocType 'LMS Question'
#. Label of the is_correct_2 (Check) field in DocType 'LMS Question'
#. Label of the is_correct_3 (Check) field in DocType 'LMS Question'
#. Label of the is_correct_4 (Check) field in DocType 'LMS Question'
#. Label of the is_correct (Check) field in DocType 'LMS Quiz Result'
#: lms/lms/doctype/lms_option/lms_option.json
#: lms/lms/doctype/lms_question/lms_question.json
#: lms/lms/doctype/lms_quiz_result/lms_quiz_result.json
msgid "Is Correct"
msgstr "是否正确"

#. Label of the is_scorm_package (Check) field in DocType 'Course Chapter'
#. Label of the is_scorm_package (Check) field in DocType 'Course Lesson'
#: lms/lms/doctype/course_chapter/course_chapter.json
#: lms/lms/doctype/course_lesson/course_lesson.json
msgid "Is SCORM Package"
msgstr "是否为SCORM包"

#. Label of the issue_date (Date) field in DocType 'Certification'
#. Label of the issue_date (Date) field in DocType 'LMS Certificate'
#: frontend/src/components/Modals/BulkCertificates.vue:28
#: frontend/src/components/Modals/Event.vue:121
#: lms/lms/doctype/certification/certification.json
#: lms/lms/doctype/lms_certificate/lms_certificate.json
msgid "Issue Date"
msgstr "签发日期"

#: frontend/src/components/AppSidebar.vue:592
msgid "Issue a Certificate"
msgstr "颁发证书"

#. Label of the issued_on (Date) field in DocType 'LMS Badge Assignment'
#: frontend/src/pages/CourseCertification.vue:27
#: lms/lms/doctype/lms_badge_assignment/lms_badge_assignment.json
msgid "Issued On"
msgstr "签发日期"

#: frontend/src/pages/ProfileAbout.vue:56
#: frontend/src/pages/ProfileCertificates.vue:17
#: lms/templates/certificates_section.html:11
msgid "Issued on"
msgstr "签发于"

#. Label of the items_in_sidebar_section (Section Break) field in DocType 'LMS
#. Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Items in Sidebar"
msgstr "侧边栏项目"

#: frontend/src/pages/ProgramForm.vue:277
msgid "Items removed successfully"
msgstr "项目移除成功"

#: lms/templates/signup-form.html:6
msgid "Jane Doe"
msgstr "示例用户"

#. Option for the 'Language' (Select) field in DocType 'LMS Programming
#. Exercise'
#: lms/lms/doctype/lms_programming_exercise/lms_programming_exercise.json
msgid "JavaScript"
msgstr "JavaScript"

#. Label of the job (Link) field in DocType 'LMS Job Application'
#: lms/job/doctype/lms_job_application/lms_job_application.json
msgid "Job"
msgstr "职位"

#. Label of the subtitle (Data) field in DocType 'Job Settings'
#: lms/job/doctype/job_settings/job_settings.json
msgid "Job Board Subtitle"
msgstr "招聘看板副标题"

#. Label of the title (Data) field in DocType 'Job Settings'
#: lms/job/doctype/job_settings/job_settings.json
msgid "Job Board Title"
msgstr "招聘看板标题"

#: frontend/src/pages/JobForm.vue:14
msgid "Job Details"
msgstr "职位详情"

#: lms/www/lms.py:176
msgid "Job Openings"
msgstr "职位空缺"

#. Name of a DocType
#: lms/job/doctype/job_opportunity/job_opportunity.json
msgid "Job Opportunity"
msgstr "工作机会"

#. Name of a DocType
#: lms/job/doctype/job_settings/job_settings.json
msgid "Job Settings"
msgstr "职位设置"

#. Label of the job_title (Data) field in DocType 'Job Opportunity'
#. Label of the job_title (Data) field in DocType 'LMS Job Application'
#: lms/job/doctype/job_opportunity/job_opportunity.json
#: lms/job/doctype/lms_job_application/lms_job_application.json
msgid "Job Title"
msgstr "职位名称"

#. Label of the jobs (Check) field in DocType 'LMS Settings'
#: frontend/src/pages/JobDetail.vue:10 frontend/src/pages/Jobs.vue:8
#: frontend/src/pages/Jobs.vue:185
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Jobs"
msgstr "职位"

#: frontend/src/components/LiveClass.vue:78
#: lms/templates/upcoming_evals.html:15
msgid "Join"
msgstr "加入"

#: frontend/src/components/UpcomingEvaluations.vue:90
msgid "Join Call"
msgstr "加入通话"

#: frontend/src/components/Modals/Event.vue:74
msgid "Join Meeting"
msgstr "加入会议"

#. Label of the join_url (Small Text) field in DocType 'LMS Live Class'
#: lms/lms/doctype/lms_live_class/lms_live_class.json
msgid "Join URL"
msgstr "加入链接"

#. Label of the joined_at (Datetime) field in DocType 'LMS Live Class
#. Participant'
#: lms/lms/doctype/lms_live_class_participant/lms_live_class_participant.json
msgid "Joined At"
msgstr ""

#: frontend/src/components/Modals/LiveClassAttendance.vue:18
msgid "Joined at"
msgstr ""

#. Name of a Workspace
#: lms/lms/workspace/lms/lms.json
msgid "LMS"
msgstr "学习管理系统"

#. Name of a DocType
#: lms/lms/doctype/lms_assessment/lms_assessment.json
msgid "LMS Assessment"
msgstr "学习管理系统考核"

#. Name of a DocType
#: lms/lms/doctype/lms_assignment/lms_assignment.json
msgid "LMS Assignment"
msgstr "学习管理系统作业"

#. Name of a DocType
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
msgid "LMS Assignment Submission"
msgstr "学习管理系统作业提交"

#. Name of a DocType
#: lms/lms/doctype/lms_badge/lms_badge.json
msgid "LMS Badge"
msgstr "学习管理系统徽章"

#. Name of a DocType
#: lms/lms/doctype/lms_badge_assignment/lms_badge_assignment.json
msgid "LMS Badge Assignment"
msgstr "学习管理系统徽章分配"

#. Name of a DocType
#. Option for the 'Payment for Document Type' (Select) field in DocType 'LMS
#. Payment'
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_payment/lms_payment.json
msgid "LMS Batch"
msgstr "学习管理系统批次"

#. Name of a DocType
#: lms/lms/doctype/lms_batch_enrollment/lms_batch_enrollment.json
msgid "LMS Batch Enrollment"
msgstr "学习管理系统批次注册"

#. Name of a DocType
#: lms/lms/doctype/lms_batch_feedback/lms_batch_feedback.json
msgid "LMS Batch Feedback"
msgstr "学习管理系统批次反馈"

#. Name of a DocType
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
msgid "LMS Batch Old"
msgstr "学习管理系统旧批次"

#. Name of a DocType
#: lms/lms/doctype/lms_batch_timetable/lms_batch_timetable.json
msgid "LMS Batch Timetable"
msgstr "学习管理系统批次课表"

#. Name of a DocType
#: lms/lms/doctype/lms_category/lms_category.json
msgid "LMS Category"
msgstr "学习管理系统分类"

#. Name of a DocType
#: lms/lms/doctype/lms_certificate/lms_certificate.json
msgid "LMS Certificate"
msgstr "学习管理系统证书"

#. Name of a DocType
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
msgid "LMS Certificate Evaluation"
msgstr "学习管理系统证书评估"

#. Name of a DocType
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
msgid "LMS Certificate Request"
msgstr "学习管理系统证书申请"

#. Name of a DocType
#. Option for the 'Payment for Document Type' (Select) field in DocType 'LMS
#. Payment'
#: lms/lms/doctype/lms_course/lms_course.json
#: lms/lms/doctype/lms_payment/lms_payment.json
msgid "LMS Course"
msgstr "学习管理系统课程"

#. Name of a DocType
#: lms/lms/doctype/lms_course_interest/lms_course_interest.json
msgid "LMS Course Interest"
msgstr "学习管理系统课程兴趣"

#. Name of a DocType
#: lms/lms/doctype/lms_course_mentor_mapping/lms_course_mentor_mapping.json
msgid "LMS Course Mentor Mapping"
msgstr "学习管理系统课程导师映射"

#. Name of a DocType
#: lms/lms/doctype/lms_course_progress/lms_course_progress.json
msgid "LMS Course Progress"
msgstr "学习管理系统课程进度"

#. Name of a DocType
#: lms/lms/doctype/lms_course_review/lms_course_review.json
msgid "LMS Course Review"
msgstr "学习管理系统课程评价"

#. Name of a DocType
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
msgid "LMS Enrollment"
msgstr "学习管理系统注册"

#. Name of a DocType
#: lms/lms/doctype/lms_exercise/lms_exercise.json
msgid "LMS Exercise"
msgstr "学习管理系统练习"

#. Name of a DocType
#: lms/job/doctype/lms_job_application/lms_job_application.json
msgid "LMS Job Application"
msgstr "学习管理系统职位申请"

#. Name of a DocType
#: lms/lms/doctype/lms_live_class/lms_live_class.json
msgid "LMS Live Class"
msgstr "学习管理系统直播课程"

#. Name of a DocType
#: lms/lms/doctype/lms_live_class_participant/lms_live_class_participant.json
msgid "LMS Live Class Participant"
msgstr ""

#. Name of a DocType
#: lms/lms/doctype/lms_mentor_request/lms_mentor_request.json
msgid "LMS Mentor Request"
msgstr "学习管理系统导师请求"

#. Name of a DocType
#: lms/lms/doctype/lms_option/lms_option.json
msgid "LMS Option"
msgstr "学习管理系统选项"

#. Name of a DocType
#: lms/lms/doctype/lms_payment/lms_payment.json
msgid "LMS Payment"
msgstr "学习管理系统支付"

#. Name of a DocType
#: lms/lms/doctype/lms_program/lms_program.json
msgid "LMS Program"
msgstr "学习管理系统项目"

#. Name of a DocType
#: lms/lms/doctype/lms_program_course/lms_program_course.json
msgid "LMS Program Course"
msgstr "学习管理系统项目课程"

#. Name of a DocType
#: lms/lms/doctype/lms_program_member/lms_program_member.json
msgid "LMS Program Member"
msgstr "学习管理系统项目成员"

#. Name of a DocType
#: lms/lms/doctype/lms_programming_exercise/lms_programming_exercise.json
msgid "LMS Programming Exercise"
msgstr ""

#. Name of a DocType
#: lms/lms/doctype/lms_programming_exercise_submission/lms_programming_exercise_submission.json
msgid "LMS Programming Exercise Submission"
msgstr ""

#. Name of a DocType
#: lms/lms/doctype/lms_question/lms_question.json
msgid "LMS Question"
msgstr "学习管理系统试题"

#. Name of a DocType
#: lms/lms/doctype/lms_quiz/lms_quiz.json
msgid "LMS Quiz"
msgstr "学习管理系统测验"

#. Name of a DocType
#: lms/lms/doctype/lms_quiz_question/lms_quiz_question.json
msgid "LMS Quiz Question"
msgstr "学习管理系统测验题目"

#. Name of a DocType
#: lms/lms/doctype/lms_quiz_result/lms_quiz_result.json
msgid "LMS Quiz Result"
msgstr "学习管理系统测验结果"

#. Name of a DocType
#: lms/lms/doctype/lms_quiz_submission/lms_quiz_submission.json
msgid "LMS Quiz Submission"
msgstr "学习管理系统测验提交"

#. Name of a DocType
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "LMS Settings"
msgstr "学习管理系统设置"

#. Name of a DocType
#: lms/lms/doctype/lms_sidebar_item/lms_sidebar_item.json
msgid "LMS Sidebar Item"
msgstr "学习管理系统侧边栏项目"

#. Name of a DocType
#: lms/lms/doctype/lms_source/lms_source.json
msgid "LMS Source"
msgstr "学习管理系统来源"

#. Name of a role
#: lms/job/doctype/job_opportunity/job_opportunity.json
#: lms/job/doctype/lms_job_application/lms_job_application.json
#: lms/lms/doctype/cohort_join_request/cohort_join_request.json
#: lms/lms/doctype/course_chapter/course_chapter.json
#: lms/lms/doctype/function/function.json
#: lms/lms/doctype/industry/industry.json
#: lms/lms/doctype/lms_assignment/lms_assignment.json
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
#: lms/lms/doctype/lms_badge_assignment/lms_badge_assignment.json
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_batch_enrollment/lms_batch_enrollment.json
#: lms/lms/doctype/lms_batch_feedback/lms_batch_feedback.json
#: lms/lms/doctype/lms_certificate/lms_certificate.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
#: lms/lms/doctype/lms_course_progress/lms_course_progress.json
#: lms/lms/doctype/lms_course_review/lms_course_review.json
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
#: lms/lms/doctype/lms_live_class/lms_live_class.json
#: lms/lms/doctype/lms_programming_exercise/lms_programming_exercise.json
#: lms/lms/doctype/lms_programming_exercise_submission/lms_programming_exercise_submission.json
#: lms/lms/doctype/lms_quiz/lms_quiz.json
#: lms/lms/doctype/lms_quiz_submission/lms_quiz_submission.json
#: lms/lms/doctype/lms_settings/lms_settings.json
#: lms/lms/doctype/lms_source/lms_source.json
#: lms/lms/doctype/lms_video_watch_duration/lms_video_watch_duration.json
#: lms/lms/doctype/user_skill/user_skill.json
msgid "LMS Student"
msgstr "学习管理系统学员"

#. Name of a DocType
#: lms/lms/doctype/lms_test_case/lms_test_case.json
msgid "LMS Test Case"
msgstr ""

#. Name of a DocType
#: lms/lms/doctype/lms_test_case_submission/lms_test_case_submission.json
msgid "LMS Test Case Submission"
msgstr ""

#. Name of a DocType
#: lms/lms/doctype/lms_timetable_legend/lms_timetable_legend.json
msgid "LMS Timetable Legend"
msgstr "学习管理系统课表图例"

#. Name of a DocType
#: lms/lms/doctype/lms_timetable_template/lms_timetable_template.json
msgid "LMS Timetable Template"
msgstr "学习管理系统课表模板"

#. Name of a DocType
#: lms/lms/doctype/lms_video_watch_duration/lms_video_watch_duration.json
msgid "LMS Video Watch Duration"
msgstr ""

#. Name of a DocType
#: lms/lms/doctype/lms_zoom_settings/lms_zoom_settings.json
msgid "LMS Zoom Settings"
msgstr ""

#. Label of the label (Data) field in DocType 'LMS Timetable Legend'
#: lms/lms/doctype/lms_timetable_legend/lms_timetable_legend.json
msgid "Label"
msgstr "标签"

#. Label of the language (Select) field in DocType 'LMS Programming Exercise'
#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseForm.vue:22
#: lms/lms/doctype/lms_programming_exercise/lms_programming_exercise.json
msgid "Language"
msgstr "语言"

#: frontend/src/components/Modals/EditProfile.vue:64
msgid "Last Name"
msgstr "姓氏"

#. Label of the latest_submission (Link) field in DocType 'Exercise Latest
#. Submission'
#: lms/lms/doctype/exercise_latest_submission/exercise_latest_submission.json
msgid "Latest Submission"
msgstr "最新提交"

#. Label of the launch_file (Code) field in DocType 'Course Chapter'
#: lms/lms/doctype/course_chapter/course_chapter.json
msgid "Launch File"
msgstr "启动文件"

#. Label of the left_at (Datetime) field in DocType 'LMS Live Class
#. Participant'
#: lms/lms/doctype/lms_live_class_participant/lms_live_class_participant.json
msgid "Left At"
msgstr ""

#: frontend/src/components/Modals/LiveClassAttendance.vue:21
msgid "Left at"
msgstr ""

#. Label of the lesson (Link) field in DocType 'Exercise Latest Submission'
#. Label of the lesson (Link) field in DocType 'Exercise Submission'
#. Label of the lesson (Link) field in DocType 'Lesson Reference'
#. Label of the lesson (Link) field in DocType 'LMS Assignment Submission'
#. Label of the lesson (Link) field in DocType 'LMS Course Progress'
#. Label of the lesson (Link) field in DocType 'LMS Exercise'
#. Label of the lesson (Link) field in DocType 'LMS Quiz'
#. Label of the lesson (Link) field in DocType 'LMS Video Watch Duration'
#. Label of the lesson (Link) field in DocType 'Scheduled Flow'
#. Label of a Link in the LMS Workspace
#: lms/lms/doctype/exercise_latest_submission/exercise_latest_submission.json
#: lms/lms/doctype/exercise_submission/exercise_submission.json
#: lms/lms/doctype/lesson_reference/lesson_reference.json
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
#: lms/lms/doctype/lms_course_progress/lms_course_progress.json
#: lms/lms/doctype/lms_exercise/lms_exercise.json
#: lms/lms/doctype/lms_quiz/lms_quiz.json
#: lms/lms/doctype/lms_video_watch_duration/lms_video_watch_duration.json
#: lms/lms/doctype/scheduled_flow/scheduled_flow.json
#: lms/lms/workspace/lms/lms.json
msgid "Lesson"
msgstr "课时"

#. Name of a DocType
#: lms/lms/doctype/lesson_reference/lesson_reference.json
msgid "Lesson Reference"
msgstr "课时引用"

#. Label of the lesson_title (Data) field in DocType 'Scheduled Flow'
#: lms/lms/doctype/scheduled_flow/scheduled_flow.json
msgid "Lesson Title"
msgstr "课时标题"

#: frontend/src/pages/LessonForm.vue:426
msgid "Lesson created successfully"
msgstr "课时创建成功。"

#: frontend/src/components/CourseOutline.vue:226
msgid "Lesson deleted successfully"
msgstr "课时删除成功。"

#: frontend/src/components/CourseOutline.vue:241
msgid "Lesson moved successfully"
msgstr "课时移动成功。"

#: frontend/src/pages/LessonForm.vue:450
msgid "Lesson updated successfully"
msgstr "课时更新成功。"

#. Label of the lessons (Table) field in DocType 'Course Chapter'
#. Group in Course Chapter's connections
#. Label of the lessons (Int) field in DocType 'LMS Course'
#: frontend/src/components/CourseCard.vue:37
#: frontend/src/components/CourseCardOverlay.vue:131
#: lms/lms/doctype/course_chapter/course_chapter.json
#: lms/lms/doctype/lms_course/lms_course.json
msgid "Lessons"
msgstr "课时"

#: lms/lms/web_template/lms_statistics/lms_statistics.html:14
#: lms/templates/statistics.html:36
msgid "Lessons Completed"
msgstr "已完成课时"

#: lms/templates/onboarding_header.html:11
msgid "Lets start setting up your content on the LMS so that you can reclaim time and focus on growth."
msgstr "立即配置学习管理系统内容，节省时间专注发展"

#. Option for the 'Grade Type' (Select) field in DocType 'Education Detail'
#: lms/lms/doctype/education_detail/education_detail.json
msgid "Letter Grade (e.g. A, B-)"
msgstr "字母评分（如A、B-）"

#. Label of the limit_questions_to (Int) field in DocType 'LMS Quiz'
#: frontend/src/pages/QuizForm.vue:110 lms/lms/doctype/lms_quiz/lms_quiz.json
msgid "Limit Questions To"
msgstr "题目限制数量"

#: lms/lms/doctype/lms_quiz/lms_quiz.py:38
msgid "Limit cannot be greater than or equal to the number of questions in the quiz."
msgstr "限制数不可大于或等于测验题目总数"

#: frontend/src/pages/ProfileAbout.vue:74
msgid "LinkedIn"
msgstr "领英"

#. Label of the linkedin (Data) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "LinkedIn ID"
msgstr "领英账号"

#. Group in Cohort's connections
#. Group in Cohort Subgroup's connections
#: lms/lms/doctype/cohort/cohort.json
#: lms/lms/doctype/cohort_subgroup/cohort_subgroup.json
msgid "Links"
msgstr "链接"

#. Option for the 'Status' (Select) field in DocType 'Cohort'
#: frontend/src/pages/Courses.vue:307 lms/lms/doctype/cohort/cohort.json
msgid "Live"
msgstr "直播"

#. Label of the live_class (Link) field in DocType 'LMS Live Class Participant'
#. Label of the show_live_class (Check) field in DocType 'LMS Settings'
#: frontend/src/components/LiveClass.vue:14
#: lms/lms/doctype/lms_live_class_participant/lms_live_class_participant.json
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Live Class"
msgstr "直播课程"

#. Label of the livecode_url (Data) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "LiveCode URL"
msgstr "实时编码链接"

#: frontend/src/components/Modals/CourseProgressSummary.vue:87
#: frontend/src/components/Settings/Evaluators.vue:81
#: frontend/src/components/Settings/Members.vue:79
#: frontend/src/pages/Assignments.vue:66 frontend/src/pages/Batches.vue:80
#: frontend/src/pages/CertifiedParticipants.vue:98
#: frontend/src/pages/Courses.vue:75
#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseSubmissions.vue:129
#: frontend/src/pages/ProgrammingExercises/ProgrammingExercises.vue:87
#: frontend/src/pages/QuizSubmissionList.vue:39
#: frontend/src/pages/Quizzes.vue:94
msgid "Load More"
msgstr "加载更多"

#. Option for the 'Auto Recording' (Select) field in DocType 'LMS Live Class'
#: lms/lms/doctype/lms_live_class/lms_live_class.json
msgid "Local"
msgstr "本地"

#. Label of the location (Data) field in DocType 'Education Detail'
#. Label of the location (Data) field in DocType 'Work Experience'
#: lms/lms/doctype/education_detail/education_detail.json
#: lms/lms/doctype/work_experience/work_experience.json
msgid "Location"
msgstr "地点"

#. Label of the location_preference (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Location Preference"
msgstr "地点偏好"

#: frontend/src/components/NoPermission.vue:28
#: frontend/src/components/QuizBlock.vue:9 frontend/src/pages/Batch.vue:196
#: frontend/src/pages/Lesson.vue:59
msgid "Login"
msgstr "登录"

#: frontend/src/components/UserDropdown.vue:174
msgid "Login to Frappe Cloud?"
msgstr "登录Frappe云平台？"

#: frontend/src/pages/JobDetail.vue:63
msgid "Login to apply"
msgstr "登录后申请"

#: lms/templates/emails/payment_reminder.html:23
msgid "Looking forward to seeing you enrolled!"
msgstr "期待您的加入！"

#. Label of the default_home (Check) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Make LMS the default home"
msgstr "将学习管理系统设为默认首页"

#: frontend/src/components/Modals/AnnouncementModal.vue:5
#: frontend/src/pages/Batch.vue:16
msgid "Make an Announcement"
msgstr "发布公告"

#: frontend/src/pages/Billing.vue:123
msgid "Make sure to enter the correct billing name as the same will be used in your invoice."
msgstr "请确保输入正确的开票名称，该名称将用于发票"

#: frontend/src/components/BatchOverlay.vue:69
msgid "Manage Batch"
msgstr "管理批次"

#. Option for the 'Role' (Select) field in DocType 'Cohort Staff'
#: lms/lms/doctype/cohort_staff/cohort_staff.json
msgid "Manager"
msgstr "经理"

#. Option for the 'User Category' (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json lms/templates/signup-form.html:24
msgid "Manager (Sales/Marketing/Customer)"
msgstr "经理（销售/市场/客户）"

#. Label of the manifest_file (Code) field in DocType 'Course Chapter'
#: lms/lms/doctype/course_chapter/course_chapter.json
msgid "Manifest File"
msgstr "清单文件"

#: frontend/src/components/Quiz.vue:120
msgid "Mark"
msgstr "标记"

#: frontend/src/pages/Notifications.vue:12
msgid "Mark all as read"
msgstr "全部标记为已读"

#. Label of the marks (Int) field in DocType 'LMS Quiz Question'
#. Label of the marks (Int) field in DocType 'LMS Quiz Result'
#: frontend/src/components/Modals/Question.vue:40
#: frontend/src/components/Modals/Question.vue:106
#: frontend/src/components/Quiz.vue:120 frontend/src/pages/QuizForm.vue:348
#: frontend/src/pages/QuizSubmission.vue:64
#: lms/lms/doctype/lms_quiz_question/lms_quiz_question.json
#: lms/lms/doctype/lms_quiz_result/lms_quiz_result.json
#: lms/templates/quiz/quiz.html:59
msgid "Marks"
msgstr "分数"

#. Label of the marks_to_cut (Int) field in DocType 'LMS Quiz'
#: lms/lms/doctype/lms_quiz/lms_quiz.json
msgid "Marks To Cut"
msgstr ""

#: lms/lms/doctype/lms_quiz_submission/lms_quiz_submission.py:41
msgid "Marks for question number {0} cannot be greater than the marks allotted for that question."
msgstr "第{0}题的得分不得超过该题总分"

#. Label of the marks_out_of (Int) field in DocType 'LMS Quiz Result'
#: frontend/src/pages/QuizSubmission.vue:67
#: lms/lms/doctype/lms_quiz_result/lms_quiz_result.json
msgid "Marks out of"
msgstr "总分"

#: frontend/src/pages/QuizForm.vue:122
msgid "Marks to Cut"
msgstr ""

#. Label of the max_attempts (Int) field in DocType 'LMS Quiz'
#: frontend/src/pages/Quizzes.vue:249 lms/lms/doctype/lms_quiz/lms_quiz.json
msgid "Max Attempts"
msgstr "最大尝试次数"

#: frontend/src/pages/QuizForm.vue:62
msgid "Maximum Attempts"
msgstr "最大尝试次数"

#. Label of the medium (Select) field in DocType 'LMS Batch'
#: frontend/src/pages/BatchForm.vue:187
#: lms/lms/doctype/lms_batch/lms_batch.json
msgid "Medium"
msgstr "媒介"

#. Label of the medium (Data) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Medium ID"
msgstr "媒介ID"

#: lms/templates/emails/batch_confirmation.html:16
#: lms/templates/emails/batch_start_reminder.html:19
msgid "Medium:"
msgstr "媒介："

#. Label of the meeting_id (Data) field in DocType 'LMS Live Class'
#: lms/lms/doctype/lms_live_class/lms_live_class.json
msgid "Meeting ID"
msgstr ""

#. Label of the member (Link) field in DocType 'Exercise Latest Submission'
#. Label of the member (Link) field in DocType 'Exercise Submission'
#. Label of the member (Link) field in DocType 'LMS Assignment Submission'
#. Label of the member (Link) field in DocType 'LMS Badge Assignment'
#. Label of the member (Link) field in DocType 'LMS Batch Enrollment'
#. Label of the member (Link) field in DocType 'LMS Batch Feedback'
#. Label of the member (Link) field in DocType 'LMS Certificate'
#. Label of the member (Link) field in DocType 'LMS Certificate Evaluation'
#. Label of the member (Link) field in DocType 'LMS Certificate Request'
#. Label of the member (Link) field in DocType 'LMS Course Progress'
#. Label of the member (Link) field in DocType 'LMS Enrollment'
#. Option for the 'Role' (Select) field in DocType 'LMS Enrollment'
#. Label of the member (Link) field in DocType 'LMS Live Class Participant'
#. Label of the member (Link) field in DocType 'LMS Mentor Request'
#. Label of the member (Link) field in DocType 'LMS Payment'
#. Label of the member (Link) field in DocType 'LMS Program Member'
#. Label of the member (Link) field in DocType 'LMS Programming Exercise
#. Submission'
#. Label of the member (Link) field in DocType 'LMS Quiz Submission'
#. Label of the member (Link) field in DocType 'LMS Video Watch Duration'
#. Label of the member (Link) field in DocType 'LMS Zoom Settings'
#: frontend/src/components/Modals/CourseProgressSummary.vue:207
#: frontend/src/components/Modals/LiveClassAttendance.vue:14
#: frontend/src/components/Modals/VideoStatistics.vue:22
#: frontend/src/components/Modals/ZoomAccountModal.vue:42
#: frontend/src/components/Settings/ZoomSettings.vue:178
#: frontend/src/pages/AssignmentSubmissionList.vue:14
#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseSubmissions.vue:268
#: frontend/src/pages/QuizSubmission.vue:31
#: frontend/src/pages/QuizSubmissionList.vue:91
#: lms/lms/doctype/exercise_latest_submission/exercise_latest_submission.json
#: lms/lms/doctype/exercise_submission/exercise_submission.json
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
#: lms/lms/doctype/lms_badge_assignment/lms_badge_assignment.json
#: lms/lms/doctype/lms_batch_enrollment/lms_batch_enrollment.json
#: lms/lms/doctype/lms_batch_feedback/lms_batch_feedback.json
#: lms/lms/doctype/lms_certificate/lms_certificate.json
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
#: lms/lms/doctype/lms_course_progress/lms_course_progress.json
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
#: lms/lms/doctype/lms_live_class_participant/lms_live_class_participant.json
#: lms/lms/doctype/lms_mentor_request/lms_mentor_request.json
#: lms/lms/doctype/lms_payment/lms_payment.json
#: lms/lms/doctype/lms_program_member/lms_program_member.json
#: lms/lms/doctype/lms_programming_exercise_submission/lms_programming_exercise_submission.json
#: lms/lms/doctype/lms_quiz_submission/lms_quiz_submission.json
#: lms/lms/doctype/lms_video_watch_duration/lms_video_watch_duration.json
#: lms/lms/doctype/lms_zoom_settings/lms_zoom_settings.json
#: lms/lms/report/course_progress_summary/course_progress_summary.py:64
msgid "Member"
msgstr "成员"

#. Label of the member_cohort (Link) field in DocType 'Exercise Latest
#. Submission'
#: lms/lms/doctype/exercise_latest_submission/exercise_latest_submission.json
msgid "Member Cohort"
msgstr "成员群组"

#. Label of the member_email (Link) field in DocType 'Exercise Latest
#. Submission'
#: lms/lms/doctype/exercise_latest_submission/exercise_latest_submission.json
msgid "Member Email"
msgstr "成员邮箱"

#. Label of the member_image (Attach Image) field in DocType 'LMS Batch
#. Feedback'
#. Label of the member_image (Attach Image) field in DocType 'LMS Enrollment'
#. Label of the member_image (Attach Image) field in DocType 'LMS Live Class
#. Participant'
#. Label of the member_image (Attach) field in DocType 'LMS Programming
#. Exercise Submission'
#. Label of the member_image (Attach Image) field in DocType 'LMS Video Watch
#. Duration'
#: lms/lms/doctype/lms_batch_feedback/lms_batch_feedback.json
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
#: lms/lms/doctype/lms_live_class_participant/lms_live_class_participant.json
#: lms/lms/doctype/lms_programming_exercise_submission/lms_programming_exercise_submission.json
#: lms/lms/doctype/lms_video_watch_duration/lms_video_watch_duration.json
msgid "Member Image"
msgstr "成员头像"

#. Label of the member_name (Data) field in DocType 'LMS Assignment Submission'
#. Label of the member_name (Data) field in DocType 'LMS Badge Assignment'
#. Label of the member_name (Data) field in DocType 'LMS Batch Enrollment'
#. Label of the member_name (Data) field in DocType 'LMS Batch Feedback'
#. Label of the member_name (Data) field in DocType 'LMS Certificate'
#. Label of the member_name (Data) field in DocType 'LMS Certificate
#. Evaluation'
#. Label of the member_name (Data) field in DocType 'LMS Certificate Request'
#. Label of the member_name (Data) field in DocType 'LMS Course Progress'
#. Label of the member_name (Data) field in DocType 'LMS Enrollment'
#. Label of the member_name (Data) field in DocType 'LMS Live Class
#. Participant'
#. Label of the member_name (Data) field in DocType 'LMS Mentor Request'
#. Label of the member_name (Data) field in DocType 'LMS Programming Exercise
#. Submission'
#. Label of the member_name (Data) field in DocType 'LMS Quiz Submission'
#. Label of the member_name (Data) field in DocType 'LMS Video Watch Duration'
#. Label of the member_name (Data) field in DocType 'LMS Zoom Settings'
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
#: lms/lms/doctype/lms_badge_assignment/lms_badge_assignment.json
#: lms/lms/doctype/lms_batch_enrollment/lms_batch_enrollment.json
#: lms/lms/doctype/lms_batch_feedback/lms_batch_feedback.json
#: lms/lms/doctype/lms_certificate/lms_certificate.json
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
#: lms/lms/doctype/lms_course_progress/lms_course_progress.json
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
#: lms/lms/doctype/lms_live_class_participant/lms_live_class_participant.json
#: lms/lms/doctype/lms_mentor_request/lms_mentor_request.json
#: lms/lms/doctype/lms_programming_exercise_submission/lms_programming_exercise_submission.json
#: lms/lms/doctype/lms_quiz_submission/lms_quiz_submission.json
#: lms/lms/doctype/lms_video_watch_duration/lms_video_watch_duration.json
#: lms/lms/doctype/lms_zoom_settings/lms_zoom_settings.json
#: lms/lms/report/course_progress_summary/course_progress_summary.py:71
msgid "Member Name"
msgstr "成员姓名"

#. Label of the member_subgroup (Link) field in DocType 'Exercise Latest
#. Submission'
#: lms/lms/doctype/exercise_latest_submission/exercise_latest_submission.json
msgid "Member Subgroup"
msgstr "成员子组"

#. Label of the member_type (Select) field in DocType 'LMS Enrollment'
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
msgid "Member Type"
msgstr "成员类型"

#. Label of the member_username (Data) field in DocType 'LMS Batch Enrollment'
#. Label of the member_username (Data) field in DocType 'LMS Enrollment'
#. Label of the member_username (Data) field in DocType 'LMS Live Class
#. Participant'
#. Label of the member_username (Data) field in DocType 'LMS Video Watch
#. Duration'
#: lms/lms/doctype/lms_batch_enrollment/lms_batch_enrollment.json
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
#: lms/lms/doctype/lms_live_class_participant/lms_live_class_participant.json
#: lms/lms/doctype/lms_video_watch_duration/lms_video_watch_duration.json
msgid "Member Username"
msgstr "成员用户名"

#: frontend/src/pages/ProgramForm.vue:256
msgid "Member added to program"
msgstr "成员已添加至项目"

#: lms/lms/doctype/lms_batch_enrollment/lms_batch_enrollment.py:25
msgid "Member already enrolled in this batch"
msgstr "该成员已注册本批次"

#: lms/lms/doctype/lms_program/lms_program.py:29
msgid "Member {0} has already been added to this batch."
msgstr "成员{0}已添加至本批次"

#. Group in LMS Batch Old's connections
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
msgid "Members"
msgstr "成员"

#. Label of the membership (Select) field in DocType 'LMS Batch Old'
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
msgid "Membership"
msgstr "成员资格"

#. Option for the 'Required Role' (Select) field in DocType 'Cohort Web Page'
#. Label of the mentor (Link) field in DocType 'LMS Course Mentor Mapping'
#. Option for the 'Member Type' (Select) field in DocType 'LMS Enrollment'
#: lms/lms/doctype/cohort_web_page/cohort_web_page.json
#: lms/lms/doctype/lms_course_mentor_mapping/lms_course_mentor_mapping.json
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
msgid "Mentor"
msgstr "导师"

#. Label of the mentor_name (Data) field in DocType 'LMS Course Mentor Mapping'
#: lms/lms/doctype/lms_course_mentor_mapping/lms_course_mentor_mapping.json
msgid "Mentor Name"
msgstr "导师姓名"

#. Label of the mentor_request_section (Section Break) field in DocType 'LMS
#. Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Mentor Request"
msgstr "导师请求"

#. Label of the mentor_request_creation (Link) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
#: lms/patches/create_mentor_request_email_templates.py:11
#: lms/patches/create_mentor_request_email_templates.py:18
#: lms/patches/create_mentor_request_email_templates.py:28
msgid "Mentor Request Creation Template"
msgstr "导师请求创建模板"

#. Label of the mentor_request_status_update (Link) field in DocType 'LMS
#. Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
#: lms/patches/create_mentor_request_email_templates.py:31
#: lms/patches/create_mentor_request_email_templates.py:38
#: lms/patches/create_mentor_request_email_templates.py:48
msgid "Mentor Request Status Update Template"
msgstr "导师请求状态更新模板"

#. Label of the meta_description (Small Text) field in DocType 'LMS Settings'
#: frontend/src/pages/BatchForm.vue:284 frontend/src/pages/CourseForm.vue:274
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Meta Description"
msgstr "元描述"

#. Label of the meta_image (Attach Image) field in DocType 'LMS Batch'
#. Label of the meta_image (Attach Image) field in DocType 'LMS Settings'
#: frontend/src/pages/BatchForm.vue:200
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Meta Image"
msgstr "元图片"

#. Label of the meta_keywords (Small Text) field in DocType 'LMS Settings'
#: frontend/src/pages/BatchForm.vue:290 frontend/src/pages/CourseForm.vue:280
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Meta Keywords"
msgstr "元关键词"

#: frontend/src/pages/BatchForm.vue:279 frontend/src/pages/CourseForm.vue:269
msgid "Meta Tags"
msgstr "元标签"

#: lms/lms/api.py:1459
msgid "Meta tags should be a list."
msgstr ""

#. Label of the milestone (Check) field in DocType 'LMS Batch Timetable'
#: lms/lms/doctype/lms_batch_timetable/lms_batch_timetable.json
msgid "Milestone"
msgstr "里程碑"

#: lms/lms/doctype/lms_question/lms_question.py:48
msgid "Minimum two options are required for multiple choice questions."
msgstr "多选题至少需要两个选项"

#. Name of a role
#: frontend/src/pages/ProfileRoles.vue:20
#: lms/lms/doctype/course_chapter/course_chapter.json
#: lms/lms/doctype/course_evaluator/course_evaluator.json
#: lms/lms/doctype/course_lesson/course_lesson.json
#: lms/lms/doctype/lms_assignment/lms_assignment.json
#: lms/lms/doctype/lms_badge_assignment/lms_badge_assignment.json
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_batch_enrollment/lms_batch_enrollment.json
#: lms/lms/doctype/lms_category/lms_category.json
#: lms/lms/doctype/lms_certificate/lms_certificate.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
#: lms/lms/doctype/lms_course/lms_course.json
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
#: lms/lms/doctype/lms_live_class/lms_live_class.json
#: lms/lms/doctype/lms_program/lms_program.json
#: lms/lms/doctype/lms_programming_exercise/lms_programming_exercise.json
#: lms/lms/doctype/lms_programming_exercise_submission/lms_programming_exercise_submission.json
#: lms/lms/doctype/lms_question/lms_question.json
#: lms/lms/doctype/lms_quiz/lms_quiz.json
#: lms/lms/doctype/lms_settings/lms_settings.json
#: lms/lms/doctype/lms_source/lms_source.json
#: lms/lms/doctype/lms_timetable_template/lms_timetable_template.json
#: lms/lms/doctype/lms_video_watch_duration/lms_video_watch_duration.json
#: lms/lms/doctype/lms_zoom_settings/lms_zoom_settings.json
msgid "Moderator"
msgstr "主持人"

#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseSubmissions.vue:286
#: frontend/src/pages/Quizzes.vue:263
msgid "Modified"
msgstr ""

#: lms/lms/doctype/lms_badge/lms_badge.js:40
msgid "Modified By"
msgstr "修改人"

#: lms/lms/api.py:218
msgid "Module Name is incorrect or does not exist."
msgstr "模块名称错误或不存在"

#: lms/lms/api.py:214
msgid "Module is incorrect."
msgstr "模块错误"

#. Option for the 'Day' (Select) field in DocType 'Evaluator Schedule'
#. Option for the 'Day' (Select) field in DocType 'LMS Certificate Request'
#: lms/lms/doctype/evaluator_schedule/evaluator_schedule.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
msgid "Monday"
msgstr "星期一"

#: frontend/src/components/AppSidebar.vue:600
msgid "Monetization"
msgstr "课程变现功能"

#: frontend/src/components/AppSidebar.vue:39
msgid "More"
msgstr "更多"

#. Label of the multiple (Check) field in DocType 'LMS Question'
#: lms/lms/doctype/lms_question/lms_question.json
msgid "Multiple Correct Answers"
msgstr "多正确答案"

#: frontend/src/pages/ProfileEvaluator.vue:4
msgid "My availability"
msgstr "我的可用时间"

#: frontend/src/pages/ProfileEvaluator.vue:127
msgid "My calendar"
msgstr "我的日历"

#: frontend/src/components/Modals/EmailTemplateModal.vue:24
msgid "Name"
msgstr "名称"

#. Option for the 'Event' (Select) field in DocType 'LMS Badge'
#: frontend/src/components/Settings/Categories.vue:27
#: frontend/src/components/Settings/EmailTemplates.vue:17
#: frontend/src/components/Settings/Evaluators.vue:17
#: frontend/src/components/Settings/Members.vue:17
#: frontend/src/components/Settings/ZoomSettings.vue:17
#: frontend/src/pages/Assignments.vue:19 frontend/src/pages/Batches.vue:17
#: frontend/src/pages/Courses.vue:17 frontend/src/pages/Courses.vue:310
#: frontend/src/pages/ProgrammingExercises/ProgrammingExercises.vue:32
#: frontend/src/pages/Programs.vue:14 frontend/src/pages/Quizzes.vue:10
#: lms/lms/doctype/lms_badge/lms_badge.json
msgid "New"
msgstr "新建"

#: lms/www/lms.py:151
msgid "New Batch"
msgstr "新建批次"

#: frontend/src/pages/CourseForm.vue:661 lms/www/lms.py:95
msgid "New Course"
msgstr "新建课程"

#: frontend/src/components/Modals/EmailTemplateModal.vue:7
msgid "New Email Template"
msgstr ""

#: frontend/src/pages/Jobs.vue:23
msgid "New Job"
msgstr "新建职位"

#: lms/job/doctype/lms_job_application/lms_job_application.py:27
msgid "New Job Applicant"
msgstr "新职位申请人"

#: frontend/src/pages/Programs.vue:90
msgid "New Program"
msgstr "新建项目"

#: frontend/src/pages/ProgramForm.vue:133
msgid "New Program Course"
msgstr "新建项目课程"

#: frontend/src/pages/ProgramForm.vue:134
msgid "New Program Member"
msgstr "新项目成员"

#: frontend/src/pages/QuizForm.vue:137
msgid "New Question"
msgstr "新建试题"

#: frontend/src/pages/QuizForm.vue:404 frontend/src/pages/QuizForm.vue:412
msgid "New Quiz"
msgstr "新建测验"

#: lms/www/new-sign-up.html:3
msgid "New Sign Up"
msgstr "新用户注册"

#: frontend/src/components/Modals/ZoomAccountModal.vue:6
msgid "New Zoom Account"
msgstr ""

#: lms/lms/utils.py:609
msgid "New comment in batch {0}"
msgstr "批次{0}的新评论"

#: lms/lms/utils.py:602
msgid "New reply on the topic {0} in course {1}"
msgstr "课程{1}中主题{0}的新回复"

#: frontend/src/components/Discussions.vue:8
#: frontend/src/components/Discussions.vue:63
msgid "New {0}"
msgstr "新建{0}"

#: frontend/src/components/Quiz.vue:237 frontend/src/pages/Lesson.vue:139
msgid "Next"
msgstr "下一步"

#: lms/templates/quiz/quiz.html:125
msgid "Next Question"
msgstr "下一题"

#: frontend/src/components/Assessments.vue:75 lms/templates/assessments.html:58
msgid "No Assessments"
msgstr "暂无考核"

#: lms/templates/notifications.html:26
msgid "No Notifications"
msgstr "无通知"

#: frontend/src/components/Quiz.vue:307
msgid "No Quiz submissions found"
msgstr "未找到测验提交记录"

#: frontend/src/pages/Quizzes.vue:19
msgid "No Quizzes"
msgstr ""

#. Option for the 'Auto Recording' (Select) field in DocType 'LMS Live Class'
#: lms/lms/doctype/lms_live_class/lms_live_class.json
msgid "No Recording"
msgstr "无录制"

#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseSubmissions.vue:13
msgid "No Submissions"
msgstr ""

#: lms/templates/upcoming_evals.html:43
msgid "No Upcoming Evaluations"
msgstr "暂无待评估项"

#: frontend/src/components/Annoucements.vue:24
msgid "No announcements"
msgstr "暂无公告"

#: lms/templates/certificates_section.html:23
msgid "No certificates"
msgstr "无证书"

#: frontend/src/components/BatchCourses.vue:67
msgid "No courses added"
msgstr "未添加任何课程"

#: lms/templates/courses_created.html:14
msgid "No courses created"
msgstr "未创建课程"

#: frontend/src/pages/Programs.vue:81
msgid "No courses in this program"
msgstr "本项目暂无课程"

#: lms/templates/courses_under_review.html:14
msgid "No courses under review"
msgstr "无审核中的课程"

#: frontend/src/components/BatchFeedback.vue:60
msgid "No feedback received yet."
msgstr "尚未收到反馈"

#: frontend/src/pages/ProfileAbout.vue:12
msgid "No introduction"
msgstr "无简介"

#: frontend/src/components/LiveClass.vue:97
msgid "No live classes scheduled"
msgstr "未安排直播课程"

#: frontend/src/pages/QuizForm.vue:188
msgid "No questions added yet"
msgstr ""

#: frontend/src/components/Modals/QuizInVideo.vue:93
msgid "No quizzes added yet."
msgstr ""

#: frontend/src/components/Modals/EvaluationModal.vue:62
msgid "No slots available for this date."
msgstr "该日期无可用时段"

#: frontend/src/components/Modals/AnnouncementModal.vue:90
msgid "No students in this batch"
msgstr "本班级暂无学员"

#: frontend/src/pages/AssignmentSubmissionList.vue:67
msgid "No submissions"
msgstr "无提交记录"

#: frontend/src/components/EmptyState.vue:5 lms/templates/course_list.html:13
msgid "No {0}"
msgstr "无{0}"

#: lms/templates/quiz/quiz.html:147
msgid "No."
msgstr "编号"

#: lms/lms/user.py:29
msgid "Not Allowed"
msgstr "不允许"

#. Option for the 'Status' (Select) field in DocType 'LMS Assignment
#. Submission'
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
msgid "Not Applicable"
msgstr "不适用"

#: lms/templates/assessments.html:48
msgid "Not Attempted"
msgstr "未尝试"

#: lms/lms/widgets/NoPreviewModal.html:6
msgid "Not Available for Preview"
msgstr "不可预览"

#. Option for the 'Status' (Select) field in DocType 'LMS Assignment
#. Submission'
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
msgid "Not Graded"
msgstr "未评分"

#: frontend/src/components/NoPermission.vue:7 frontend/src/pages/Batch.vue:164
msgid "Not Permitted"
msgstr "未授权"

#: frontend/src/components/Assignment.vue:36
#: frontend/src/components/Settings/BrandSettings.vue:10
#: frontend/src/components/Settings/PaymentSettings.vue:9
#: frontend/src/components/Settings/SettingDetails.vue:10
#: frontend/src/pages/QuizForm.vue:8 frontend/src/pages/QuizSubmission.vue:9
msgid "Not Saved"
msgstr "未保存"

#: frontend/src/pages/Notifications.vue:53
msgid "Nothing to see here."
msgstr "暂无内容"

#. Label of the notifications (Check) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Notifications"
msgstr "通知"

#: lms/lms/widgets/NoPreviewModal.html:30
msgid "Notify me when available"
msgstr "可用时通知我"

#: frontend/src/components/BatchStudents.vue:48
msgid "Number of Students"
msgstr ""

#: frontend/src/pages/BatchForm.vue:150
msgid "Number of seats available"
msgstr "可用席位数量"

#. Label of the sb_00 (Section Break) field in DocType 'Zoom Settings'
#: lms/lms/doctype/zoom_settings/zoom_settings.json
msgid "OAuth Client ID"
msgstr "OAuth客户端ID"

#. Option for the 'Location Preference' (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Office close to Home"
msgstr "离家近的办公地点"

#. Option for the 'Medium' (Select) field in DocType 'LMS Batch'
#: lms/lms/doctype/lms_batch/lms_batch.json
msgid "Offline"
msgstr "离线"

#: lms/templates/emails/certification.html:16
msgid "Once again, congratulations on this significant accomplishment."
msgstr "再次祝贺您取得这一重要成就"

#: frontend/src/components/Assignment.vue:60
msgid "Once the moderator grades your submission, you'll find the details here."
msgstr "审核员评分后，您可在此查看详情"

#. Option for the 'Medium' (Select) field in DocType 'LMS Batch'
#: lms/lms/doctype/lms_batch/lms_batch.json
msgid "Online"
msgstr "在线"

#: frontend/src/pages/ProgramForm.vue:157
msgid "Only courses for which self learning is disabled can be added to program."
msgstr "仅允许添加已禁用自主学习的课程至项目"

#: lms/templates/assignment.html:6
msgid "Only files of type {0} will be accepted."
msgstr "仅接受{0}类型文件"

#: frontend/src/pages/CourseForm.vue:597 frontend/src/utils/index.js:503
msgid "Only image file is allowed."
msgstr "仅允许图片文件"

#: frontend/src/components/Modals/ChapterModal.vue:218
msgid "Only zip files are allowed"
msgstr "仅允许zip文件"

#. Option for the 'Status' (Select) field in DocType 'Job Opportunity'
#. Option for the 'Membership' (Select) field in DocType 'LMS Batch Old'
#: lms/job/doctype/job_opportunity/job_opportunity.json
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
msgid "Open"
msgstr "开放"

#: lms/templates/emails/assignment_submission.html:8
msgid "Open Assignment"
msgstr "开放作业"

#: lms/templates/emails/lms_message.html:13
msgid "Open Course"
msgstr "开放课程"

#. Option for the 'Type' (Select) field in DocType 'LMS Question'
#. Option for the 'Type' (Select) field in DocType 'LMS Quiz Question'
#: lms/lms/doctype/lms_question/lms_question.json
#: lms/lms/doctype/lms_quiz_question/lms_quiz_question.json
msgid "Open Ended"
msgstr "开放型问题"

#. Label of the option (Data) field in DocType 'LMS Option'
#: frontend/src/components/Modals/Question.vue:70
#: lms/lms/doctype/lms_option/lms_option.json
msgid "Option"
msgstr "选项"

#. Label of the option_1 (Small Text) field in DocType 'LMS Question'
#: lms/lms/doctype/lms_question/lms_question.json
msgid "Option 1"
msgstr "选项1"

#. Label of the option_2 (Small Text) field in DocType 'LMS Question'
#: lms/lms/doctype/lms_question/lms_question.json
msgid "Option 2"
msgstr "选项2"

#. Label of the option_3 (Small Text) field in DocType 'LMS Question'
#: lms/lms/doctype/lms_question/lms_question.json
msgid "Option 3"
msgstr "选项3"

#. Label of the option_4 (Small Text) field in DocType 'LMS Question'
#: lms/lms/doctype/lms_question/lms_question.json
msgid "Option 4"
msgstr "选项4"

#: frontend/src/components/Modals/Question.vue:56
msgid "Options"
msgstr "选项"

#. Label of the order_id (Data) field in DocType 'LMS Payment'
#: lms/lms/doctype/lms_payment/lms_payment.json
msgid "Order ID"
msgstr "订单ID"

#. Label of the organization (Data) field in DocType 'Certification'
#: lms/lms/doctype/certification/certification.json
msgid "Organization"
msgstr "组织"

#: frontend/src/pages/Billing.vue:32
msgid "Original Amount"
msgstr "原始金额"

#. Option for the 'User Category' (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json lms/templates/signup-form.html:28
msgid "Others"
msgstr "其他"

#. Label of the output (Data) field in DocType 'LMS Test Case Submission'
#: lms/lms/doctype/lms_test_case_submission/lms_test_case_submission.json
msgid "Output"
msgstr "输出"

#: lms/lms/doctype/lms_badge/lms_badge.js:39
msgid "Owner"
msgstr "所有者"

#. Label of the pan (Data) field in DocType 'LMS Payment'
#: lms/lms/doctype/lms_payment/lms_payment.json
msgid "PAN"
msgstr "永久账户号码"

#. Option for the 'File Type' (Select) field in DocType 'Course Lesson'
#. Option for the 'Type' (Select) field in DocType 'LMS Assignment'
#. Option for the 'Type' (Select) field in DocType 'LMS Assignment Submission'
#: lms/lms/doctype/course_lesson/course_lesson.json
#: lms/lms/doctype/lms_assignment/lms_assignment.json
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
msgid "PDF"
msgstr "PDF文件"

#. Label of the pages (Table) field in DocType 'Cohort'
#: lms/lms/doctype/cohort/cohort.json
msgid "Pages"
msgstr "页面"

#. Label of the paid_batch (Check) field in DocType 'LMS Batch'
#: frontend/src/pages/BatchForm.vue:260
#: lms/lms/doctype/lms_batch/lms_batch.json
msgid "Paid Batch"
msgstr "付费批次"

#. Label of the paid_certificate (Check) field in DocType 'LMS Course'
#: frontend/src/pages/CourseForm.vue:237
#: lms/lms/doctype/lms_course/lms_course.json
msgid "Paid Certificate"
msgstr "付费证书"

#: frontend/src/components/CourseCardOverlay.vue:165
msgid "Paid Certificate after Evaluation"
msgstr "评估后付费证书"

#. Label of the paid_course (Check) field in DocType 'LMS Course'
#: frontend/src/pages/CourseForm.vue:227
#: lms/lms/doctype/lms_course/lms_course.json
msgid "Paid Course"
msgstr "付费课程"

#: frontend/src/pages/Billing.vue:115
msgid "Pan Number"
msgstr "PAN号码"

#. Option for the 'Type' (Select) field in DocType 'Job Opportunity'
#: frontend/src/pages/Jobs.vue:177
#: lms/job/doctype/job_opportunity/job_opportunity.json
msgid "Part Time"
msgstr "兼职"

#. Option for the 'Status' (Select) field in DocType 'LMS Course Progress'
#: lms/lms/doctype/lms_course_progress/lms_course_progress.json
msgid "Partially Complete"
msgstr "部分完成"

#. Option for the 'Status' (Select) field in DocType 'LMS Assignment
#. Submission'
#. Option for the 'Status' (Select) field in DocType 'LMS Certificate
#. Evaluation'
#: frontend/src/components/Modals/Event.vue:362
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
msgid "Pass"
msgstr "通过"

#. Option for the 'Status' (Select) field in DocType 'LMS Programming Exercise
#. Submission'
#. Option for the 'Status' (Select) field in DocType 'LMS Test Case Submission'
#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseSubmissions.vue:36
#: lms/lms/doctype/lms_programming_exercise_submission/lms_programming_exercise_submission.json
#: lms/lms/doctype/lms_test_case_submission/lms_test_case_submission.json
msgid "Passed"
msgstr "通过"

#. Label of the passing_percentage (Int) field in DocType 'LMS Quiz'
#. Label of the passing_percentage (Int) field in DocType 'LMS Quiz Submission'
#: frontend/src/pages/QuizForm.vue:78 frontend/src/pages/Quizzes.vue:242
#: lms/lms/doctype/lms_quiz/lms_quiz.json
#: lms/lms/doctype/lms_quiz_submission/lms_quiz_submission.json
msgid "Passing Percentage"
msgstr "通过百分比"

#. Label of the password (Password) field in DocType 'LMS Live Class'
#: lms/lms/doctype/lms_live_class/lms_live_class.json
msgid "Password"
msgstr "密码"

#: frontend/src/pages/CourseForm.vue:197
msgid "Paste the youtube link of a short video introducing the course"
msgstr "粘贴课程简介的YouTube短视频链接"

#. Label of the payment (Link) field in DocType 'LMS Batch Enrollment'
#. Label of the payment (Link) field in DocType 'LMS Enrollment'
#: lms/lms/doctype/lms_batch_enrollment/lms_batch_enrollment.json
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
msgid "Payment"
msgstr "支付"

#. Name of a DocType
#: lms/lms/doctype/payment_country/payment_country.json
msgid "Payment Country"
msgstr "支付国家"

#. Label of the payment_details_section (Section Break) field in DocType 'LMS
#. Payment'
#: lms/lms/doctype/lms_payment/lms_payment.json
msgid "Payment Details"
msgstr "支付详情"

#. Label of the payment_gateway (Data) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Payment Gateway"
msgstr "支付网关"

#. Label of the payment_id (Data) field in DocType 'LMS Payment'
#: lms/lms/doctype/lms_payment/lms_payment.json
msgid "Payment ID"
msgstr "支付ID"

#. Label of the payment_received (Check) field in DocType 'LMS Payment'
#: lms/lms/doctype/lms_payment/lms_payment.json
msgid "Payment Received"
msgstr "已收款"

#. Label of the payment_reminder_template (Link) field in DocType 'LMS
#. Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Payment Reminder Template"
msgstr "支付提醒模板"

#. Label of the payment_settings_tab (Tab Break) field in DocType 'LMS
#. Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Payment Settings"
msgstr "支付设置"

#: frontend/src/pages/Billing.vue:21
msgid "Payment for "
msgstr "支付对象："

#. Label of the payment_for_certificate (Check) field in DocType 'LMS Payment'
#: lms/lms/doctype/lms_payment/lms_payment.json
msgid "Payment for Certificate"
msgstr "证书支付"

#. Label of the payment_for_document (Dynamic Link) field in DocType 'LMS
#. Payment'
#: lms/lms/doctype/lms_payment/lms_payment.json
msgid "Payment for Document"
msgstr "文档支付"

#. Label of the payment_for_document_type (Select) field in DocType 'LMS
#. Payment'
#: lms/lms/doctype/lms_payment/lms_payment.json
msgid "Payment for Document Type"
msgstr "文档类型支付"

#. Label of the payments_app_is_not_installed (HTML) field in DocType 'LMS
#. Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Payments app is not installed"
msgstr "未安装支付应用"

#. Option for the 'Status' (Select) field in DocType 'Cohort Join Request'
#. Option for the 'Status' (Select) field in DocType 'Invite Request'
#. Option for the 'Status' (Select) field in DocType 'LMS Certificate
#. Evaluation'
#. Option for the 'Status' (Select) field in DocType 'LMS Mentor Request'
#: frontend/src/components/Modals/Event.vue:354
#: lms/lms/doctype/cohort_join_request/cohort_join_request.json
#: lms/lms/doctype/invite_request/invite_request.json
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
#: lms/lms/doctype/lms_mentor_request/lms_mentor_request.json
msgid "Pending"
msgstr "待处理"

#. Label of the percentage (Int) field in DocType 'LMS Quiz Submission'
#: frontend/src/pages/QuizSubmission.vue:44
#: frontend/src/pages/QuizSubmissionList.vue:102
#: lms/lms/doctype/lms_quiz_submission/lms_quiz_submission.json
msgid "Percentage"
msgstr "百分比"

#. Option for the 'Grade Type' (Select) field in DocType 'Education Detail'
#: lms/lms/doctype/education_detail/education_detail.json
msgid "Percentage (e.g. 70%)"
msgstr "百分比（如70%）"

#: frontend/src/components/Modals/BatchStudentProgress.vue:44
msgid "Percentage/Status"
msgstr "百分比/状态"

#. Label of the persona_captured (Check) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Persona Captured"
msgstr "用户画像已采集"

#: frontend/src/pages/Billing.vue:99
msgid "Phone Number"
msgstr "电话号码"

#: lms/lms/doctype/lms_settings/lms_settings.py:34
msgid "Please add <a href='{0}'>{1}</a> for <a href='{2}'>{3}</a> to send calendar invites for evaluations."
msgstr "请为<a href='{2}'>{3}</a>添加<a href='{0}'>{1}</a>以发送评估日历邀请"

#: frontend/src/components/LiveClass.vue:8
msgid "Please add a zoom account to the batch to create live classes."
msgstr ""

#: lms/lms/user.py:75
msgid "Please ask your administrator to verify your sign-up"
msgstr "请联系管理员审核您的注册"

#: lms/lms/user.py:73
msgid "Please check your email for verification"
msgstr "请查看邮箱进行验证"

#: lms/templates/emails/community_course_membership.html:7
msgid "Please click on the following button to set your new password"
msgstr "点击下方按钮设置新密码"

#: lms/lms/utils.py:2077 lms/lms/utils.py:2081
msgid "Please complete the previous courses in the program to enroll in this course."
msgstr "请先完成项目前置课程以报名本课程"

#: lms/lms/doctype/lms_batch/lms_batch.py:211
msgid "Please enable the zoom account to use this feature."
msgstr ""

#: frontend/src/components/CourseOutline.vue:328
msgid "Please enroll for this course to view this lesson"
msgstr "请报名本课程以查看此课时"

#: frontend/src/components/Quiz.vue:16
msgid "Please ensure that you complete all the questions in {0} minutes."
msgstr "请确保在{0}分钟内完成所有试题"

#: frontend/src/components/Modals/LiveClassModal.vue:186
msgid "Please enter a title."
msgstr "请输入标题"

#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.py:31
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.py:84
msgid "Please enter a valid URL."
msgstr "请输入有效URL"

#: frontend/src/components/Modals/LiveClassModal.vue:198
msgid "Please enter a valid time in the format HH:mm."
msgstr "请输入HH:mm格式的有效时间"

#: frontend/src/components/Modals/QuizInVideo.vue:181
msgid "Please enter a valid timestamp"
msgstr ""

#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.py:78
msgid "Please enter the URL for assignment submission."
msgstr "请输入作业提交URL"

#: lms/templates/quiz/quiz.js:176
msgid "Please enter your answer"
msgstr "请输入答案"

#: lms/lms/doctype/lms_batch/lms_batch.py:63
msgid "Please install the Payments App to create a paid batch. Refer to the documentation for more details. {0}"
msgstr "请安装支付应用以创建付费班级，详情请参阅文档{0}"

#: lms/lms/doctype/lms_course/lms_course.py:55
msgid "Please install the Payments App to create a paid course. Refer to the documentation for more details. {0}"
msgstr "请安装支付应用以创建付费课程，详情请参阅文档{0}"

#: frontend/src/pages/Billing.vue:254
msgid "Please let us know where you heard about us from."
msgstr "请告知您通过何种渠道了解到我们"

#: frontend/src/components/QuizBlock.vue:5
msgid "Please login to access the quiz."
msgstr "请登录以访问测验"

#: frontend/src/components/NoPermission.vue:25 frontend/src/pages/Batch.vue:175
msgid "Please login to access this page."
msgstr "请登录以访问此页面"

#: lms/lms/api.py:210
msgid "Please login to continue with payment."
msgstr "请登录以继续支付"

#: lms/lms/notification/certificate_request_reminder/certificate_request_reminder.html:7
#: lms/templates/emails/certificate_request_notification.html:7
msgid "Please prepare well and be on time for the evaluations."
msgstr "请充分准备并准时参加评估"

#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseSubmission.vue:135
msgid "Please run the code to execute the test cases."
msgstr ""

#: frontend/src/components/UpcomingEvaluations.vue:98
msgid "Please schedule an evaluation to get certified."
msgstr "请安排评估以获取认证"

#: frontend/src/components/Modals/LiveClassModal.vue:189
msgid "Please select a date."
msgstr "请选择日期"

#: frontend/src/components/Modals/LiveClassModal.vue:213
msgid "Please select a duration."
msgstr "请选择时长"

#: frontend/src/components/Modals/LiveClassModal.vue:210
msgid "Please select a future date and time."
msgstr "请选择未来的日期和时间"

#: frontend/src/components/Modals/QuizInVideo.vue:186
msgid "Please select a quiz"
msgstr ""

#: frontend/src/components/Modals/LiveClassModal.vue:192
msgid "Please select a time."
msgstr "请选择时间"

#: frontend/src/components/Modals/LiveClassModal.vue:195
msgid "Please select a timezone."
msgstr "请选择时区"

#: frontend/src/components/Quiz.vue:533
msgid "Please select an option"
msgstr "请选择选项"

#: lms/templates/emails/job_report.html:6
msgid "Please take appropriate action at {0}"
msgstr "请在{0}采取适当操作"

#: frontend/src/components/Modals/ChapterModal.vue:175
msgid "Please upload a SCORM package"
msgstr "请上传SCORM包"

#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.py:81
msgid "Please upload the assignment file."
msgstr "请上传作业文件"

#. Option for the 'Grade Type' (Select) field in DocType 'Education Detail'
#: lms/lms/doctype/education_detail/education_detail.json
msgid "Point of Score (e.g. 70)"
msgstr "分数点（如70分）"

#: frontend/src/components/Modals/Question.vue:62
msgid "Possibilities"
msgstr "可能性分析"

#: frontend/src/components/Modals/Question.vue:91
msgid "Possibility"
msgstr "可能性"

#. Label of the possibility_1 (Small Text) field in DocType 'LMS Question'
#: lms/lms/doctype/lms_question/lms_question.json
msgid "Possible Answer 1"
msgstr "备选答案1"

#. Label of the possibility_2 (Small Text) field in DocType 'LMS Question'
#: lms/lms/doctype/lms_question/lms_question.json
msgid "Possible Answer 2"
msgstr "备选答案2"

#. Label of the possibility_3 (Small Text) field in DocType 'LMS Question'
#: lms/lms/doctype/lms_question/lms_question.json
msgid "Possible Answer 3"
msgstr "备选答案3"

#. Label of the possibility_4 (Small Text) field in DocType 'LMS Question'
#: lms/lms/doctype/lms_question/lms_question.json
msgid "Possible Answer 4"
msgstr "备选答案4"

#: frontend/src/components/DiscussionReplies.vue:54
#: frontend/src/components/DiscussionReplies.vue:89
msgid "Post"
msgstr "发布"

#: frontend/src/pages/Billing.vue:95
msgid "Postal Code"
msgstr "邮政编码"

#: frontend/src/components/AppSidebar.vue:122
msgid "Powered by Learning"
msgstr "技术支持：学习平台"

#. Name of a DocType
#: lms/lms/doctype/preferred_function/preferred_function.json
msgid "Preferred Function"
msgstr "首选职能"

#. Label of the preferred_functions (Table MultiSelect) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Preferred Functions"
msgstr "首选职能"

#. Label of the preferred_industries (Table MultiSelect) field in DocType
#. 'User'
#: lms/fixtures/custom_field.json
msgid "Preferred Industries"
msgstr "首选行业"

#. Name of a DocType
#: lms/lms/doctype/preferred_industry/preferred_industry.json
msgid "Preferred Industry"
msgstr "首选行业"

#. Label of the preferred_location (Data) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Preferred Location"
msgstr "首选地点"

#. Label of the prevent_skipping_videos (Check) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Prevent Skipping Videos"
msgstr ""

#. Label of the image (Attach Image) field in DocType 'LMS Course'
#: lms/lms/doctype/lms_course/lms_course.json
msgid "Preview Image"
msgstr "预览图片"

#: frontend/src/pages/CourseForm.vue:195
msgid "Preview Video"
msgstr "预览视频"

#: frontend/src/pages/Lesson.vue:114
msgid "Previous"
msgstr "上一项"

#. Label of the pricing_tab (Tab Break) field in DocType 'LMS Batch'
#: frontend/src/pages/BatchForm.vue:255
#: lms/lms/doctype/lms_batch/lms_batch.json
msgid "Pricing"
msgstr "定价"

#. Label of the pricing_tab (Tab Break) field in DocType 'LMS Course'
#: frontend/src/pages/CourseForm.vue:221
#: lms/lms/doctype/lms_course/lms_course.json
msgid "Pricing and Certification"
msgstr "定价与认证"

#. Label of the exception_country (Table MultiSelect) field in DocType 'LMS
#. Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Primary Countries"
msgstr "主要国家"

#. Label of the subgroup (Link) field in DocType 'Cohort Mentor'
#: lms/lms/doctype/cohort_mentor/cohort_mentor.json
msgid "Primary Subgroup"
msgstr "主分组"

#: lms/lms/utils.py:441
msgid "Privacy Policy"
msgstr "隐私政策"

#. Option for the 'Visibility' (Select) field in DocType 'LMS Batch Old'
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
msgid "Private"
msgstr "私有"

#. Description of the 'Hide my Private Information from others' (Check) field
#. in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Private Information includes your Grade and Work Environment Preferences"
msgstr "隐私信息包含您的成绩和工作环境偏好"

#. Label of the problem_statement (Text Editor) field in DocType 'LMS
#. Programming Exercise'
#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseForm.vue:41
#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseSubmission.vue:25
#: lms/lms/doctype/lms_programming_exercise/lms_programming_exercise.json
msgid "Problem Statement"
msgstr ""

#: frontend/src/pages/Billing.vue:129
msgid "Proceed to Payment"
msgstr "前往支付"

#. Label of the profession (Data) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Profession"
msgstr "职业"

#: frontend/src/components/Modals/EditProfile.vue:37
msgid "Profile Image"
msgstr "个人头像"

#: frontend/src/pages/ProgramForm.vue:155
msgid "Program Course"
msgstr "项目课程"

#. Label of the program_courses (Table) field in DocType 'LMS Program'
#: frontend/src/pages/ProgramForm.vue:17
#: lms/lms/doctype/lms_program/lms_program.json
msgid "Program Courses"
msgstr "项目课程"

#: frontend/src/pages/ProgramForm.vue:170
msgid "Program Member"
msgstr "项目成员"

#. Label of the program_members (Table) field in DocType 'LMS Program'
#: frontend/src/pages/ProgramForm.vue:79
#: lms/lms/doctype/lms_program/lms_program.json
msgid "Program Members"
msgstr "项目成员"

#: frontend/src/components/Assessments.vue:249
msgid "Programming Exercise"
msgstr ""

#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseSubmission.vue:420
msgid "Programming Exercise Submission"
msgstr ""

#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseSubmission.vue:411
#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseSubmissions.vue:298
msgid "Programming Exercise Submissions"
msgstr ""

#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseForm.vue:211
msgid "Programming Exercise created successfully"
msgstr ""

#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseForm.vue:247
msgid "Programming Exercise deleted successfully"
msgstr ""

#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseForm.vue:230
msgid "Programming Exercise updated successfully"
msgstr ""

#. Label of the programming_exercises (Check) field in DocType 'LMS Settings'
#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseSubmissions.vue:308
#: frontend/src/pages/ProgrammingExercises/ProgrammingExercises.vue:158
#: frontend/src/pages/ProgrammingExercises/ProgrammingExercises.vue:166
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Programming Exercises"
msgstr ""

#: frontend/src/pages/Programs.vue:206 frontend/src/pages/Programs.vue:212
#: lms/www/lms.py:295
msgid "Programs"
msgstr "课程项目"

#. Label of the progress (Float) field in DocType 'LMS Enrollment'
#. Label of the progress (Int) field in DocType 'LMS Program Member'
#: frontend/src/components/Modals/BatchStudentProgress.vue:94
#: frontend/src/components/Modals/CourseProgressSummary.vue:213
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
#: lms/lms/doctype/lms_program_member/lms_program_member.json
msgid "Progress"
msgstr "进度"

#: lms/lms/report/course_progress_summary/course_progress_summary.py:77
msgid "Progress (%)"
msgstr "进度百分比"

#: frontend/src/components/Modals/CourseProgressSummary.vue:103
msgid "Progress Distribution"
msgstr ""

#: frontend/src/components/CourseCardOverlay.vue:99
msgid "Progress Summary"
msgstr ""

#: frontend/src/components/BatchStudents.vue:41
msgid "Progress of students in courses and assessments"
msgstr ""

#. Option for the 'Required Role' (Select) field in DocType 'Cohort Web Page'
#. Option for the 'Visibility' (Select) field in DocType 'LMS Batch Old'
#: lms/lms/doctype/cohort_web_page/cohort_web_page.json
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
msgid "Public"
msgstr "公开"

#. Label of the published (Check) field in DocType 'LMS Certificate'
#: lms/lms/doctype/lms_certificate/lms_certificate.json
msgid "Publish on Participant Page"
msgstr "发布至参与者页面"

#. Label of the published (Check) field in DocType 'LMS Batch'
#. Label of the published (Check) field in DocType 'LMS Course'
#: frontend/src/components/Modals/BulkCertificates.vue:51
#: frontend/src/components/Modals/Event.vue:108
#: frontend/src/pages/BatchForm.vue:52 frontend/src/pages/CourseForm.vue:150
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_course/lms_course.json
msgid "Published"
msgstr "已发布"

#: frontend/src/pages/Statistics.vue:10
#: lms/lms/web_template/lms_statistics/lms_statistics.html:14
#: lms/templates/statistics.html:4
msgid "Published Courses"
msgstr "已发布课程"

#. Label of the published_on (Date) field in DocType 'LMS Course'
#: frontend/src/pages/CourseForm.vue:154
#: lms/lms/doctype/lms_course/lms_course.json
msgid "Published On"
msgstr "发布于"

#. Label of the purchased_certificate (Check) field in DocType 'LMS Enrollment'
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
msgid "Purchased Certificate"
msgstr "已购证书"

#. Option for the 'Language' (Select) field in DocType 'LMS Programming
#. Exercise'
#: lms/lms/doctype/lms_programming_exercise/lms_programming_exercise.json
msgid "Python"
msgstr "Python"

#. Label of the question (Small Text) field in DocType 'Course Lesson'
#. Label of the question (Text Editor) field in DocType 'LMS Assignment'
#. Label of the question (Text Editor) field in DocType 'LMS Assignment
#. Submission'
#. Label of the question (Text Editor) field in DocType 'LMS Question'
#. Label of the question (Link) field in DocType 'LMS Quiz Question'
#. Label of the question (Text) field in DocType 'LMS Quiz Result'
#: frontend/src/components/Assignment.vue:20
#: frontend/src/components/Modals/AssignmentForm.vue:32
#: frontend/src/components/Modals/Question.vue:27
#: frontend/src/pages/QuizForm.vue:343 frontend/src/pages/QuizSubmission.vue:56
#: lms/lms/doctype/course_lesson/course_lesson.json
#: lms/lms/doctype/lms_assignment/lms_assignment.json
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
#: lms/lms/doctype/lms_question/lms_question.json
#: lms/lms/doctype/lms_quiz_question/lms_quiz_question.json
#: lms/lms/doctype/lms_quiz_result/lms_quiz_result.json
#: lms/templates/quiz/quiz.html:104
msgid "Question"
msgstr "问题"

#: lms/templates/quiz/quiz.html:62
msgid "Question "
msgstr "问题"

#. Label of the question_detail (Text) field in DocType 'LMS Quiz Question'
#: lms/lms/doctype/lms_quiz_question/lms_quiz_question.json
msgid "Question Detail"
msgstr "问题详情"

#. Label of the question_name (Link) field in DocType 'LMS Quiz Result'
#: lms/lms/doctype/lms_quiz_result/lms_quiz_result.json
msgid "Question Name"
msgstr "问题名称"

#: frontend/src/components/Modals/Question.vue:284
msgid "Question added successfully"
msgstr "问题添加成功"

#: frontend/src/components/Modals/Question.vue:334
msgid "Question updated successfully"
msgstr "问题更新成功"

#: frontend/src/components/Quiz.vue:112
msgid "Question {0}"
msgstr "问题{0}"

#: frontend/src/components/Quiz.vue:214
msgid "Question {0} of {1}"
msgstr "第{0}/{1}题"

#. Label of the questions (Table) field in DocType 'LMS Quiz'
#: frontend/src/pages/QuizForm.vue:131 lms/lms/doctype/lms_quiz/lms_quiz.json
msgid "Questions"
msgstr "问题"

#: frontend/src/pages/QuizForm.vue:385
msgid "Questions deleted successfully"
msgstr "问题删除成功"

#. Label of the quiz (Link) field in DocType 'LMS Quiz Submission'
#. Label of a Link in the LMS Workspace
#: frontend/src/components/Assessments.vue:247
#: frontend/src/components/Modals/QuizInVideo.vue:21
#: frontend/src/components/Modals/QuizInVideo.vue:216
#: frontend/src/pages/QuizSubmission.vue:26 frontend/src/utils/quiz.js:24
#: lms/lms/doctype/lms_quiz_submission/lms_quiz_submission.json
#: lms/lms/workspace/lms/lms.json
msgid "Quiz"
msgstr "测验"

#. Label of the quiz_id (Data) field in DocType 'Course Lesson'
#: lms/lms/doctype/course_lesson/course_lesson.json
msgid "Quiz ID"
msgstr "测验ID"

#. Label of a Link in the LMS Workspace
#: frontend/src/pages/QuizPage.vue:57 lms/lms/workspace/lms/lms.json
msgid "Quiz Submission"
msgstr "测验提交"

#: frontend/src/pages/QuizSubmission.vue:131
#: frontend/src/pages/QuizSubmissionList.vue:111
#: frontend/src/pages/QuizSubmissionList.vue:116
msgid "Quiz Submissions"
msgstr "测验提交记录"

#: frontend/src/components/Quiz.vue:251
msgid "Quiz Summary"
msgstr "测验摘要"

#. Label of the quiz_title (Data) field in DocType 'LMS Quiz Submission'
#: lms/lms/doctype/lms_quiz_submission/lms_quiz_submission.json
msgid "Quiz Title"
msgstr "测验标题"

#: frontend/src/pages/Quizzes.vue:201
msgid "Quiz created successfully"
msgstr "测验创建成功"

#: lms/plugins.py:96
msgid "Quiz is not available to Guest users. Please login to continue."
msgstr "访客无法访问测验，请登录继续"

#: frontend/src/pages/QuizForm.vue:310
msgid "Quiz updated successfully"
msgstr "测验更新成功"

#. Description of the 'Quiz ID' (Data) field in DocType 'Course Lesson'
#: lms/lms/doctype/course_lesson/course_lesson.json
msgid "Quiz will appear at the bottom of the lesson."
msgstr "测验将显示在课时末尾"

#: frontend/src/components/AppSidebar.vue:584
#: frontend/src/pages/QuizForm.vue:396 frontend/src/pages/Quizzes.vue:275
#: frontend/src/pages/Quizzes.vue:285 lms/www/lms.py:251
msgid "Quizzes"
msgstr "测验"

#: frontend/src/pages/Quizzes.vue:223
msgid "Quizzes deleted successfully"
msgstr ""

#: frontend/src/components/Modals/QuizInVideo.vue:35
msgid "Quizzes in this video"
msgstr ""

#. Label of the rating (Rating) field in DocType 'LMS Certificate Evaluation'
#. Label of the rating (Data) field in DocType 'LMS Course'
#. Label of the rating (Rating) field in DocType 'LMS Course Review'
#: frontend/src/components/CourseCardOverlay.vue:147
#: frontend/src/components/Modals/Event.vue:86
#: frontend/src/components/Modals/ReviewModal.vue:18
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
#: lms/lms/doctype/lms_course/lms_course.json
#: lms/lms/doctype/lms_course_review/lms_course_review.json
#: lms/templates/reviews.html:125
msgid "Rating"
msgstr "评分"

#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.py:17
msgid "Rating cannot be 0"
msgstr "评分不可为0"

#. Option for the 'Stage' (Select) field in DocType 'LMS Batch Old'
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
msgid "Ready"
msgstr "就绪"

#. Label of the reference_docname (Dynamic Link) field in DocType 'LMS Batch
#. Timetable'
#: lms/lms/doctype/lms_batch_timetable/lms_batch_timetable.json
msgid "Reference DocName"
msgstr "参考文档名称"

#. Label of the reference_doctype (Link) field in DocType 'LMS Batch Timetable'
#. Label of the reference_doctype (Link) field in DocType 'LMS Timetable
#. Legend'
#: lms/lms/doctype/lms_batch_timetable/lms_batch_timetable.json
#: lms/lms/doctype/lms_timetable_legend/lms_timetable_legend.json
msgid "Reference DocType"
msgstr "参考文档类型"

#. Label of the reference_doctype (Link) field in DocType 'LMS Badge'
#: lms/lms/doctype/lms_badge/lms_badge.json
msgid "Reference Document Type"
msgstr "参考文档类型"

#: lms/templates/emails/community_course_membership.html:17
msgid "Regards"
msgstr "此致"

#: frontend/src/components/BatchOverlay.vue:89
msgid "Register Now"
msgstr "立即注册"

#. Option for the 'Status' (Select) field in DocType 'Invite Request'
#: lms/lms/doctype/invite_request/invite_request.json
msgid "Registered"
msgstr "已注册"

#: lms/lms/user.py:36
msgid "Registered but disabled"
msgstr "已注册但已停用"

#. Option for the 'Status' (Select) field in DocType 'Cohort Join Request'
#. Option for the 'Status' (Select) field in DocType 'Invite Request'
#. Option for the 'Status' (Select) field in DocType 'LMS Mentor Request'
#: lms/lms/doctype/cohort_join_request/cohort_join_request.json
#: lms/lms/doctype/invite_request/invite_request.json
#: lms/lms/doctype/lms_mentor_request/lms_mentor_request.json
msgid "Rejected"
msgstr "已拒绝"

#. Label of the related_courses (Table) field in DocType 'LMS Course'
#. Name of a DocType
#: frontend/src/components/RelatedCourses.vue:5
#: frontend/src/pages/CourseForm.vue:206
#: lms/lms/doctype/lms_course/lms_course.json
#: lms/lms/doctype/related_courses/related_courses.json
msgid "Related Courses"
msgstr "相关课程"

#: frontend/src/pages/BatchForm.vue:236 frontend/src/pages/CourseForm.vue:127
msgid "Remove"
msgstr "移除"

#: frontend/src/components/Modals/AnnouncementModal.vue:27
msgid "Reply To"
msgstr "回复至"

#: lms/lms/widgets/RequestInvite.html:7
msgid "Request Invite"
msgstr "申请邀请"

#: lms/patches/create_mentor_request_email_templates.py:20
msgid "Request for Mentorship"
msgstr "导师请求"

#. Label of the required_role (Select) field in DocType 'Cohort Web Page'
#: lms/lms/doctype/cohort_web_page/cohort_web_page.json
msgid "Required Role"
msgstr "所需角色"

#. Option for the 'Membership' (Select) field in DocType 'LMS Batch Old'
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
msgid "Restricted"
msgstr "受限"

#. Label of the result (Table) field in DocType 'LMS Quiz Submission'
#: lms/lms/doctype/lms_quiz_submission/lms_quiz_submission.json
msgid "Result"
msgstr "结果"

#. Label of the resume (Attach) field in DocType 'LMS Job Application'
#: lms/job/doctype/lms_job_application/lms_job_application.json
msgid "Resume"
msgstr "简历"

#: frontend/src/components/Quiz.vue:85 frontend/src/components/Quiz.vue:288
msgid "Resume Video"
msgstr ""

#. Label of the review (Small Text) field in DocType 'LMS Course Review'
#. Label of a Link in the LMS Workspace
#: frontend/src/components/Modals/ReviewModal.vue:20
#: lms/lms/doctype/lms_course_review/lms_course_review.json
#: lms/lms/workspace/lms/lms.json lms/templates/reviews.html:143
msgid "Review"
msgstr "审核"

#: lms/templates/reviews.html:100
msgid "Review the course"
msgstr "课程评价"

#. Label of the reviewed_by (Link) field in DocType 'LMS Mentor Request'
#: lms/lms/doctype/lms_mentor_request/lms_mentor_request.json
msgid "Reviewed By"
msgstr "审核人"

#: lms/templates/reviews.html:4
msgid "Reviews"
msgstr "评价"

#. Label of the role (Select) field in DocType 'Cohort Staff'
#. Label of the role (Select) field in DocType 'LMS Enrollment'
#: lms/lms/doctype/cohort_staff/cohort_staff.json
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
msgid "Role"
msgstr "角色"

#. Label of the role (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Role Preference"
msgstr "角色偏好"

#: frontend/src/pages/ProfileRoles.vue:117
msgid "Role updated successfully"
msgstr "角色更新成功。"

#: frontend/src/components/AppSidebar.vue:612
msgid "Roles"
msgstr "角色"

#. Label of the route (Data) field in DocType 'LMS Sidebar Item'
#: lms/lms/doctype/lms_sidebar_item/lms_sidebar_item.json
msgid "Route"
msgstr "路由"

#: lms/lms/doctype/lms_batch/lms_batch.py:139
msgid "Row #{0} Date cannot be outside the batch duration."
msgstr "第{0}行日期不可超出批次周期"

#: lms/lms/doctype/lms_batch/lms_batch.py:134
msgid "Row #{0} End time cannot be outside the batch duration."
msgstr "第{0}行结束时间不可超出批次周期"

#: lms/lms/doctype/lms_batch/lms_batch.py:116
msgid "Row #{0} Start time cannot be greater than or equal to end time."
msgstr "第{0}行开始时间不可大于等于结束时间"

#: lms/lms/doctype/lms_batch/lms_batch.py:125
msgid "Row #{0} Start time cannot be outside the batch duration."
msgstr "第{0}行开始时间不可超出批次周期"

#: lms/lms/doctype/lms_quiz/lms_quiz.py:32
msgid "Rows {0} have the duplicate questions."
msgstr "第{0}行存在重复试题"

#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseSubmission.vue:56
#: lms/templates/livecode/extension_footer.html:21
msgid "Run"
msgstr "运行"

#. Label of the scorm_section (Section Break) field in DocType 'Course Chapter'
#: lms/lms/doctype/course_chapter/course_chapter.json
msgid "SCORM"
msgstr "SCORM标准"

#. Label of the scorm_package (Link) field in DocType 'Course Chapter'
#: frontend/src/components/Modals/ChapterModal.vue:22
#: lms/lms/doctype/course_chapter/course_chapter.json
msgid "SCORM Package"
msgstr "SCORM包"

#. Label of the scorm_package_path (Code) field in DocType 'Course Chapter'
#: lms/lms/doctype/course_chapter/course_chapter.json
msgid "SCORM Package Path"
msgstr "SCORM包路径"

#. Label of the seo_tab (Tab Break) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "SEO"
msgstr "搜索引擎优化"

#. Option for the 'Day' (Select) field in DocType 'Evaluator Schedule'
#. Option for the 'Day' (Select) field in DocType 'LMS Certificate Request'
#: lms/lms/doctype/evaluator_schedule/evaluator_schedule.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
msgid "Saturday"
msgstr "星期六"

#: frontend/src/components/AssessmentPlugin.vue:12
#: frontend/src/components/Assignment.vue:46
#: frontend/src/components/Controls/Code.vue:18
#: frontend/src/components/Controls/CodeEditor.vue:25
#: frontend/src/components/Modals/AssignmentForm.vue:59
#: frontend/src/components/Modals/EmailTemplateModal.vue:12
#: frontend/src/components/Modals/Event.vue:101
#: frontend/src/components/Modals/Event.vue:129
#: frontend/src/components/Modals/Question.vue:112
#: frontend/src/components/Modals/ZoomAccountModal.vue:10
#: frontend/src/pages/BatchForm.vue:8 frontend/src/pages/CourseForm.vue:17
#: frontend/src/pages/JobForm.vue:8 frontend/src/pages/LessonForm.vue:14
#: frontend/src/pages/ProgramForm.vue:7
#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseForm.vue:101
#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseModal.vue:9
#: frontend/src/pages/QuizForm.vue:43 frontend/src/pages/QuizSubmission.vue:14
#: frontend/src/pages/Quizzes.vue:105
msgid "Save"
msgstr "保存"

#. Label of the schedule (Table) field in DocType 'Course Evaluator'
#: lms/lms/doctype/course_evaluator/course_evaluator.json
msgid "Schedule"
msgstr "日程"

#: frontend/src/components/Modals/EvaluationModal.vue:5
#: frontend/src/components/UpcomingEvaluations.vue:11
msgid "Schedule Evaluation"
msgstr "安排评估"

#. Name of a DocType
#: lms/lms/doctype/scheduled_flow/scheduled_flow.json
msgid "Scheduled Flow"
msgstr "预定流程"

#. Label of the scope (Select) field in DocType 'Cohort Web Page'
#: lms/lms/doctype/cohort_web_page/cohort_web_page.json
msgid "Scope"
msgstr "范围"

#. Label of the score (Int) field in DocType 'LMS Quiz Submission'
#: frontend/src/pages/QuizSubmission.vue:39
#: frontend/src/pages/QuizSubmissionList.vue:96
#: lms/lms/doctype/lms_quiz_submission/lms_quiz_submission.json
#: lms/templates/quiz/quiz.html:148
msgid "Score"
msgstr "得分"

#. Label of the score_out_of (Int) field in DocType 'LMS Quiz Submission'
#: lms/lms/doctype/lms_quiz_submission/lms_quiz_submission.json
msgid "Score Out Of"
msgstr "总分值"

#: frontend/src/components/Settings/Evaluators.vue:25
#: frontend/src/components/Settings/Members.vue:25
#: frontend/src/pages/Jobs.vue:41
msgid "Search"
msgstr "搜索"

#: frontend/src/components/Modals/CourseProgressSummary.vue:18
msgid "Search by Member Name"
msgstr ""

#: frontend/src/pages/CertifiedParticipants.vue:23
msgid "Search by Name"
msgstr "按名称搜索"

#: frontend/src/pages/Batches.vue:45 frontend/src/pages/Courses.vue:41
msgid "Search by Title"
msgstr "按标题搜索"

#: frontend/src/pages/Assignments.vue:34
#: frontend/src/pages/ProgrammingExercises/ProgrammingExercises.vue:47
msgid "Search by title"
msgstr "按标题搜索"

#: frontend/src/components/Controls/IconPicker.vue:36
msgid "Search for an icon"
msgstr "搜索图标"

#. Label of the seat_count (Int) field in DocType 'LMS Batch'
#: frontend/src/pages/BatchForm.vue:147
#: lms/lms/doctype/lms_batch/lms_batch.json
msgid "Seat Count"
msgstr "席位总数"

#: frontend/src/components/BatchCard.vue:18
#: frontend/src/components/BatchOverlay.vue:17
msgid "Seat Left"
msgstr "剩余席位"

#: lms/lms/doctype/lms_batch/lms_batch.py:103
msgid "Seat count cannot be negative."
msgstr "座位数不可为负数。"

#: frontend/src/components/BatchCard.vue:15
#: frontend/src/components/BatchOverlay.vue:14
msgid "Seats Left"
msgstr "剩余席位"

#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseModal.vue:23
msgid "Select a Programming Exercise"
msgstr ""

#: frontend/src/components/Modals/Question.vue:101
msgid "Select a question"
msgstr "选择试题"

#: frontend/src/components/AssessmentPlugin.vue:28
msgid "Select a quiz"
msgstr "选择测验"

#: frontend/src/components/Modals/EvaluationModal.vue:40
msgid "Select a slot"
msgstr "选择时段"

#: frontend/src/components/AssessmentPlugin.vue:35
msgid "Select an assignment"
msgstr "选择作业"

#: lms/lms/doctype/lms_batch_enrollment/lms_batch_enrollment.js:7
msgid "Send Confirmation Email"
msgstr "发送确认邮件"

#. Label of the send_calendar_invite_for_evaluations (Check) field in DocType
#. 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Send calendar invite for evaluations"
msgstr "发送评估日历邀请"

#. Label of the sessions_on (Data) field in DocType 'LMS Batch Old'
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
msgid "Sessions On Days"
msgstr "上课日"

#: lms/templates/emails/community_course_membership.html:1
msgid "Set your Password"
msgstr "设置密码"

#: frontend/src/components/AppSidebar.vue:560
msgid "Setting up"
msgstr "系统配置中"

#: frontend/src/components/AppSidebar.vue:605
msgid "Setting up payment gateway"
msgstr "设置支付网关"

#: frontend/src/components/AppSidebar.vue:610
#: frontend/src/components/Settings/Settings.vue:7
#: frontend/src/pages/BatchForm.vue:46 frontend/src/pages/CourseForm.vue:143
#: frontend/src/pages/ProfileRoles.vue:4
#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseSubmission.vue:19
#: frontend/src/pages/QuizForm.vue:86
msgid "Settings"
msgstr "设置"

#: frontend/src/pages/ProfileAbout.vue:62
msgid "Share on"
msgstr "分享至"

#: frontend/src/pages/BatchForm.vue:35
msgid "Short Description"
msgstr "简短描述"

#. Label of the short_introduction (Small Text) field in DocType 'LMS Course'
#: frontend/src/pages/CourseForm.vue:80
#: lms/lms/doctype/lms_course/lms_course.json
msgid "Short Introduction"
msgstr "简介"

#: frontend/src/pages/BatchForm.vue:38
msgid "Short description of the batch"
msgstr "批次简介"

#. Label of the show_answer (Check) field in DocType 'LMS Assignment'
#: lms/lms/doctype/lms_assignment/lms_assignment.json
msgid "Show Answer"
msgstr "显示答案"

#. Label of the show_answers (Check) field in DocType 'LMS Quiz'
#: frontend/src/pages/QuizForm.vue:93 frontend/src/pages/Quizzes.vue:256
#: lms/lms/doctype/lms_quiz/lms_quiz.json
msgid "Show Answers"
msgstr "显示答案"

#. Label of the show_submission_history (Check) field in DocType 'LMS Quiz'
#: frontend/src/pages/QuizForm.vue:98 lms/lms/doctype/lms_quiz/lms_quiz.json
msgid "Show Submission History"
msgstr "显示提交历史"

#. Label of the column_break_2 (Column Break) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Show Tab in Batch"
msgstr "在批次中显示标签页"

#. Label of the show_usd_equivalent (Check) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Show USD Equivalent"
msgstr "显示美元等值"

#. Label of the show_day_view (Check) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Show day view in timetable"
msgstr "在课表中显示日视图"

#. Label of the show_live_class (Check) field in DocType 'LMS Batch'
#: lms/lms/doctype/lms_batch/lms_batch.json
msgid "Show live class"
msgstr "显示直播课程"

#. Label of the shuffle_questions (Check) field in DocType 'LMS Quiz'
#: frontend/src/pages/QuizForm.vue:105 lms/lms/doctype/lms_quiz/lms_quiz.json
msgid "Shuffle Questions"
msgstr "题目乱序"

#. Label of the sidebar_tab (Tab Break) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Sidebar"
msgstr "侧边栏"

#. Label of the sidebar_items (Table) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Sidebar Items"
msgstr "侧边栏项目"

#: lms/lms/user.py:29
msgid "Sign Up is disabled"
msgstr "注册功能已禁用"

#: lms/templates/signup-form.html:53
msgid "Sign up"
msgstr "注册"

#. Label of the signup_email (Data) field in DocType 'Invite Request'
#: lms/lms/doctype/invite_request/invite_request.json
msgid "Signup Email"
msgstr "注册邮件"

#. Label of the signup_settings_tab (Tab Break) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Signup Settings"
msgstr "注册设置"

#. Label of a chart in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "Signups"
msgstr "注册记录"

#. Label of the skill (Table MultiSelect) field in DocType 'User'
#. Label of the skill (Data) field in DocType 'User Skill'
#: lms/fixtures/custom_field.json lms/lms/doctype/user_skill/user_skill.json
msgid "Skill"
msgstr "技能"

#. Label of the skill_details (Section Break) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Skill Details"
msgstr "技能详情"

#. Label of the skill_name (Link) field in DocType 'Skills'
#: lms/lms/doctype/skills/skills.json
msgid "Skill Name"
msgstr "技能名称"

#. Name of a DocType
#: lms/lms/doctype/skills/skills.json
msgid "Skills"
msgstr "技能"

#: frontend/src/pages/PersonaForm.vue:51 lms/templates/onboarding_header.html:6
msgid "Skip"
msgstr "跳过"

#: lms/lms/doctype/course_evaluator/course_evaluator.py:63
msgid "Slot Times are overlapping for some schedules."
msgstr "部分日程时段存在重叠"

#: frontend/src/pages/ProfileEvaluator.vue:201
msgid "Slot added successfully"
msgstr "时段添加成功。"

#: frontend/src/pages/ProfileEvaluator.vue:240
msgid "Slot deleted successfully"
msgstr "时段删除成功。"

#. Label of the slug (Data) field in DocType 'Cohort'
#. Label of the slug (Data) field in DocType 'Cohort Subgroup'
#. Label of the slug (Data) field in DocType 'Cohort Web Page'
#: lms/lms/doctype/cohort/cohort.json
#: lms/lms/doctype/cohort_subgroup/cohort_subgroup.json
#: lms/lms/doctype/cohort_web_page/cohort_web_page.json
msgid "Slug"
msgstr "URL别名"

#: frontend/src/components/BatchCard.vue:25
#: frontend/src/components/BatchOverlay.vue:24
msgid "Sold Out"
msgstr "已售罄"

#. Label of the solution (Code) field in DocType 'Exercise Latest Submission'
#. Label of the solution (Code) field in DocType 'Exercise Submission'
#: lms/lms/doctype/exercise_latest_submission/exercise_latest_submission.json
#: lms/lms/doctype/exercise_submission/exercise_submission.json
msgid "Solution"
msgstr "解决方案"

#. Label of the source (Link) field in DocType 'LMS Batch Enrollment'
#. Label of the source (Link) field in DocType 'LMS Payment'
#. Label of the source (Data) field in DocType 'LMS Source'
#. Label of the source (Data) field in DocType 'LMS Video Watch Duration'
#: lms/lms/doctype/lms_batch_enrollment/lms_batch_enrollment.json
#: lms/lms/doctype/lms_payment/lms_payment.json
#: lms/lms/doctype/lms_source/lms_source.json
#: lms/lms/doctype/lms_video_watch_duration/lms_video_watch_duration.json
msgid "Source"
msgstr "来源"

#. Option for the 'Role' (Select) field in DocType 'Cohort Staff'
#. Option for the 'Member Type' (Select) field in DocType 'LMS Enrollment'
#: lms/lms/doctype/cohort_staff/cohort_staff.json
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
msgid "Staff"
msgstr "工作人员"

#. Label of the stage (Select) field in DocType 'LMS Batch Old'
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
msgid "Stage"
msgstr "阶段"

#: frontend/src/components/LiveClass.vue:70 frontend/src/components/Quiz.vue:81
#: lms/templates/quiz/quiz.html:39
msgid "Start"
msgstr "开始"

#. Label of the start_date (Date) field in DocType 'Education Detail'
#. Label of the start_date (Date) field in DocType 'LMS Batch'
#. Label of the start_date (Date) field in DocType 'LMS Batch Old'
#: frontend/src/pages/BatchForm.vue:75
#: lms/lms/doctype/education_detail/education_detail.json
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
msgid "Start Date"
msgstr "开始日期"

#: lms/templates/emails/batch_start_reminder.html:13
msgid "Start Date:"
msgstr "开始日期："

#: frontend/src/components/CourseCardOverlay.vue:76
#: frontend/src/pages/Lesson.vue:45 frontend/src/pages/SCORMChapter.vue:28
#: lms/templates/emails/lms_course_interest.html:9
msgid "Start Learning"
msgstr "开始学习"

#. Label of the start_time (Time) field in DocType 'Evaluator Schedule'
#. Label of the start_time (Time) field in DocType 'LMS Batch'
#. Label of the start_time (Time) field in DocType 'LMS Batch Old'
#. Label of the start_time (Time) field in DocType 'LMS Batch Timetable'
#. Label of the start_time (Time) field in DocType 'LMS Certificate Evaluation'
#. Label of the start_time (Time) field in DocType 'LMS Certificate Request'
#. Label of the start_time (Time) field in DocType 'Scheduled Flow'
#: frontend/src/pages/BatchForm.vue:91
#: frontend/src/pages/ProfileEvaluator.vue:29
#: lms/lms/doctype/evaluator_schedule/evaluator_schedule.json
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
#: lms/lms/doctype/lms_batch_timetable/lms_batch_timetable.json
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
#: lms/lms/doctype/scheduled_flow/scheduled_flow.json
msgid "Start Time"
msgstr "开始时间"

#: lms/lms/doctype/course_evaluator/course_evaluator.py:34
msgid "Start Time cannot be greater than End Time"
msgstr "开始时间不可晚于结束时间"

#. Label of the start_url (Small Text) field in DocType 'LMS Live Class'
#: lms/lms/doctype/lms_live_class/lms_live_class.json
msgid "Start URL"
msgstr "启动链接"

#: frontend/src/components/Quiz.vue:81
msgid "Start the Quiz"
msgstr ""

#. Option for the 'Company Type' (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Startup Organization"
msgstr "初创机构"

#: frontend/src/pages/Billing.vue:83
msgid "State/Province"
msgstr "州/省"

#. Label of the tab_4_tab (Tab Break) field in DocType 'LMS Course'
#. Label of the statistics (Check) field in DocType 'LMS Settings'
#: frontend/src/components/BatchStudents.vue:5
#: frontend/src/pages/Statistics.vue:225
#: lms/lms/doctype/lms_course/lms_course.json
#: lms/lms/doctype/lms_settings/lms_settings.json lms/www/lms.py:204
msgid "Statistics"
msgstr "统计"

#. Label of the status (Select) field in DocType 'Job Opportunity'
#. Label of the status (Select) field in DocType 'Cohort'
#. Label of the status (Select) field in DocType 'Cohort Join Request'
#. Label of the status (Select) field in DocType 'Exercise Latest Submission'
#. Label of the status (Select) field in DocType 'Exercise Submission'
#. Label of the status (Select) field in DocType 'Invite Request'
#. Label of the status (Select) field in DocType 'LMS Assignment Submission'
#. Label of the status (Select) field in DocType 'LMS Batch Old'
#. Label of the status (Select) field in DocType 'LMS Certificate Evaluation'
#. Label of the status (Select) field in DocType 'LMS Certificate Request'
#. Label of the status (Select) field in DocType 'LMS Course'
#. Label of the status (Select) field in DocType 'LMS Course Progress'
#. Label of the status (Select) field in DocType 'LMS Mentor Request'
#. Label of the status (Select) field in DocType 'LMS Programming Exercise
#. Submission'
#. Label of the status (Select) field in DocType 'LMS Test Case Submission'
#: frontend/src/components/Modals/Event.vue:91
#: frontend/src/components/Settings/ZoomSettings.vue:182
#: frontend/src/pages/AssignmentSubmissionList.vue:19
#: frontend/src/pages/JobForm.vue:46
#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseSubmissions.vue:280
#: lms/job/doctype/job_opportunity/job_opportunity.json
#: lms/lms/doctype/cohort/cohort.json
#: lms/lms/doctype/cohort_join_request/cohort_join_request.json
#: lms/lms/doctype/exercise_latest_submission/exercise_latest_submission.json
#: lms/lms/doctype/exercise_submission/exercise_submission.json
#: lms/lms/doctype/invite_request/invite_request.json
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
#: lms/lms/doctype/lms_course/lms_course.json
#: lms/lms/doctype/lms_course_progress/lms_course_progress.json
#: lms/lms/doctype/lms_mentor_request/lms_mentor_request.json
#: lms/lms/doctype/lms_programming_exercise_submission/lms_programming_exercise_submission.json
#: lms/lms/doctype/lms_test_case_submission/lms_test_case_submission.json
msgid "Status"
msgstr "状态"

#: lms/templates/assessments.html:17
msgid "Status/Score"
msgstr "状态/得分"

#. Option for the 'User Category' (Select) field in DocType 'User'
#. Option for the 'Required Role' (Select) field in DocType 'Cohort Web Page'
#. Option for the 'Member Type' (Select) field in DocType 'LMS Enrollment'
#: frontend/src/pages/ProfileRoles.vue:38 lms/fixtures/custom_field.json
#: lms/lms/doctype/cohort_web_page/cohort_web_page.json
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
#: lms/templates/signup-form.html:26
msgid "Student"
msgstr "学员"

#: frontend/src/components/CourseReviews.vue:11
msgid "Student Reviews"
msgstr "学员评价"

#. Label of the show_students (Check) field in DocType 'LMS Settings'
#: frontend/src/components/BatchStudents.vue:11
#: frontend/src/components/BatchStudents.vue:67
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Students"
msgstr "学员"

#: frontend/src/components/BatchStudents.vue:285
msgid "Students deleted successfully"
msgstr "学员删除成功"

#. Description of the 'Paid Batch' (Check) field in DocType 'LMS Batch'
#: lms/lms/doctype/lms_batch/lms_batch.json
msgid "Students will be enrolled in a paid batch once they complete the payment"
msgstr "学员支付完成后将加入付费批次"

#. Label of the subgroup (Link) field in DocType 'Cohort Join Request'
#. Option for the 'Scope' (Select) field in DocType 'Cohort Web Page'
#. Label of the subgroup (Link) field in DocType 'LMS Enrollment'
#: lms/lms/doctype/cohort_join_request/cohort_join_request.json
#: lms/lms/doctype/cohort_web_page/cohort_web_page.json
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
msgid "Subgroup"
msgstr "子组"

#: frontend/src/components/Modals/AnnouncementModal.vue:20
#: frontend/src/components/Modals/EmailTemplateModal.vue:31
msgid "Subject"
msgstr "主题"

#: frontend/src/components/Modals/AnnouncementModal.vue:93
msgid "Subject is required"
msgstr "必须填写主题"

#: frontend/src/components/Assignment.vue:32
msgid "Submission"
msgstr "提交"

#: frontend/src/components/Modals/AssignmentForm.vue:27
msgid "Submission Type"
msgstr "提交类型"

#: frontend/src/components/Assignment.vue:13
#: frontend/src/components/Assignment.vue:16
msgid "Submission by"
msgstr "提交人"

#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseSubmission.vue:353
msgid "Submission saved!"
msgstr ""

#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseSubmissions.vue:254
msgid "Submissions deleted successfully"
msgstr ""

#: frontend/src/components/Modals/AssessmentModal.vue:9
#: frontend/src/components/Modals/BatchCourseModal.vue:9
#: frontend/src/components/Modals/EvaluationModal.vue:9
#: frontend/src/components/Quiz.vue:242 lms/templates/assignment.html:9
#: lms/templates/livecode/extension_footer.html:25
#: lms/templates/quiz/quiz.html:128 lms/templates/reviews.html:163
#: lms/www/new-sign-up.html:32
msgid "Submit"
msgstr "提交"

#: frontend/src/components/BatchFeedback.vue:35
msgid "Submit Feedback"
msgstr "提交反馈"

#: frontend/src/pages/PersonaForm.vue:43
msgid "Submit and Continue"
msgstr "提交并继续"

#: frontend/src/components/Modals/JobApplicationModal.vue:23
msgid "Submit your resume to proceed with your application for this position. Upon submission, it will be shared with the job poster."
msgstr "请提交简历以继续申请此职位。提交后，简历将分享给招聘方"

#: lms/templates/livecode/extension_footer.html:85
#: lms/templates/livecode/extension_footer.html:115
msgid "Submitted {0}"
msgstr "已提交{0}"

#. Label of the summary (Small Text) field in DocType 'LMS Certificate
#. Evaluation'
#: frontend/src/components/Modals/Event.vue:97
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
msgid "Summary"
msgstr "摘要"

#. Option for the 'Day' (Select) field in DocType 'Evaluator Schedule'
#. Option for the 'Day' (Select) field in DocType 'LMS Certificate Request'
#: lms/lms/doctype/evaluator_schedule/evaluator_schedule.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
msgid "Sunday"
msgstr "星期日"

#: lms/lms/api.py:1075
msgid "Suspicious pattern found in {0}: {1}"
msgstr "在{0}中发现可疑模式：{1}"

#. Name of a role
#: lms/job/doctype/job_opportunity/job_opportunity.json
#: lms/job/doctype/job_settings/job_settings.json
#: lms/job/doctype/lms_job_application/lms_job_application.json
#: lms/lms/doctype/cohort/cohort.json
#: lms/lms/doctype/cohort_join_request/cohort_join_request.json
#: lms/lms/doctype/cohort_mentor/cohort_mentor.json
#: lms/lms/doctype/cohort_staff/cohort_staff.json
#: lms/lms/doctype/cohort_subgroup/cohort_subgroup.json
#: lms/lms/doctype/course_chapter/course_chapter.json
#: lms/lms/doctype/course_evaluator/course_evaluator.json
#: lms/lms/doctype/course_lesson/course_lesson.json
#: lms/lms/doctype/exercise_latest_submission/exercise_latest_submission.json
#: lms/lms/doctype/exercise_submission/exercise_submission.json
#: lms/lms/doctype/function/function.json
#: lms/lms/doctype/industry/industry.json
#: lms/lms/doctype/invite_request/invite_request.json
#: lms/lms/doctype/lms_assignment/lms_assignment.json
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
#: lms/lms/doctype/lms_badge/lms_badge.json
#: lms/lms/doctype/lms_badge_assignment/lms_badge_assignment.json
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_batch_enrollment/lms_batch_enrollment.json
#: lms/lms/doctype/lms_batch_feedback/lms_batch_feedback.json
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
#: lms/lms/doctype/lms_category/lms_category.json
#: lms/lms/doctype/lms_certificate/lms_certificate.json
#: lms/lms/doctype/lms_certificate_evaluation/lms_certificate_evaluation.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
#: lms/lms/doctype/lms_course/lms_course.json
#: lms/lms/doctype/lms_course_interest/lms_course_interest.json
#: lms/lms/doctype/lms_course_mentor_mapping/lms_course_mentor_mapping.json
#: lms/lms/doctype/lms_course_progress/lms_course_progress.json
#: lms/lms/doctype/lms_course_review/lms_course_review.json
#: lms/lms/doctype/lms_enrollment/lms_enrollment.json
#: lms/lms/doctype/lms_exercise/lms_exercise.json
#: lms/lms/doctype/lms_live_class/lms_live_class.json
#: lms/lms/doctype/lms_live_class_participant/lms_live_class_participant.json
#: lms/lms/doctype/lms_mentor_request/lms_mentor_request.json
#: lms/lms/doctype/lms_payment/lms_payment.json
#: lms/lms/doctype/lms_program/lms_program.json
#: lms/lms/doctype/lms_programming_exercise/lms_programming_exercise.json
#: lms/lms/doctype/lms_programming_exercise_submission/lms_programming_exercise_submission.json
#: lms/lms/doctype/lms_question/lms_question.json
#: lms/lms/doctype/lms_quiz/lms_quiz.json
#: lms/lms/doctype/lms_quiz_submission/lms_quiz_submission.json
#: lms/lms/doctype/lms_settings/lms_settings.json
#: lms/lms/doctype/lms_source/lms_source.json
#: lms/lms/doctype/lms_timetable_template/lms_timetable_template.json
#: lms/lms/doctype/lms_video_watch_duration/lms_video_watch_duration.json
#: lms/lms/doctype/lms_zoom_settings/lms_zoom_settings.json
#: lms/lms/doctype/user_skill/user_skill.json
#: lms/lms/doctype/zoom_settings/zoom_settings.json
msgid "System Manager"
msgstr "系统管理员"

#. Label of the tags (Data) field in DocType 'LMS Course'
#: frontend/src/pages/CourseForm.vue:51
#: lms/lms/doctype/lms_course/lms_course.json
msgid "Tags"
msgstr "标签"

#: lms/templates/emails/community_course_membership.html:18
#: lms/templates/emails/mentor_request_creation_email.html:8
#: lms/templates/emails/mentor_request_status_update_email.html:7
msgid "Team School"
msgstr "团队学院"

#. Option for the 'Collaboration Preference' (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Team Work"
msgstr "团队协作"

#. Label of the template (Link) field in DocType 'Cohort Web Page'
#. Label of the template (Link) field in DocType 'LMS Certificate'
#: frontend/src/components/Modals/BulkCertificates.vue:43
#: frontend/src/components/Modals/Event.vue:112
#: lms/lms/doctype/cohort_web_page/cohort_web_page.json
#: lms/lms/doctype/lms_certificate/lms_certificate.json
msgid "Template"
msgstr "模板"

#: lms/lms/user.py:40
msgid "Temporarily Disabled"
msgstr "临时禁用"

#: lms/lms/utils.py:440
msgid "Terms of Use"
msgstr "使用条款"

#. Label of the test_cases (Table) field in DocType 'LMS Programming Exercise'
#. Label of the test_cases (Table) field in DocType 'LMS Programming Exercise
#. Submission'
#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseForm.vue:29
#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseSubmission.vue:83
#: lms/lms/doctype/lms_programming_exercise/lms_programming_exercise.json
#: lms/lms/doctype/lms_programming_exercise_submission/lms_programming_exercise_submission.json
msgid "Test Cases"
msgstr ""

#: frontend/src/pages/QuizForm.vue:23
msgid "Test Quiz"
msgstr ""

#. Label of the test_results (Small Text) field in DocType 'Exercise Latest
#. Submission'
#. Label of the test_results (Small Text) field in DocType 'Exercise
#. Submission'
#: lms/lms/doctype/exercise_latest_submission/exercise_latest_submission.json
#: lms/lms/doctype/exercise_submission/exercise_submission.json
msgid "Test Results"
msgstr "测试结果"

#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseForm.vue:82
msgid "Test this Exercise"
msgstr ""

#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseSubmission.vue:92
msgid "Test {0}"
msgstr ""

#. Label of the tests (Code) field in DocType 'LMS Exercise'
#: lms/lms/doctype/lms_exercise/lms_exercise.json
msgid "Tests"
msgstr "测试"

#. Option for the 'Type' (Select) field in DocType 'LMS Assignment'
#. Option for the 'Type' (Select) field in DocType 'LMS Assignment Submission'
#: lms/lms/doctype/lms_assignment/lms_assignment.json
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
msgid "Text"
msgstr "文本"

#: frontend/src/components/BatchFeedback.vue:6
msgid "Thank you for providing your feedback."
msgstr ""

#: lms/templates/emails/lms_course_interest.html:17
#: lms/templates/emails/lms_invite_request_approved.html:15
#: lms/templates/emails/mentor_request_creation_email.html:7
#: lms/templates/emails/mentor_request_status_update_email.html:6
msgid "Thanks and Regards"
msgstr "此致敬礼"

#: lms/lms/utils.py:1975
msgid "The batch is full. Please contact the Administrator."
msgstr "该批次已满。请联系管理员"

#: lms/templates/emails/batch_start_reminder.html:6
msgid "The batch you have enrolled for is starting tomorrow. Please be prepared and be on time for the session."
msgstr "您报名的批次明日开始，请做好准备准时参加"

#: lms/templates/emails/lms_course_interest.html:5
msgid "The course {0} is now available on {1}."
msgstr "课程{0}现已在{1}上线"

#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.py:53
msgid "The evaluator of this course is unavailable from {0} to {1}. Please select a date after {1}"
msgstr "本课程评估人{0}至{1}期间不可用，请选择{1}之后的日期"

#: lms/templates/quiz/quiz.html:24
msgid "The quiz has a time limit. For each question you will be given {0} seconds."
msgstr "本测验有时间限制，每题限时{0}秒"

#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.py:71
msgid "The slot is already booked by another participant."
msgstr "该时段已被其他参与者预定"

#: lms/patches/create_mentor_request_email_templates.py:40
msgid "The status of your application has changed."
msgstr "您的申请状态已变更"

#: frontend/src/components/CreateOutline.vue:12
msgid "There are no chapters in this course. Create and manage chapters from here."
msgstr "本课程暂无章节，请在此创建和管理章节"

#: lms/lms/doctype/lms_batch/lms_batch.py:107
msgid "There are no seats available in this batch."
msgstr "本批次无可用席位"

#: frontend/src/components/BatchStudents.vue:155
msgid "There are no students in this batch."
msgstr "本批次暂无学员"

#: frontend/src/pages/AssignmentSubmissionList.vue:70
msgid "There are no submissions for this assignment."
msgstr "本作业暂无提交记录"

#: frontend/src/components/EmptyState.vue:11
msgid "There are no {0} currently. Keep an eye out, fresh learning experiences are on the way!"
msgstr "当前暂无{0}，新学习资源即将上线，敬请关注！"

#: lms/templates/course_list.html:14
msgid "There are no {0} on this site."
msgstr "本站暂无{0}"

#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.py:44
msgid "There has been an update on your submission for assignment {0}"
msgstr "您的作业{0}提交状态已更新"

#: lms/lms/doctype/lms_quiz_submission/lms_quiz_submission.py:59
msgid "There has been an update on your submission. You have got a score of {0} for the quiz {1}"
msgstr "您的提交状态已更新，测验{1}得分为{0}"

#. Description of the 'section_break_ubxi' (Section Break) field in DocType
#. 'LMS Batch'
#: lms/lms/doctype/lms_batch/lms_batch.json
msgid "These customisations will work on the main batch page."
msgstr "此自定义配置将应用于主批次页面"

#: frontend/src/pages/Badge.vue:14
msgid "This badge has been awarded to {0} on {1}."
msgstr "此徽章已于{1}授予{0}"

#. Label of the expire (Check) field in DocType 'Certification'
#: lms/lms/doctype/certification/certification.json
msgid "This certificate does no expire"
msgstr "本证书永久有效"

#: frontend/src/components/LiveClass.vue:83
msgid "This class has ended"
msgstr "本课程已结束"

#: frontend/src/components/CourseCardOverlay.vue:126
msgid "This course has:"
msgstr "本课程包含："

#: lms/lms/utils.py:1818
msgid "This course is free."
msgstr "本课程免费"

#. Description of the 'Meta Description' (Small Text) field in DocType 'LMS
#. Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "This description will be shown on lists and pages without meta description"
msgstr "本描述将展示在无元描述的列表及页面中"

#. Description of the 'Meta Image' (Attach Image) field in DocType 'LMS
#. Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "This image will be shown on lists and pages that don't have an image by default"
msgstr "本图片将显示在无默认图片的列表及页面"

#: frontend/src/pages/Lesson.vue:30
msgid "This lesson is locked"
msgstr "本课时已锁定"

#: frontend/src/pages/Lesson.vue:35
msgid "This lesson is not available for preview. Please enroll in the course to access it."
msgstr "本课时不可预览，请报名课程后访问"

#: lms/lms/widgets/NoPreviewModal.html:16
msgid "This lesson is not available for preview. Please join the course to access it."
msgstr "本课时不可预览，请加入课程后访问"

#: frontend/src/components/Quiz.vue:11 lms/templates/quiz/quiz.html:6
msgid "This quiz consists of {0} questions."
msgstr "本测验共包含{0}道试题"

#: frontend/src/components/AppSidebar.vue:75
#: frontend/src/components/AppSidebar.vue:115
msgid "This site is being updated. You will not be able to make any changes. Full access will be restored shortly."
msgstr "系统正在更新中，期间不可进行任何修改操作。完整访问权限将很快恢复。"

#: frontend/src/components/VideoBlock.vue:5
msgid "This video contains {0} {1}:"
msgstr ""

#. Option for the 'Day' (Select) field in DocType 'Evaluator Schedule'
#. Option for the 'Day' (Select) field in DocType 'LMS Certificate Request'
#: lms/lms/doctype/evaluator_schedule/evaluator_schedule.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
msgid "Thursday"
msgstr "星期四"

#. Label of the time (Time) field in DocType 'LMS Live Class'
#: frontend/src/components/Modals/Event.vue:48
#: frontend/src/components/Modals/LiveClassModal.vue:52
#: frontend/src/components/Quiz.vue:58
#: lms/lms/doctype/lms_live_class/lms_live_class.json
msgid "Time"
msgstr "时间"

#. Label of the time (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Time Preference"
msgstr "时间偏好"

#: frontend/src/components/VideoBlock.vue:140
msgid "Time for a Quiz"
msgstr ""

#: frontend/src/components/Modals/QuizInVideo.vue:13
msgid "Time in Video"
msgstr ""

#: frontend/src/components/Modals/QuizInVideo.vue:220
msgid "Time in Video (minutes)"
msgstr ""

#: frontend/src/components/Modals/QuizInVideo.vue:173
msgid "Time in video exceeds the total duration of the video."
msgstr ""

#: frontend/src/components/Modals/LiveClassModal.vue:44
msgid "Time must be in 24 hour format (HH:mm). Example 11:30 or 22:00"
msgstr "时间须为24小时制（HH:mm），例如11:30或22:00"

#. Label of the schedule_tab (Tab Break) field in DocType 'LMS Batch'
#. Label of the timetable (Table) field in DocType 'LMS Batch'
#. Label of the timetable (Table) field in DocType 'LMS Timetable Template'
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_timetable_template/lms_timetable_template.json
msgid "Timetable"
msgstr "课程表"

#. Label of the timetable_legends (Table) field in DocType 'LMS Batch'
#. Label of the timetable_legends (Table) field in DocType 'LMS Timetable
#. Template'
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_timetable_template/lms_timetable_template.json
msgid "Timetable Legends"
msgstr "课表图例"

#. Label of the timetable_template (Link) field in DocType 'LMS Batch'
#: lms/lms/doctype/lms_batch/lms_batch.json
msgid "Timetable Template"
msgstr "课表模板"

#. Label of the timezone (Data) field in DocType 'LMS Batch'
#. Label of the timezone (Data) field in DocType 'LMS Certificate Request'
#. Label of the timezone (Data) field in DocType 'LMS Live Class'
#: frontend/src/components/Modals/LiveClassModal.vue:59
#: frontend/src/pages/BatchForm.vue:107
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
#: lms/lms/doctype/lms_live_class/lms_live_class.json
msgid "Timezone"
msgstr "时区"

#: lms/templates/emails/batch_confirmation.html:21
#: lms/templates/emails/batch_start_reminder.html:16
#: lms/templates/emails/live_class_reminder.html:16
msgid "Timings:"
msgstr "时间安排："

#. Label of the title (Data) field in DocType 'Cohort'
#. Label of the title (Data) field in DocType 'Cohort Subgroup'
#. Label of the title (Data) field in DocType 'Cohort Web Page'
#. Label of the title (Data) field in DocType 'Course Chapter'
#. Label of the title (Data) field in DocType 'Course Lesson'
#. Label of the title (Data) field in DocType 'LMS Assignment'
#. Label of the title (Data) field in DocType 'LMS Badge'
#. Label of the title (Data) field in DocType 'LMS Batch'
#. Label of the title (Data) field in DocType 'LMS Batch Old'
#. Label of the title (Data) field in DocType 'LMS Course'
#. Label of the title (Data) field in DocType 'LMS Exercise'
#. Label of the title (Data) field in DocType 'LMS Live Class'
#. Label of the title (Data) field in DocType 'LMS Program'
#. Label of the title (Data) field in DocType 'LMS Programming Exercise'
#. Label of the title (Data) field in DocType 'LMS Quiz'
#. Label of the title (Data) field in DocType 'LMS Sidebar Item'
#. Label of the title (Data) field in DocType 'LMS Timetable Template'
#. Label of the title (Data) field in DocType 'Work Experience'
#: frontend/src/components/Modals/AssignmentForm.vue:20
#: frontend/src/components/Modals/DiscussionModal.vue:18
#: frontend/src/components/Modals/LiveClassModal.vue:23
#: frontend/src/pages/Assignments.vue:162 frontend/src/pages/BatchForm.vue:20
#: frontend/src/pages/CourseForm.vue:30 frontend/src/pages/JobForm.vue:20
#: frontend/src/pages/ProgramForm.vue:11
#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseForm.vue:17
#: frontend/src/pages/Programs.vue:101 frontend/src/pages/QuizForm.vue:56
#: frontend/src/pages/Quizzes.vue:115 frontend/src/pages/Quizzes.vue:229
#: lms/lms/doctype/cohort/cohort.json
#: lms/lms/doctype/cohort_subgroup/cohort_subgroup.json
#: lms/lms/doctype/cohort_web_page/cohort_web_page.json
#: lms/lms/doctype/course_chapter/course_chapter.json
#: lms/lms/doctype/course_lesson/course_lesson.json
#: lms/lms/doctype/lms_assignment/lms_assignment.json
#: lms/lms/doctype/lms_badge/lms_badge.json
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
#: lms/lms/doctype/lms_course/lms_course.json
#: lms/lms/doctype/lms_exercise/lms_exercise.json
#: lms/lms/doctype/lms_live_class/lms_live_class.json
#: lms/lms/doctype/lms_program/lms_program.json
#: lms/lms/doctype/lms_programming_exercise/lms_programming_exercise.json
#: lms/lms/doctype/lms_quiz/lms_quiz.json
#: lms/lms/doctype/lms_sidebar_item/lms_sidebar_item.json
#: lms/lms/doctype/lms_timetable_template/lms_timetable_template.json
#: lms/lms/doctype/work_experience/work_experience.json
msgid "Title"
msgstr "标题"

#: frontend/src/components/Modals/ChapterModal.vue:172
msgid "Title is required"
msgstr "标题为必填项"

#. Label of the unavailable_to (Date) field in DocType 'Course Evaluator'
#: frontend/src/pages/ProfileEvaluator.vue:112
#: lms/lms/doctype/course_evaluator/course_evaluator.json
msgid "To"
msgstr "至"

#. Label of the to_date (Date) field in DocType 'Work Experience'
#: lms/lms/doctype/work_experience/work_experience.json
msgid "To Date"
msgstr "截止日期"

#: lms/lms/utils.py:1829
msgid "To join this batch, please contact the Administrator."
msgstr "加入本批次请联系管理员"

#: lms/lms/user.py:41
msgid "Too many users signed up recently, so the registration is disabled. Please try back in an hour"
msgstr "近期注册用户过多，注册功能已临时关闭，请一小时后重试"

#: frontend/src/pages/Billing.vue:53
msgid "Total"
msgstr "总计"

#. Label of the total_marks (Int) field in DocType 'LMS Quiz'
#: frontend/src/pages/QuizForm.vue:73 frontend/src/pages/Quizzes.vue:235
#: lms/lms/doctype/lms_quiz/lms_quiz.json
msgid "Total Marks"
msgstr "总分"

#: lms/lms/web_template/lms_statistics/lms_statistics.html:14
#: lms/templates/statistics.html:12
msgid "Total Signups"
msgstr "总注册数"

#: frontend/src/components/Modals/FeedbackModal.vue:11
msgid "Training Feedback"
msgstr "培训反馈"

#. Option for the 'Location Preference' (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Travel"
msgstr "差旅"

#: frontend/src/components/Quiz.vue:284 lms/templates/quiz/quiz.html:131
msgid "Try Again"
msgstr "重试"

#. Option for the 'Day' (Select) field in DocType 'Evaluator Schedule'
#. Option for the 'Day' (Select) field in DocType 'LMS Certificate Request'
#: lms/lms/doctype/evaluator_schedule/evaluator_schedule.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
msgid "Tuesday"
msgstr "星期二"

#: frontend/src/pages/ProfileAbout.vue:86
msgid "Twitter"
msgstr "推特"

#. Label of the type (Select) field in DocType 'Job Opportunity'
#. Label of the type (Select) field in DocType 'LMS Assignment'
#. Label of the type (Select) field in DocType 'LMS Assignment Submission'
#. Label of the type (Select) field in DocType 'LMS Question'
#. Label of the type (Select) field in DocType 'LMS Quiz Question'
#: frontend/src/components/Modals/AssessmentModal.vue:22
#: frontend/src/components/Modals/Question.vue:44
#: frontend/src/pages/Assignments.vue:40 frontend/src/pages/Assignments.vue:167
#: frontend/src/pages/JobForm.vue:25 frontend/src/pages/Jobs.vue:65
#: frontend/src/pages/ProgrammingExercises/ProgrammingExercises.vue:53
#: lms/job/doctype/job_opportunity/job_opportunity.json
#: lms/lms/doctype/lms_assignment/lms_assignment.json
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
#: lms/lms/doctype/lms_question/lms_question.json
#: lms/lms/doctype/lms_quiz_question/lms_quiz_question.json
#: lms/templates/assessments.html:14
msgid "Type"
msgstr "类型"

#: frontend/src/utils/markdownParser.js:11
msgid "Type '/' for commands or select text to format"
msgstr ""

#: frontend/src/components/Quiz.vue:646
msgid "Type your answer"
msgstr "输入答案"

#. Option for the 'Grade Type' (Select) field in DocType 'Education Detail'
#: lms/lms/doctype/education_detail/education_detail.json
msgid "UK Grading  (e.g. 1st, 2:2)"
msgstr "英国评分制（如一等、二等一级）"

#. Option for the 'Type' (Select) field in DocType 'LMS Assignment'
#. Option for the 'Type' (Select) field in DocType 'LMS Assignment Submission'
#: lms/lms/doctype/lms_assignment/lms_assignment.json
#: lms/lms/doctype/lms_assignment_submission/lms_assignment_submission.json
msgid "URL"
msgstr "链接"

#. Label of the uuid (Data) field in DocType 'LMS Live Class'
#: lms/lms/doctype/lms_live_class/lms_live_class.json
msgid "UUID"
msgstr "通用唯一标识符"

#. Label of the unavailability_section (Section Break) field in DocType 'Course
#. Evaluator'
#: lms/lms/doctype/course_evaluator/course_evaluator.json
msgid "Unavailability"
msgstr "不可用状态"

#: frontend/src/pages/ProfileEvaluator.vue:259
msgid "Unavailability updated successfully"
msgstr "不可用状态更新成功"

#: lms/lms/doctype/course_evaluator/course_evaluator.py:29
msgid "Unavailable From Date cannot be greater than Unavailable To Date"
msgstr "不可用开始日期不可晚于结束日期"

#. Option for the 'Status' (Select) field in DocType 'LMS Course'
#: lms/lms/doctype/lms_course/lms_course.json
msgid "Under Review"
msgstr "审核中"

#. Option for the 'Visibility' (Select) field in DocType 'LMS Batch Old'
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
msgid "Unlisted"
msgstr "未列出"

#: frontend/src/pages/Batches.vue:284 frontend/src/pages/Courses.vue:322
msgid "Unpublished"
msgstr "未发布"

#: frontend/src/components/Modals/EditCoverImage.vue:60
#: frontend/src/components/UnsplashImageBrowser.vue:54
msgid "Unsplash"
msgstr "Unsplash图库"

#. Label of the unsplash_access_key (Data) field in DocType 'LMS Settings'
#: lms/lms/doctype/lms_settings/lms_settings.json
msgid "Unsplash Access Key"
msgstr "Unsplash访问密钥"

#. Option for the 'Role Preference' (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Unstructured Role"
msgstr "非结构化角色"

#. Option for the 'Status' (Select) field in DocType 'Cohort'
#. Option for the 'Status' (Select) field in DocType 'LMS Certificate Request'
#. Label of the upcoming (Check) field in DocType 'LMS Course'
#: frontend/src/pages/Batches.vue:282 frontend/src/pages/CourseForm.vue:162
#: frontend/src/pages/Courses.vue:313 lms/lms/doctype/cohort/cohort.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
#: lms/lms/doctype/lms_course/lms_course.json
msgid "Upcoming"
msgstr "即将到来"

#: frontend/src/pages/Batch.vue:187
msgid "Upcoming Batches"
msgstr "即将开课批次"

#: frontend/src/components/UpcomingEvaluations.vue:5
#: lms/templates/upcoming_evals.html:3
msgid "Upcoming Evaluations"
msgstr "即将进行的评估"

#: frontend/src/components/Settings/BrandSettings.vue:24
#: frontend/src/components/Settings/PaymentSettings.vue:27
#: frontend/src/components/Settings/SettingDetails.vue:23
msgid "Update"
msgstr "更新"

#: lms/templates/emails/community_course_membership.html:11
msgid "Update Password"
msgstr "更新密码"

#: frontend/src/pages/BatchForm.vue:217 frontend/src/pages/CourseForm.vue:108
msgid "Upload"
msgstr "上传"

#: frontend/src/components/Assignment.vue:81
msgid "Upload File"
msgstr "上传文件"

#: frontend/src/components/Assignment.vue:80
msgid "Uploading {0}%"
msgstr "上传进度{0}%"

#: frontend/src/components/Modals/EmailTemplateModal.vue:38
msgid "Use HTML"
msgstr "使用HTML"

#. Label of the user (Link) field in DocType 'LMS Job Application'
#. Label of the email (Link) field in DocType 'Cohort Staff'
#. Label of the user (Link) field in DocType 'LMS Course Interest'
#: lms/job/doctype/lms_job_application/lms_job_application.json
#: lms/lms/doctype/cohort_staff/cohort_staff.json
#: lms/lms/doctype/lms_course_interest/lms_course_interest.json
msgid "User"
msgstr "用户"

#. Label of the user_category (Select) field in DocType 'User'
#: lms/fixtures/custom_field.json lms/templates/signup-form.html:17
msgid "User Category"
msgstr "用户类别"

#. Label of the user_field (Select) field in DocType 'LMS Badge'
#: lms/lms/doctype/lms_badge/lms_badge.json
msgid "User Field"
msgstr "用户字段"

#. Label of the user_image (Attach Image) field in DocType 'Course Evaluator'
#: lms/lms/doctype/course_evaluator/course_evaluator.json
msgid "User Image"
msgstr "用户头像"

#. Option for the 'Type' (Select) field in DocType 'LMS Question'
#. Option for the 'Type' (Select) field in DocType 'LMS Quiz Question'
#: lms/lms/doctype/lms_question/lms_question.json
#: lms/lms/doctype/lms_quiz_question/lms_quiz_question.json
msgid "User Input"
msgstr "用户输入"

#. Name of a DocType
#: lms/lms/doctype/user_skill/user_skill.json
msgid "User Skill"
msgstr "用户技能"

#: lms/job/doctype/job_opportunity/job_opportunity.py:40
msgid "User {0} has reported the job post {1}"
msgstr "用户{0}已举报职位发布{1}"

#. Label of the username (Data) field in DocType 'Course Evaluator'
#. Label of the username (Data) field in DocType 'Invite Request'
#: lms/lms/doctype/course_evaluator/course_evaluator.json
#: lms/lms/doctype/invite_request/invite_request.json
msgid "Username"
msgstr "用户名"

#. Label of a shortcut in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "Users"
msgstr "用户"

#. Label of the answer (Small Text) field in DocType 'LMS Quiz Result'
#: lms/lms/doctype/lms_quiz_result/lms_quiz_result.json
msgid "Users Response"
msgstr "用户响应"

#: lms/templates/signup-form.html:83
msgid "Valid email and name required"
msgstr "需提供有效的电子邮件和姓名"

#. Label of the value (Rating) field in DocType 'LMS Batch Feedback'
#: lms/lms/doctype/lms_batch_feedback/lms_batch_feedback.json
msgid "Value"
msgstr "值"

#. Option for the 'Event' (Select) field in DocType 'LMS Badge'
#: lms/lms/doctype/lms_badge/lms_badge.json
msgid "Value Change"
msgstr "数值变更"

#. Label of the video_link (Data) field in DocType 'LMS Course'
#: lms/lms/doctype/lms_course/lms_course.json
msgid "Video Embed Link"
msgstr "视频嵌入链接"

#: frontend/src/pages/Lesson.vue:19
msgid "Video Statistics"
msgstr ""

#: frontend/src/components/Modals/VideoStatistics.vue:6
msgid "Video Statistics for {0}"
msgstr ""

#: frontend/src/pages/Notifications.vue:39
msgid "View"
msgstr "查看"

#: frontend/src/components/CertificationLinks.vue:10
#: frontend/src/components/Modals/Event.vue:67
msgid "View Certificate"
msgstr "查看证书"

#: frontend/src/components/BatchFeedback.vue:56
msgid "View all feedback"
msgstr ""

#. Label of the visibility (Select) field in DocType 'LMS Batch Old'
#: lms/lms/doctype/lms_batch_old/lms_batch_old.json
msgid "Visibility"
msgstr "可见性"

#: frontend/src/components/BatchOverlay.vue:69
msgid "Visit Batch"
msgstr "访问批次"

#: frontend/src/pages/JobDetail.vue:41
msgid "Visit Website"
msgstr "访问网站"

#: lms/templates/emails/batch_confirmation.html:25
msgid "Visit the following link to view your "
msgstr "访问以下链接查看您的"

#: lms/templates/emails/batch_start_reminder.html:23
#: lms/templates/emails/live_class_reminder.html:20
msgid "Visit your batch"
msgstr "访问您的批次"

#. Label of the internship (Table) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Volunteering or Internship"
msgstr "志愿者/实习经历"

#. Label of the watch_time (Data) field in DocType 'LMS Video Watch Duration'
#: frontend/src/components/Modals/VideoStatistics.vue:25
#: lms/lms/doctype/lms_video_watch_duration/lms_video_watch_duration.json
msgid "Watch Time"
msgstr ""

#: lms/templates/emails/batch_confirmation.html:6
msgid "We are pleased to inform you that you have been enrolled in our upcoming batch. Congratulations!"
msgstr "很高兴通知您已成功注册即将开课的批次！祝贺！"

#: lms/templates/emails/payment_reminder.html:7
msgid "We have a limited number of seats, and they won't be available for long!"
msgstr "席位有限，即将售罄！"

#: lms/templates/emails/payment_reminder.html:4
msgid "We noticed that you started enrolling in the"
msgstr "我们注意到您已开始注册"

#. Label of the web_page (Link) field in DocType 'LMS Sidebar Item'
#: frontend/src/components/Modals/PageModal.vue:23
#: lms/lms/doctype/lms_sidebar_item/lms_sidebar_item.json
msgid "Web Page"
msgstr "网页"

#: frontend/src/components/Modals/PageModal.vue:80
msgid "Web page added to sidebar"
msgstr "网页已添加至侧边栏"

#. Option for the 'Day' (Select) field in DocType 'Evaluator Schedule'
#. Option for the 'Day' (Select) field in DocType 'LMS Certificate Request'
#: lms/lms/doctype/evaluator_schedule/evaluator_schedule.json
#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.json
msgid "Wednesday"
msgstr "星期三"

#: lms/lms/doctype/invite_request/invite_request.py:40
#: lms/templates/emails/lms_invite_request_approved.html:4
msgid "Welcome to {0}!"
msgstr "欢迎使用{0}！"

#: frontend/src/pages/PersonaForm.vue:32
msgid "What best describes your role?"
msgstr "请选择最能描述您角色的选项"

#: frontend/src/components/LessonHelp.vue:6
msgid "What does include in preview mean?"
msgstr "「包含在预览中」的含义是什么？"

#: frontend/src/pages/PersonaForm.vue:21
msgid "What is your use case for Frappe Learning?"
msgstr "您使用Frappe Learning的主要场景是？"

#: lms/templates/courses_under_review.html:15
msgid "When a course gets submitted for review, it will be listed here."
msgstr "提交审核的课程将在此列出"

#: frontend/src/pages/Billing.vue:106
msgid "Where did you hear about us?"
msgstr "您通过何种渠道了解到我们？"

#: lms/templates/emails/certification.html:10
msgid "With this certification, you can now showcase your updated skills and share your achievement with your colleagues and on LinkedIn. To access your certificate, please click on the link provided below. Make sure you are logged in to the portal."
msgstr "获得此认证后，您可向同事及领英展示最新技能。点击下方链接查看证书（需登录系统）"

#. Option for the 'Status' (Select) field in DocType 'LMS Mentor Request'
#: lms/lms/doctype/lms_mentor_request/lms_mentor_request.json
msgid "Withdrawn"
msgstr "已撤回"

#. Label of the work_environment (Section Break) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Work Environment"
msgstr "工作环境"

#. Label of the work_experience (Table) field in DocType 'User'
#. Name of a DocType
#: lms/fixtures/custom_field.json
#: lms/lms/doctype/work_experience/work_experience.json
msgid "Work Experience"
msgstr "工作经验"

#. Label of the work_experience_details (Section Break) field in DocType 'User'
#: lms/fixtures/custom_field.json
msgid "Work Experience Details"
msgstr "工作经验详情"

#: frontend/src/components/CourseReviews.vue:8
#: frontend/src/components/Modals/ReviewModal.vue:5
#: lms/templates/reviews.html:117
msgid "Write a Review"
msgstr "撰写评价"

#: lms/templates/reviews.html:31 lms/templates/reviews.html:103
#: lms/templates/reviews_cta.html:3 lms/templates/reviews_cta.html:7
msgid "Write a review"
msgstr "撰写评价"

#: frontend/src/components/Assignment.vue:123
msgid "Write your answer here"
msgstr "在此输入答案"

#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.py:95
msgid "You already have an evaluation on {0} at {1} for the course {2}."
msgstr "您已在{0} {1}为课程{2}安排评估"

#: frontend/src/pages/CourseCertification.vue:14
msgid "You are already certified for this course. Click on the card below to open your certificate."
msgstr "您已完成本课程认证，点击下方卡片查看证书"

#: lms/lms/api.py:234
msgid "You are already enrolled for this batch."
msgstr "您已注册本批次"

#: lms/lms/api.py:226
msgid "You are already enrolled for this course."
msgstr "您已注册本课程"

#: frontend/src/pages/Batch.vue:169
msgid "You are not a member of this batch. Please checkout our upcoming batches."
msgstr "您不属于本批次成员，请查看即将开课批次"

#: lms/lms/doctype/lms_batch_old/lms_batch_old.py:20
msgid "You are not a mentor of the course {0}"
msgstr "您不是课程{0}的导师"

#: frontend/src/pages/SCORMChapter.vue:22
msgid "You are not enrolled in this course. Please enroll to access this lesson."
msgstr "请注册本课程以访问此课时"

#: lms/templates/emails/lms_course_interest.html:13
#: lms/templates/emails/lms_invite_request_approved.html:11
msgid "You can also copy-paste following link in your browser"
msgstr "您也可将以下链接复制到浏览器"

#: lms/templates/quiz/quiz.html:18
msgid "You can attempt this quiz only {0} {1}"
msgstr "您只能尝试此测验{0}次{1}"

#: frontend/src/components/Quiz.vue:37
msgid "You can attempt this quiz {0}."
msgstr "您可尝试此测验{0}次"

#: lms/templates/emails/job_application.html:6
msgid "You can find their resume attached to this email."
msgstr "简历已作为附件发送至本邮箱"

#: frontend/src/pages/ProfileEvaluator.vue:14
msgid "You cannot change the availability when the site is being updated."
msgstr "系统更新期间不可修改可用性设置。"

#: frontend/src/pages/ProfileRoles.vue:12
msgid "You cannot change the roles in read-only mode."
msgstr "只读模式下不可修改角色。"

#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.py:115
msgid "You cannot schedule evaluations after {0}."
msgstr "您不能在{0}之后安排评估"

#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.py:104
msgid "You cannot schedule evaluations for past slots."
msgstr "不能为过去的时段安排评估"

#: frontend/src/components/NoPermission.vue:11
msgid "You do not have permission to access this page."
msgstr "您无权访问此页面"

#: lms/templates/notifications.html:27
msgid "You don't have any notifications."
msgstr "暂无通知"

#: lms/templates/quiz/quiz.js:137
msgid "You got"
msgstr "您获得"

#: frontend/src/components/Quiz.vue:265
#, python-format
msgid "You got {0}% correct answers with a score of {1} out of {2}"
msgstr "您答对{0}%的题目，得分{1}（总分{2}）"

#: lms/templates/emails/live_class_reminder.html:6
msgid "You have a live class scheduled tomorrow. Please be prepared and be on time for the session."
msgstr "您已预约明日直播课程，请准时参加"

#: lms/job/doctype/lms_job_application/lms_job_application.py:22
msgid "You have already applied for this job."
msgstr "您已申请该职位"

#: frontend/src/components/Quiz.vue:96 lms/templates/quiz/quiz.html:43
msgid "You have already exceeded the maximum number of attempts allowed for this quiz."
msgstr "您已超过本测验允许的最大尝试次数"

#: lms/lms/api.py:258
msgid "You have already purchased the certificate for this course."
msgstr "您已购买本课程证书"

#: lms/lms/doctype/lms_course_review/lms_course_review.py:17
msgid "You have already reviewed this course"
msgstr "您已评价过本课程"

#: frontend/src/pages/JobDetail.vue:57
msgid "You have applied"
msgstr "您已申请"

#: frontend/src/components/BatchOverlay.vue:159
msgid "You have been enrolled in this batch"
msgstr "您已加入本批次"

#: frontend/src/components/CourseCardOverlay.vue:229
msgid "You have been enrolled in this course"
msgstr "您已注册本课程"

#: lms/lms/doctype/lms_quiz_submission/lms_quiz_submission.py:30
msgid "You have exceeded the maximum number of attempts ({0}) for this quiz"
msgstr "您已超过本测验最大尝试次数（{0}次）"

#: lms/lms/doctype/lms_quiz_submission/lms_quiz_submission.py:56
msgid "You have got a score of {0} for the quiz {1}"
msgstr "您在测验{1}中获得了{0}分"

#: lms/lms/widgets/NoPreviewModal.html:12
msgid "You have opted to be notified for this course. You will receive an email when the course becomes available."
msgstr "您已订阅本课程通知，课程开放时将收到邮件提醒"

#: frontend/src/components/CourseCardOverlay.vue:217
msgid "You need to login first to enroll for this course"
msgstr "请先登录以注册本课程"

#: frontend/src/components/Quiz.vue:7
msgid "You will have to complete the quiz to continue the video"
msgstr ""

#: frontend/src/components/Quiz.vue:30 lms/templates/quiz/quiz.html:11
#, python-format
msgid "You will have to get {0}% correct answers in order to pass the quiz."
msgstr "需答对{0}%的题目以通过测验"

#: lms/templates/emails/mentor_request_creation_email.html:4
msgid "You've applied to become a mentor for this course. Your request is currently under review."
msgstr "您已申请成为本课程导师，请求正在审核中"

#: frontend/src/components/Assignment.vue:58
msgid "You've successfully submitted the assignment."
msgstr "作业提交成功"

#. Label of the youtube (Data) field in DocType 'Course Lesson'
#: lms/lms/doctype/course_lesson/course_lesson.json
msgid "YouTube Video URL"
msgstr "YouTube视频链接"

#. Description of the 'YouTube Video URL' (Data) field in DocType 'Course
#. Lesson'
#: lms/lms/doctype/course_lesson/course_lesson.json
msgid "YouTube Video will appear at the top of the lesson."
msgstr "YouTube视频将显示在课时顶部"

#: lms/www/new-sign-up.html:56
msgid "Your Account has been successfully created!"
msgstr "账户创建成功！"

#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseSubmission.vue:119
msgid "Your Output"
msgstr ""

#: lms/lms/doctype/lms_batch/lms_batch.py:362
msgid "Your batch {0} is starting tomorrow"
msgstr "您的批次{0}将于明日开始"

#: frontend/src/pages/ProfileEvaluator.vue:134
msgid "Your calendar is set."
msgstr "日历已设置"

#: lms/lms/doctype/lms_live_class/lms_live_class.py:90
msgid "Your class on {0} is today"
msgstr "您的{0}课程今天开始"

#: frontend/src/components/Modals/EmailTemplateModal.vue:35
msgid "Your enrollment in {{ batch_name }} is confirmed"
msgstr ""

#: lms/lms/notification/certificate_request_reminder/certificate_request_reminder.html:3
#: lms/templates/emails/certificate_request_notification.html:3
msgid "Your evaluation for the course {0} has been scheduled on {1} at {2} {3}."
msgstr "您的课程{0}评估已安排在{1} {2} {3}"

#: lms/lms/doctype/lms_certificate_request/lms_certificate_request.py:125
msgid "Your evaluation slot has been booked"
msgstr "评估时段已预约成功"

#: lms/templates/emails/certificate_request_notification.html:5
msgid "Your evaluator is {0}"
msgstr "您的评估人是{0}"

#: lms/templates/emails/mentor_request_status_update_email.html:4
msgid "Your request to join us as a mentor for the course"
msgstr "您申请成为课程导师的请求"

#: lms/templates/quiz/quiz.js:140
msgid "Your score is"
msgstr "您的得分是"

#: frontend/src/components/Quiz.vue:258
msgid "Your submission has been successfully saved. The instructor will review and grade it shortly, and you'll be notified of your final result."
msgstr "提交内容已保存，讲师将尽快评阅并通知最终结果"

#: frontend/src/pages/Lesson.vue:8
msgid "Zen Mode"
msgstr "禅模式"

#. Label of the zoom_account (Link) field in DocType 'LMS Batch'
#. Label of the zoom_account (Link) field in DocType 'LMS Live Class'
#: frontend/src/pages/BatchForm.vue:164
#: lms/lms/doctype/lms_batch/lms_batch.json
#: lms/lms/doctype/lms_live_class/lms_live_class.json
msgid "Zoom Account"
msgstr ""

#: frontend/src/components/Modals/ZoomAccountModal.vue:158
msgid "Zoom Account created successfully"
msgstr ""

#: frontend/src/components/Modals/ZoomAccountModal.vue:195
msgid "Zoom Account updated successfully"
msgstr ""

#. Name of a DocType
#: lms/lms/doctype/zoom_settings/zoom_settings.json
msgid "Zoom Settings"
msgstr "Zoom设置"

#: frontend/src/components/StudentHeatmap.vue:6
msgid "activities"
msgstr "活动"

#: frontend/src/components/StudentHeatmap.vue:6
msgid "activity"
msgstr "活动"

#: frontend/src/components/JobCard.vue:26 frontend/src/pages/JobDetail.vue:108
msgid "applicant"
msgstr "申请人"

#: frontend/src/components/JobCard.vue:26 frontend/src/pages/JobDetail.vue:108
msgid "applicants"
msgstr "申请人列表"

#: frontend/src/components/VideoBlock.vue:15
msgid "at {0} minutes"
msgstr ""

#: lms/templates/emails/payment_reminder.html:4
msgid "but didn’t complete your payment"
msgstr "但未完成支付"

#: lms/templates/emails/mentor_request_creation_email.html:5
msgid "cancel your application"
msgstr "取消申请"

#: frontend/src/pages/CertifiedParticipants.vue:79
msgid "certificate"
msgstr "证书"

#: frontend/src/pages/CertifiedParticipants.vue:78
msgid "certificates"
msgstr "证书"

#: frontend/src/pages/CertifiedParticipants.vue:18
msgid "certified members"
msgstr "认证成员"

#: frontend/src/pages/Lesson.vue:98 frontend/src/pages/Lesson.vue:234
msgid "completed"
msgstr "已完成"

#: lms/templates/quiz/quiz.js:137
msgid "correct answers"
msgstr "正确答案"

#: lms/templates/emails/mentor_request_status_update_email.html:4
msgid "has been"
msgstr "已"

#: frontend/src/components/StudentHeatmap.vue:8
msgid "in the last"
msgstr "在过去"

#: lms/templates/signup-form.html:12
msgid "<EMAIL>"
msgstr "示例@邮箱.com"

#: frontend/src/pages/Programs.vue:31
msgid "member"
msgstr "成员"

#: frontend/src/pages/Programs.vue:31
msgid "members"
msgstr "成员"

#: frontend/src/components/Modals/LiveClassAttendance.vue:57
msgid "minutes"
msgstr "分钟数"

#: lms/templates/quiz/quiz.html:106
msgid "of"
msgstr "/"

#: lms/templates/quiz/quiz.js:141
msgid "out of"
msgstr "超出"

#: frontend/src/pages/QuizForm.vue:344
msgid "question_detail"
msgstr "问题详情"

#: lms/templates/reviews.html:25
msgid "ratings"
msgstr "评分"

#: frontend/src/components/Settings/Categories.vue:19
msgid "saving..."
msgstr "保存中..."

#: lms/templates/reviews.html:43
msgid "stars"
msgstr "星级"

#: frontend/src/components/BatchFeedback.vue:12
msgid "to view your feedback."
msgstr ""

#: frontend/src/components/StudentHeatmap.vue:10
msgid "weeks"
msgstr "周"

#: lms/templates/emails/mentor_request_creation_email.html:5
msgid "you can"
msgstr "您可以"

#: frontend/src/pages/Assignments.vue:26
msgid "{0} Assignments"
msgstr "{0}项作业"

#: frontend/src/pages/ProgrammingExercises/ProgrammingExercises.vue:39
msgid "{0} Exercises"
msgstr ""

#: frontend/src/components/Modals/CourseProgressSummary.vue:14
msgid "{0} Members"
msgstr ""

#: frontend/src/pages/Jobs.vue:32
msgid "{0} Open Jobs"
msgstr "{0}个开放职位"

#: frontend/src/pages/Quizzes.vue:18
msgid "{0} Quizzes"
msgstr "{0}项测验"

#: lms/lms/api.py:865 lms/lms/api.py:873
msgid "{0} Settings not found"
msgstr "未找到{0}设置"

#: frontend/src/pages/ProgrammingExercises/ProgrammingExerciseSubmissions.vue:12
msgid "{0} Submissions"
msgstr ""

#: lms/templates/emails/job_application.html:2
msgid "{0} has applied for the job position {1}"
msgstr "{0}已申请职位{1}"

#: lms/templates/emails/job_report.html:4
msgid "{0} has reported a job post for the following reason."
msgstr "{0}举报了职位发布，原因如下："

#: lms/templates/emails/assignment_submission.html:2
msgid "{0} has submitted the assignment {1}"
msgstr "{0}已提交作业{1}"

#: lms/lms/doctype/lms_enrollment/lms_enrollment.py:57
msgid "{0} is already a Student of {1} course through {2} batch"
msgstr "{0}已通过批次{2}注册课程{1}"

#: lms/lms/doctype/lms_course_mentor_mapping/lms_course_mentor_mapping.py:16
msgid "{0} is already a mentor for course {1}"
msgstr "{0}已是课程{1}的导师"

#: lms/lms/doctype/lms_enrollment/lms_enrollment.py:30
msgid "{0} is already a {1} of the course {2}"
msgstr "{0}已是课程{2}的{1}"

#: lms/lms/doctype/lms_certificate/lms_certificate.py:91
msgid "{0} is already certified for the batch {1}"
msgstr "{0}已获得批次{1}的认证"

#: lms/lms/doctype/lms_certificate/lms_certificate.py:72
msgid "{0} is already certified for the course {1}"
msgstr "{0}已获得课程{1}的认证"

#: lms/lms/notification/certificate_request_reminder/certificate_request_reminder.html:5
msgid "{0} is your evaluator"
msgstr "{0}是您的评估人"

#: lms/lms/utils.py:686
msgid "{0} mentioned you in a comment"
msgstr "{0}在评论中提及您"

#: lms/templates/emails/mention_template.html:2
msgid "{0} mentioned you in a comment in your batch."
msgstr "{0}在您的批次评论中提及您"

#: lms/lms/utils.py:639 lms/lms/utils.py:645
msgid "{0} mentioned you in a comment in {1}"
msgstr "{0}在{1}的评论中提及您"

#: lms/lms/utils.py:462
msgid "{0}k"
msgstr "{0}千"

#. Count format of shortcut in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "{} Active"
msgstr "{} 活跃"

#. Count format of shortcut in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "{} Completed"
msgstr "{} 完成"

#. Count format of shortcut in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "{} Enrolled"
msgstr "{} 注册"

#. Count format of shortcut in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "{} Granted"
msgstr "{} 授予"

#. Count format of shortcut in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "{} Passed"
msgstr "{} 通过"

#. Count format of shortcut in the LMS Workspace
#: lms/lms/workspace/lms/lms.json
msgid "{} Published"
msgstr "{} 已发布"

