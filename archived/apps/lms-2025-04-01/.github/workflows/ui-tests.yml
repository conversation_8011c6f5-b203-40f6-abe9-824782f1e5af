name: UI

on:
  pull_request:
  workflow_dispatch:
  push:
    branches: [ main ]

permissions:
  # Do not change this as GITHUB_TOKEN is being used by roulette
  contents: read

jobs:
  
  test:
    runs-on: ubuntu-latest
    if: ${{ github.repository_owner == 'frappe' }}
    timeout-minutes: 60

    strategy:
      fail-fast: false

    name: UI Tests (Cypress)

    services:
      mariadb:
        image: mariadb:10.8
        env:
          MARIADB_ROOT_PASSWORD: 123
        ports:
          - 3306:3306
        options: --health-cmd="mysqladmin ping" --health-interval=5s --health-timeout=2s --health-retries=3

    steps:
      - name: <PERSON><PERSON>
        uses: actions/checkout@v3

      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'

      - name: Check for valid Python & Merge Conflicts
        run: |
          python -m compileall -q -f "${GITHUB_WORKSPACE}"
          if grep -lr --exclude-dir=node_modules "^<<<<<<< " "${GITHUB_WORKSPACE}"
              then echo "Found merge conflicts"
              exit 1
          fi

      - uses: actions/setup-node@v3
        with:
          node-version: 18
          check-latest: true

      - name: Add to Hosts
        run: |
          echo "127.0.0.1 lms.test" | sudo tee -a /etc/hosts

      - name: Cache pip
        uses: actions/cache@v4
        with:
          path: ~/.cache/pip
          key: ${{ runner.os }}-pip-${{ hashFiles('**/*requirements.txt', '**/pyproject.toml', '**/setup.py') }}
          restore-keys: |
            ${{ runner.os }}-pip-
            ${{ runner.os }}-

      - name: Get yarn cache directory path
        id: yarn-cache-dir-path
        run: echo "dir=$(yarn cache dir)" >> $GITHUB_OUTPUT

      - uses: actions/cache@v4
        id: yarn-cache
        with:
          path: ${{ steps.yarn-cache-dir-path.outputs.dir }}
          key: ${{ runner.os }}-yarn-ui-${{ hashFiles('**/yarn.lock') }}
          restore-keys: |
            ${{ runner.os }}-yarn-ui-

      - name: Cache cypress binary
        uses: actions/cache@v4
        with:
          path: ~/.cache/Cypress
          key: ${{ runner.os }}-cypress

      - name: Install Dependencies
        run: |
          bash ${GITHUB_WORKSPACE}/.github/helper/install_dependencies.sh
          bash ${GITHUB_WORKSPACE}/.github/helper/install.sh
        env:
          BEFORE: ${{ env.GITHUB_EVENT_PATH.before }}
          AFTER: ${{ env.GITHUB_EVENT_PATH.after }}
          TYPE: ui
          DB: mariadb

      - name: Site Setup
        run: |
          cd ~/frappe-bench/
          bench --site lms.test execute frappe.utils.install.complete_setup_wizard
          bench --site lms.test execute frappe.tests.ui_test_helpers.create_test_user
          bench --site lms.test set-password <EMAIL> admin
          bench --site lms.test execute lms.lms.utils.persona_captured

      - name: cypress pre-requisites
        run: |
          cd ~/frappe-bench/apps/lms
          yarn add cypress@^10 --no-lockfile -W

      - name: UI Tests
        run: cd ~/frappe-bench/ && bench --site lms.test run-ui-tests lms --headless
        env:
          CYPRESS_BASE_URL: http://lms.test:8000
          CYPRESS_RECORD_KEY: 095366ec-7b9f-41bd-aeec-03bb76d627fe

      - name: Stop server and wait for coverage file
        run: |
          ps -ef | grep "[f]rappe serve" | awk '{print $2}' | xargs kill -s SIGINT
          sleep 5

      - name: Show bench output
        if: ${{ always() }}
        run: cat ~/frappe-bench/bench_start.log || true