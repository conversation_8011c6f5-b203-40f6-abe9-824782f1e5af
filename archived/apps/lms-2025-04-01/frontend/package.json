{"name": "frappe-ui-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "serve": "vite preview", "build": "vite build --base=/assets/lms/frontend/ && yarn copy-html-entry", "copy-html-entry": "cp ../lms/public/frontend/index.html ../lms/www/lms.html"}, "dependencies": {"@codemirror/lang-html": "^6.4.9", "@codemirror/lang-javascript": "^6.2.4", "@codemirror/lang-json": "^6.0.1", "@codemirror/lang-python": "^6.2.1", "@editorjs/checklist": "^1.6.0", "@editorjs/code": "^2.9.0", "@editorjs/editorjs": "^2.29.0", "@editorjs/embed": "^2.7.0", "@editorjs/header": "^2.8.1", "@editorjs/inline-code": "^1.5.0", "@editorjs/nested-list": "^1.4.2", "@editorjs/paragraph": "^2.11.3", "@editorjs/simple-image": "^1.6.0", "@editorjs/table": "^2.4.2", "@vueuse/router": "^12.7.0", "ace-builds": "^1.36.2", "apexcharts": "^4.3.0", "chart.js": "^4.4.1", "codemirror": "^6.0.1", "dayjs": "^1.11.6", "feather-icons": "^4.28.0", "frappe-ui": "^0.1.163", "highlight.js": "^11.11.1", "lucide-vue-next": "^0.383.0", "markdown-it": "^14.0.0", "pinia": "^2.0.33", "plyr": "^3.7.8", "socket.io-client": "^4.7.2", "tailwindcss": "3.4.15", "thememirror": "^2.0.1", "typescript": "^5.7.2", "vue": "^3.4.23", "vue-chartjs": "^5.3.0", "vue-codemirror": "^6.1.1", "vue-draggable-next": "^2.2.1", "vue-router": "^4.0.12", "vue3-apexcharts": "^1.8.0", "vuedraggable": "4.1.0"}, "devDependencies": {"@vitejs/plugin-vue": "^5.0.3", "autoprefixer": "^10.4.2", "postcss": "^8.4.5", "vite": "^5.0.11"}}